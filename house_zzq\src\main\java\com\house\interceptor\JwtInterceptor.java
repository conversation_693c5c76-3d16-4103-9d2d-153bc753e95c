package com.house.interceptor;

import com.house.utils.JwtUtil;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.ExpiredJwtException;
import io.jsonwebtoken.MalformedJwtException;
import io.jsonwebtoken.security.SignatureException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * JWT拦截器
 * 用于验证请求中的token是否有效
 */
@Slf4j
@Component
public class JwtInterceptor implements HandlerInterceptor {

    @Autowired
    private JwtUtil jwtUtil;

    // 不需要验证token的URL路径
    private static final String[] EXCLUDE_URLS = {
        "/user/login",
        "/user/register",
        "/api/images/",
        "/static/",
        "/favicon.ico",
        "/error",
        "/house/gethothouses",      // 获取热门房源（首页公开接口）
        "/house/getrecommendhouses", // 获取推荐房源（首页公开接口）
        "/house/getallhouselist"    // 获取所有房源列表（公开接口）
    };

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 记录当前处理的请求路径，便于调试
        String requestURI = request.getRequestURI();
        
        // 检查是否是OPTIONS请求（预检请求）
        if ("OPTIONS".equalsIgnoreCase(request.getMethod())) {
            return true;
        }

        // 检查URL是否需要验证token
        if (isExcludeUrl(requestURI)) {
            return true;
        }

        // 从请求头中获取token
        String token = request.getHeader("Authorization");
        
        // 如果请求头中没有token，尝试从请求参数中获取
        if (token == null || token.isEmpty()) {
            token = request.getParameter("token");
        }
        
        // 如果token以Bearer 开头，去掉Bearer 前缀
        if (token != null && token.startsWith("Bearer ")) {
            token = token.substring(7);
        }
        
        // 如果token为空，返回未授权错误
        if (token == null || token.isEmpty()) {
            log.warn("请求未携带token: {}", requestURI);
            handleUnauthorized(response, "未授权：请求未携带token");
            return false;
        }
        
        try {
            // 解析token
            Claims claims = jwtUtil.parseJWT(token);
            
            // 检查token是否过期
            Date expiration = claims.getExpiration();
            Date now = new Date();
            
            if (expiration != null) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                
                // 只在重要路径或每小时记录一次token信息
                if (isImportantPath(requestURI) || shouldLogToken()) {
                    log.info("Token信息 - 用户: {}, 角色: {}, 过期时间: {}, 剩余: {}秒", 
                            claims.getSubject(), 
                            claims.get("roles"),
                            sdf.format(expiration),
                            (expiration.getTime() - now.getTime()) / 1000);
                }
                
                if (expiration.before(now)) {
                    log.warn("Token已过期 - 请求路径: {}, 过期时间: {}", 
                            requestURI, sdf.format(expiration));
                    handleUnauthorized(response, "未授权：token已过期");
                    return false;
                }
            }
            
            // token有效，将解析出的用户信息存入请求属性中，方便后续使用
            request.setAttribute("claims", claims);
            request.setAttribute("userId", claims.getId());
            request.setAttribute("username", claims.getSubject());
            request.setAttribute("roles", claims.get("roles"));
            
            return true;
        } catch (ExpiredJwtException e) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            log.warn("Token已过期 - 请求路径: {}, 过期时间: {}", 
                    requestURI, sdf.format(e.getClaims().getExpiration()));
            handleUnauthorized(response, "未授权：token已过期");
            return false;
        } catch (SignatureException e) {
            log.warn("Token签名无效: {}", requestURI);
            handleUnauthorized(response, "未授权：token签名无效");
            return false;
        } catch (MalformedJwtException e) {
            log.warn("Token格式错误: {}", requestURI);
            handleUnauthorized(response, "未授权：token格式错误");
            return false;
        } catch (Exception e) {
            log.error("Token验证异常: {}, 异常: {}", requestURI, e.getMessage());
            handleUnauthorized(response, "未授权：token验证失败");
            return false;
        }
    }
    
    /**
     * 检查是否是重要路径，需要记录详细token信息
     */
    private boolean isImportantPath(String requestURI) {
        // 定义一些需要详细记录的重要路径
        String[] importantPaths = {
            "/user/", 
            "/admin/", 
            "/house/add",
            "/house/update",
            "/order/create",
            "/payment/"
        };
        
        for (String path : importantPaths) {
            if (requestURI.contains(path)) {
                return true;
            }
        }
        return false;
    }
    
    // 上次记录token的时间
    private static long lastTokenLogTime = 0;
    
    /**
     * 判断是否应该记录token信息（每小时最多记录一次）
     */
    private boolean shouldLogToken() {
        long currentTime = System.currentTimeMillis();
        // 每小时记录一次（3600000毫秒）
        if (currentTime - lastTokenLogTime > 3600000) {
            lastTokenLogTime = currentTime;
            return true;
        }
        return false;
    }
    
    /**
     * 检查URL是否在排除列表中
     */
    private boolean isExcludeUrl(String requestURI) {
        // 遍历排除列表，检查URL是否匹配
        for (String excludeUrl : EXCLUDE_URLS) {
            if (requestURI.startsWith(excludeUrl)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 处理未授权请求
     */
    private void handleUnauthorized(HttpServletResponse response, String message) throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write("{\"flag\":false,\"code\":401,\"message\":\"" + message + "\"}");
    }
} 