package com.house.constants;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单状态常量定义
 * 与前端保持完全一致
 */
public class OrderStatus {
    
    // 订单状态常量
    public static final String APPLICATION = "application";        // 租房申请
    public static final String APPROVED = "approved";             // 申请已批准
    public static final String CONTRACT_PENDING = "contract_pending"; // 待签署合同
    public static final String UNPAID = "unpaid";                 // 待支付押金
    public static final String PENDING = "pending";               // 待入住
    public static final String RENTING = "renting";               // 租赁中
    public static final String COMPLETED = "completed";           // 已完成
    public static final String CANCELLED = "cancelled";           // 已取消
    
    // 状态显示文本映射
    private static final Map<String, String> STATUS_TEXT_MAP = new HashMap<>();
    static {
        STATUS_TEXT_MAP.put(APPLICATION, "待审核");
        STATUS_TEXT_MAP.put(APPROVED, "申请已批准");
        STATUS_TEXT_MAP.put(CONTRACT_PENDING, "待签署合同");
        STATUS_TEXT_MAP.put(UNPAID, "待支付押金");
        STATUS_TEXT_MAP.put(PENDING, "待入住");
        STATUS_TEXT_MAP.put(RENTING, "租赁中");
        STATUS_TEXT_MAP.put(COMPLETED, "已完成");
        STATUS_TEXT_MAP.put(CANCELLED, "已取消");
    }
    
    // 状态描述映射
    private static final Map<String, String> STATUS_DESC_MAP = new HashMap<>();
    static {
        STATUS_DESC_MAP.put(APPLICATION, "租房申请已提交，等待房东审核");
        STATUS_DESC_MAP.put(APPROVED, "申请已通过，等待合同生成");
        STATUS_DESC_MAP.put(CONTRACT_PENDING, "合同已生成，等待签署");
        STATUS_DESC_MAP.put(UNPAID, "合同已签署，请尽快支付押金");
        STATUS_DESC_MAP.put(PENDING, "押金已支付，请准备入住");
        STATUS_DESC_MAP.put(RENTING, "已入住，请按时缴纳租金");
        STATUS_DESC_MAP.put(COMPLETED, "订单已完成，感谢您的使用");
        STATUS_DESC_MAP.put(CANCELLED, "订单已取消");
    }
    
    // 正确的业务流程顺序
    public static final List<String> ORDER_FLOW = Arrays.asList(
        APPLICATION,      // 1. 提交申请
        APPROVED,         // 2. 申请通过
        CONTRACT_PENDING, // 3. 签署合同
        UNPAID,          // 4. 支付押金
        PENDING,         // 5. 待入住
        RENTING,         // 6. 租赁中
        COMPLETED        // 7. 完成
    );
    
    // 可操作的状态转换
    private static final Map<String, List<String>> STATUS_TRANSITIONS = new HashMap<>();
    static {
        STATUS_TRANSITIONS.put(APPLICATION, Arrays.asList(APPROVED, CANCELLED));
        STATUS_TRANSITIONS.put(APPROVED, Arrays.asList(CONTRACT_PENDING, CANCELLED));
        STATUS_TRANSITIONS.put(CONTRACT_PENDING, Arrays.asList(UNPAID, CANCELLED));
        STATUS_TRANSITIONS.put(UNPAID, Arrays.asList(PENDING, CANCELLED));
        STATUS_TRANSITIONS.put(PENDING, Arrays.asList(RENTING));
        STATUS_TRANSITIONS.put(RENTING, Arrays.asList(COMPLETED));
        STATUS_TRANSITIONS.put(COMPLETED, Arrays.asList());
        STATUS_TRANSITIONS.put(CANCELLED, Arrays.asList());
    }
    
    /**
     * 获取状态显示文本
     */
    public static String getStatusText(String status) {
        return STATUS_TEXT_MAP.getOrDefault(status, status);
    }
    
    /**
     * 获取状态描述
     */
    public static String getStatusDesc(String status) {
        return STATUS_DESC_MAP.getOrDefault(status, "");
    }
    
    /**
     * 检查状态是否有效
     */
    public static boolean isValidStatus(String status) {
        return STATUS_TEXT_MAP.containsKey(status);
    }
    
    /**
     * 检查状态转换是否合法
     */
    public static boolean canTransitionTo(String fromStatus, String toStatus) {
        List<String> allowedTransitions = STATUS_TRANSITIONS.get(fromStatus);
        return allowedTransitions != null && allowedTransitions.contains(toStatus);
    }
    
    /**
     * 获取下一个可能的状态
     */
    public static List<String> getNextStatuses(String currentStatus) {
        return STATUS_TRANSITIONS.getOrDefault(currentStatus, Arrays.asList());
    }
    
    /**
     * 获取状态在流程中的顺序
     */
    public static int getStatusOrder(String status) {
        return ORDER_FLOW.indexOf(status);
    }
    
    /**
     * 检查是否为终态
     */
    public static boolean isFinalStatus(String status) {
        return COMPLETED.equals(status) || CANCELLED.equals(status);
    }
    
    /**
     * 检查是否可以取消
     */
    public static boolean canCancel(String status) {
        return !isFinalStatus(status) && !RENTING.equals(status);
    }
}
