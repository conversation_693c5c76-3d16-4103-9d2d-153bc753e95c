package com.house.constants;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 合同状态常量定义
 * 与前端保持完全一致
 */
public class ContractStatus {
    
    // 合同状态常量
    public static final String PENDING = "pending";           // 待签署
    public static final String ACTIVE = "active";            // 生效中
    public static final String EXPIRED = "expired";          // 已过期
    public static final String CANCELLED = "cancelled";       // 已取消
    
    // 状态显示文本映射
    private static final Map<String, String> STATUS_TEXT_MAP = new HashMap<>();
    static {
        STATUS_TEXT_MAP.put(PENDING, "待签署");
        STATUS_TEXT_MAP.put(ACTIVE, "已生效");
        STATUS_TEXT_MAP.put(EXPIRED, "已过期");
        STATUS_TEXT_MAP.put(CANCELLED, "已取消");
    }
    
    // 状态描述映射
    private static final Map<String, String> STATUS_DESC_MAP = new HashMap<>();
    static {
        STATUS_DESC_MAP.put(PENDING, "合同待双方签署");
        STATUS_DESC_MAP.put(ACTIVE, "合同已生效，正在执行中");
        STATUS_DESC_MAP.put(EXPIRED, "合同已到期");
        STATUS_DESC_MAP.put(CANCELLED, "合同已取消");
    }
    
    // 可操作的状态转换
    private static final Map<String, List<String>> STATUS_TRANSITIONS = new HashMap<>();
    static {
        STATUS_TRANSITIONS.put(PENDING, Arrays.asList(ACTIVE, CANCELLED));
        STATUS_TRANSITIONS.put(ACTIVE, Arrays.asList(EXPIRED, CANCELLED));
        STATUS_TRANSITIONS.put(EXPIRED, Arrays.asList());
        STATUS_TRANSITIONS.put(CANCELLED, Arrays.asList());
    }
    
    /**
     * 获取状态显示文本
     */
    public static String getStatusText(String status) {
        return STATUS_TEXT_MAP.getOrDefault(status, status);
    }
    
    /**
     * 获取状态描述
     */
    public static String getStatusDesc(String status) {
        return STATUS_DESC_MAP.getOrDefault(status, "");
    }
    
    /**
     * 检查状态是否有效
     */
    public static boolean isValidStatus(String status) {
        return STATUS_TEXT_MAP.containsKey(status);
    }
    
    /**
     * 检查状态转换是否合法
     */
    public static boolean canTransitionTo(String fromStatus, String toStatus) {
        List<String> allowedTransitions = STATUS_TRANSITIONS.get(fromStatus);
        return allowedTransitions != null && allowedTransitions.contains(toStatus);
    }
    
    /**
     * 获取下一个可能的状态
     */
    public static List<String> getNextStatuses(String currentStatus) {
        return STATUS_TRANSITIONS.getOrDefault(currentStatus, Arrays.asList());
    }
    
    /**
     * 检查是否为终态
     */
    public static boolean isFinalStatus(String status) {
        return EXPIRED.equals(status) || CANCELLED.equals(status);
    }
    
    /**
     * 检查是否可以签署
     */
    public static boolean canSign(String status) {
        return PENDING.equals(status);
    }
    
    /**
     * 检查是否可以取消
     */
    public static boolean canCancel(String status) {
        return PENDING.equals(status) || ACTIVE.equals(status);
    }
    
    /**
     * 检查合同是否生效
     */
    public static boolean isActive(String status) {
        return ACTIVE.equals(status);
    }
}
