<template>
  <div class="house-add">
    <div class="page-header">
      <h2>发布房源</h2>
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/backstage/dashboard' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: '/backstage/my-houses' }">我的房源</el-breadcrumb-item>
        <el-breadcrumb-item>发布房源</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <div class="form-container">
      <el-form :model="houseForm" :rules="rules" ref="houseForm" label-width="100px" class="house-form">
        <el-form-item label="房源地址" prop="address">
          <el-input v-model="houseForm.address" placeholder="请输入房源地址"></el-input>
        </el-form-item>

        <el-form-item label="租金" prop="price">
          <el-input-number v-model="houseForm.price" :min="0" :step="100" :precision="0" style="width: 200px;"></el-input-number>
          <span class="unit">元/月</span>
        </el-form-item>

        <el-form-item label="房源类型" prop="type">
          <el-select v-model="houseForm.type" placeholder="请选择房源类型">
            <el-option label="整租" value="整租"></el-option>
            <el-option label="合租" value="合租"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="户型" prop="layout">
          <el-select v-model="houseForm.layout" placeholder="请选择户型">
            <el-option label="1室0厅1卫" value="1室0厅1卫"></el-option>
            <el-option label="1室1厅1卫" value="1室1厅1卫"></el-option>
            <el-option label="2室1厅1卫" value="2室1厅1卫"></el-option>
            <el-option label="2室2厅1卫" value="2室2厅1卫"></el-option>
            <el-option label="3室1厅1卫" value="3室1厅1卫"></el-option>
            <el-option label="3室2厅2卫" value="3室2厅2卫"></el-option>
            <el-option label="4室2厅2卫" value="4室2厅2卫"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="面积" prop="area">
          <el-input-number v-model="houseForm.area" :min="1" :step="1" :precision="0" style="width: 200px;"></el-input-number>
          <span class="unit">平方米</span>
        </el-form-item>

        <el-form-item label="楼层" prop="floor">
          <el-input-number v-model="houseForm.floor" :min="1" :step="1" :precision="0" style="width: 200px;"></el-input-number>
          <span class="unit">层</span>
        </el-form-item>

        <el-form-item label="朝向" prop="orientation">
          <el-select v-model="houseForm.orientation" placeholder="请选择朝向">
            <el-option label="东" value="东"></el-option>
            <el-option label="南" value="南"></el-option>
            <el-option label="西" value="西"></el-option>
            <el-option label="北" value="北"></el-option>
            <el-option label="东南" value="东南"></el-option>
            <el-option label="东北" value="东北"></el-option>
            <el-option label="西南" value="西南"></el-option>
            <el-option label="西北" value="西北"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="装修" prop="decoration">
          <el-select v-model="houseForm.decoration" placeholder="请选择装修情况">
            <el-option label="精装" value="精装"></el-option>
            <el-option label="简装" value="简装"></el-option>
            <el-option label="毛坯" value="毛坯"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="配套设施" prop="facilities">
          <el-checkbox-group v-model="houseForm.facilities">
            <el-checkbox label="床"></el-checkbox>
            <el-checkbox label="衣柜"></el-checkbox>
            <el-checkbox label="沙发"></el-checkbox>
            <el-checkbox label="电视"></el-checkbox>
            <el-checkbox label="冰箱"></el-checkbox>
            <el-checkbox label="洗衣机"></el-checkbox>
            <el-checkbox label="空调"></el-checkbox>
            <el-checkbox label="热水器"></el-checkbox>
            <el-checkbox label="宽带"></el-checkbox>
            <el-checkbox label="暖气"></el-checkbox>
            <el-checkbox label="燃气灶"></el-checkbox>
            <el-checkbox label="微波炉"></el-checkbox>
          </el-checkbox-group>
        </el-form-item>

        <el-form-item label="房源描述" prop="detail">
          <el-input type="textarea" v-model="houseForm.detail" :rows="6" placeholder="请详细描述房源情况，如周边配套、交通情况等"></el-input>
        </el-form-item>

        <el-form-item label="房源图片" prop="images">
          <el-upload
            action="/api/upload"
            list-type="picture-card"
            :on-preview="handlePictureCardPreview"
            :on-remove="handleRemove"
            :on-success="handleUploadSuccess"
            :before-upload="beforeUpload"
            multiple>
            <i class="el-icon-plus"></i>
          </el-upload>
          <el-dialog :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="">
          </el-dialog>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm('houseForm')">发布房源</el-button>
          <el-button @click="resetForm('houseForm')">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import houseApi from '../api/house.js'
import { getBackstageUser } from '../utils/auth'

export default {
  name: 'HouseAdd',
  data() {
    return {
      houseForm: {
        address: '',
        price: 1000,
        type: '整租',
        layout: '',
        area: 50,
        floor: 1,
        orientation: '',
        decoration: '',
        facilities: [],
        detail: '',
        images: [],
        status: '未出租'
      },
      rules: {
        address: [
          { required: true, message: '请输入房源地址', trigger: 'blur' }
        ],
        price: [
          { required: true, message: '请输入租金', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择房源类型', trigger: 'change' }
        ],
        layout: [
          { required: true, message: '请选择户型', trigger: 'change' }
        ],
        area: [
          { required: true, message: '请输入面积', trigger: 'blur' }
        ],
        floor: [
          { required: true, message: '请输入楼层', trigger: 'blur' }
        ],
        orientation: [
          { required: true, message: '请选择朝向', trigger: 'change' }
        ],
        decoration: [
          { required: true, message: '请选择装修情况', trigger: 'change' }
        ],
        detail: [
          { required: true, message: '请输入房源描述', trigger: 'blur' }
        ]
      },
      dialogImageUrl: '',
      dialogVisible: false,
      userId: ''
    }
  },
  created() {
    const userInfo = getBackstageUser()
    if (userInfo && userInfo.userInfo) {
      this.userId = userInfo.userInfo.id
    }
  },
  methods: {
    handleRemove(file, fileList) {
      this.houseForm.images = fileList.map(file => file.url)
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    handleUploadSuccess(response, file, fileList) {
      if (response.flag) {
        this.houseForm.images.push(response.data.url)
        this.$message.success('图片上传成功')
      } else {
        this.$message.error('图片上传失败')
      }
    },
    beforeUpload(file) {
      const isImage = file.type.startsWith('image/')
      const isLt20M = file.size / 1024 / 1024 < 20

      if (!isImage) {
        this.$message.error('只能上传图片文件!')
      }
      if (!isLt20M) {
        this.$message.error('图片大小不能超过 20MB!')
      }
      return isImage && isLt20M
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const params = {
            address: this.houseForm.address,
            price: this.houseForm.price,
            status: this.houseForm.status,
            detail: this.houseForm.detail,
            imageUrl: this.houseForm.images.join(','),
            ownerId: this.userId,
            // 后端使用的是area字段，将前端的area映射过去
            area: this.houseForm.area.toString(),
            // 户型转换为房间数
            roomNum: parseInt(this.houseForm.layout.split('室')[0]) || 1,
            // 面积
            houseArea: this.houseForm.area,
            decoration: this.houseForm.decoration,
            // 额外信息作为扩展属性保存在detail中
            ownerName: '',  // 由后端根据ownerId查询设置
          }

          // 添加扩展信息到detail中
          const extraInfo = {
            type: this.houseForm.type,
            layout: this.houseForm.layout,
            floor: this.houseForm.floor,
            orientation: this.houseForm.orientation,
            facilities: this.houseForm.facilities.join(',')
          }
          params.detail = params.detail + '\n\n' + JSON.stringify(extraInfo)

          houseApi.addHouse(params).then(res => {
            if (res.data.flag) {
              this.$message.success('房源发布成功')
              this.$router.push('/backstage/my-houses')
            } else {
              this.$message.error('房源发布失败')
            }
          }).catch(() => {
            this.$message.error('房源发布失败')
          })
        } else {
          this.$message.error('请完善表单信息')
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>

<style scoped>
.house-add {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin-bottom: 10px;
}

.form-container {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.house-form {
  max-width: 800px;
}

.unit {
  margin-left: 10px;
  color: #606266;
}
</style> 