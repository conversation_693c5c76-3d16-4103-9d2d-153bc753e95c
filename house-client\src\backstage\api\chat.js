import request from '../../utils/request'

/**
 * 获取所有聊天消息列表
 * @param {Number} landlordId 房东ID
 * @returns {Promise} 请求Promise
 */
export function getAllChatMessages(landlordId) {
  return request({
    url: '/chat/landlord/messages',
    method: 'get',
    params: { landlordId }
  })
}

/**
 * 获取与特定用户的聊天记录
 * @param {Number} houseId 房源ID
 * @param {Number} userId 用户ID
 * @param {Number} landlordId 房东ID
 * @returns {Promise} 请求Promise
 */
export function getChatHistory(houseId, userId, landlordId) {
  return request({
    url: '/chat/history',
    method: 'get',
    params: { houseId, userId, landlordId }
  })
}

/**
 * 发送消息
 * @param {Object} message 消息对象
 * @returns {Promise} 请求Promise
 */
export function sendMessage(message) {
  return request({
    url: '/chat/send',
    method: 'post',
    data: message
  })
}

/**
 * 将消息标记为已读
 * @param {Number} toUserId 接收者ID
 * @param {Number} fromUserId 发送者ID
 * @returns {Promise} 请求Promise
 */
export function markMessagesAsRead(toUserId, fromUserId) {
  return request({
    url: '/chat/markRead',
    method: 'put',
    params: { toUserId, fromUserId }
  })
}

/**
 * 获取未读消息数量
 * @param {Number} landlordId 房东ID
 * @returns {Promise} 请求Promise
 */
export function getUnreadMessageCount(landlordId) {
  return request({
    url: '/chat/unreadCount/landlord',
    method: 'get',
    params: { landlordId }
  })
}

/**
 * 删除聊天记录
 * @param {Number} houseId 房源ID
 * @param {Number} userId 用户ID
 * @param {Number} landlordId 房东ID
 * @returns {Promise} 请求Promise
 */
export function deleteChatHistory(houseId, userId, landlordId) {
  return request({
    url: '/chat/delete',
    method: 'delete',
    params: { houseId, userId, landlordId }
  })
}

export default {
  getAllChatMessages,
  getChatHistory,
  sendMessage,
  markMessagesAsRead,
  getUnreadMessageCount,
  deleteChatHistory
} 