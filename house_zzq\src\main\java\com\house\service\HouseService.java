package com.house.service;

import com.github.pagehelper.PageInfo;
import com.house.dto.HousePageQueryDTO;
import com.house.dto.HouseQueryDTO;
import com.house.pojo.House;

import java.util.List;

/**
 * 房源服务接口
 */
public interface HouseService {
    
    /**
     * 获取所有房源列表
     *
     * @return 房源列表
     */
    List<House> getAllHouseList();
    
    /**
     * 获取热门房源列表（按点击量排序）
     *
     * @param limit 获取数量
     * @return 热门房源列表
     */
    List<House> getHotHouseList(int limit);
    
    /**
     * 根据条件查询房源
     *
     * @param queryDTO 查询条件
     * @return 房源列表
     */
    List<House> getHouseListByCondition(HouseQueryDTO queryDTO);
    
    /**
     * 获取房源详情
     *
     * @param houseId 房源ID
     * @return 房源详情
     */
    House getHouseDetail(Integer houseId);
    
    /**
     * 增加房源点击量
     *
     * @param houseId 房源ID
     * @return 是否成功
     */
    boolean increaseViewCount(Integer houseId);
    
    /**
     * 获取用户发布的房源
     *
     * @param userId 用户ID
     * @return 房源列表
     */
    List<House> getHouseListByUserId(Integer userId);
    
    /**
     * 分页查询房东的房源列表
     *
     * @param queryDTO 查询条件
     * @return 分页房源列表
     */
    PageInfo<House> getHouseListByOwnerIdPage(HousePageQueryDTO queryDTO);
    
    /**
     * 添加房源
     *
     * @param house 房源信息
     * @return 添加结果
     */
    boolean addHouse(House house);
    
    /**
     * 更新房源信息
     *
     * @param house 房源信息
     * @return 更新结果
     */
    boolean updateHouse(House house);
    
    /**
     * 删除房源
     *
     * @param houseId 房源ID
     * @return 删除结果
     */
    boolean deleteHouse(Integer houseId);
} 