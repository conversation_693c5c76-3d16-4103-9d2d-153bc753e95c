package com.house.config;

import com.house.websocket.WebSocketServer;
import com.house.websocket.WebSocketChatServer;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

/**
 * WebSocket配置类
 */
@Configuration
public class WebSocketConfig implements ApplicationContextAware {

    /**
     * 注入ServerEndpointExporter，
     * 这个bean会自动注册使用了@ServerEndpoint注解声明的Websocket endpoint
     */
    @Bean
    public ServerEndpointExporter serverEndpointExporter() {
        return new ServerEndpointExporter();
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        // 将ApplicationContext设置到WebSocketServer中
        WebSocketServer.setApplicationContext(applicationContext);
        // 将ApplicationContext设置到WebSocketChatServer中
        WebSocketChatServer.setApplicationContext(applicationContext);
    }
} 