import request from '../../utils/request'

const api_name = 'message'

export default {
  // 获取用户的消息列表
  getMessageListByUserId(userId) {
    return request({
      url: `/${api_name}/getmessagelistbyuserid?userId=${userId}`,
      method: 'get'
    })
  },

  // 获取未读消息数量
  getUnreadMessageCount(userId) {
    return request({
      url: `/${api_name}/getunreadmessagecount?userId=${userId}`,
      method: 'get'
    })
  },

  // 标记消息为已读
  markMessageAsRead(messageId) {
    return request({
      url: `/${api_name}/markmessageasread?messageId=${messageId}`,
      method: 'post'
    })
  },

  // 标记所有消息为已读
  markAllMessagesAsRead(userId) {
    return request({
      url: `/${api_name}/markallmessagesasread?userId=${userId}`,
      method: 'post'
    })
  },

  // 删除消息
  deleteMessage(messageId) {
    return request({
      url: `/${api_name}/deletemessage?messageId=${messageId}`,
      method: 'delete'
    })
  },

  // 发送消息
  sendMessage(data) {
    return request({
      url: `/${api_name}/sendmessage`,
      method: 'post',
      data
    })
  }
} 