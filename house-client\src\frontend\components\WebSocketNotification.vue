<template>
  <div class="notification-container">
    <transition-group name="notification-list" tag="div">
      <div 
        v-for="notification in visibleNotifications" 
        :key="notification.id"
        class="notification-item"
        :class="getNotificationClass(notification.type)">
        <div class="notification-content">
          <div class="notification-icon">
            <i :class="getNotificationIcon(notification.type)"></i>
          </div>
          <div class="notification-body">
            <div class="notification-title">{{ notification.title }}</div>
            <div class="notification-message">{{ notification.message }}</div>
            <div class="notification-time">{{ formatTime(notification.timestamp) }}</div>
          </div>
          <div class="notification-close" @click="hideNotification(notification.id)">
            <i class="el-icon-close"></i>
          </div>
        </div>
      </div>
    </transition-group>
  </div>
</template>

<script>
import { getUser } from '../../utils/auth'

export default {
  name: 'WebSocketNotification',
  data() {
    return {
      socket: null,
      connected: false,
      notifications: [],
      visibleNotifications: [],
      unreadCount: 0,
      maxVisibleNotifications: 3,
      reconnectCount: 0,
      reconnectTimer: null,
      heartbeatTimer: null,
      manualClosed: false,
      needReconnect: true // 默认需要重连，在组件销毁时设为false
    }
  },
  created() {
    // 初始化WebSocket连接
    this.initWebSocket();
    
    // 添加页面可见性变化监听
    document.addEventListener('visibilitychange', this.handleVisibilityChange);
    
    // 添加页面刷新前的处理
    window.addEventListener('beforeunload', this.handleBeforeUnload);
  },
  beforeDestroy() {
    // 移除事件监听
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    window.removeEventListener('beforeunload', this.handleBeforeUnload);
    
    // 关闭WebSocket连接
    this.needReconnect = false; // 组件销毁前设置不需要重连
    this.closeWebSocket();
  },
  methods: {
    initWebSocket() {
      // 获取用户信息
      const userData = getUser();
      if (!userData || !userData.userInfo || !userData.token) {
        if (process.env.NODE_ENV === 'development') {
          console.log('用户未登录，不初始化WebSocket');
        }
        return;
      }
      
      const userId = userData.userInfo.id;
      const token = userData.token;
      
      try {
        // 检查是否已经有连接，如果有则关闭
        if (this.socket && this.socket.readyState !== WebSocket.CLOSED) {
          // 如果已经连接，不重复连接
          if (this.socket.readyState === WebSocket.OPEN) {
            if (process.env.NODE_ENV === 'development') {
              console.log('WebSocket已经连接，无需重新连接');
            }
            return;
          }
          // 如果正在连接中，也不重复连接
          if (this.socket.readyState === WebSocket.CONNECTING) {
            if (process.env.NODE_ENV === 'development') {
              console.log('WebSocket正在连接中，无需重新发起连接');
            }
            return;
          }
          // 关闭现有连接
          this.closeWebSocket();
        }
        
        // 重置手动关闭标志
        this.manualClosed = false;
        
        // 正确的WebSocket连接地址
        const wsUrl = `ws://localhost:9002/ws/chat/${userId}?token=${token}&houseId=0&landlordId=${userId}`;
        if (process.env.NODE_ENV === 'development') {
          console.log('尝试连接前台WebSocket:', wsUrl);
        }
        
        this.socket = new WebSocket(wsUrl);
        
        // WebSocket连接成功
        this.socket.onopen = () => {
          if (process.env.NODE_ENV === 'development') {
            console.log('WebSocket连接成功');
          }
          this.connected = true;
          // 连接成功后清除重连计时器
          if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
          }
          // 发送心跳
          this.startHeartbeat();
        };
        
        // 接收消息
        this.socket.onmessage = (event) => {
          if (process.env.NODE_ENV === 'development') {
            console.log('收到WebSocket消息:', event.data);
          }
          this.handleMessage(event.data);
        };
        
        // 连接关闭
        this.socket.onclose = (event) => {
          if (process.env.NODE_ENV === 'development') {
            console.log('WebSocket连接关闭，状态码:', event.code);
          }
          this.connected = false;
          this.stopHeartbeat();
          
          // 只要用户仍在登录状态，无论何种原因断开，都尝试立即重连
          if (!this.manualClosed) {
            if (process.env.NODE_ENV === 'development') {
              console.log('WebSocket连接断开，立即尝试重新连接');
            }
            // 不使用延迟重连策略，直接重新连接
            this.initWebSocket();
          }
        };
        
        // 连接错误
        this.socket.onerror = (error) => {
          console.error('WebSocket连接错误');
          this.connected = false;
          // 连接错误时也尝试重连
          this.reconnect();
        };
      } catch (error) {
        console.error('初始化WebSocket失败:', error);
        // 初始化失败时也尝试重连
        this.reconnect();
      }
    },
    
    reconnect() {
      // 立即重新连接，不再使用延迟和计数
      console.log('正在重新连接WebSocket...');
      this.initWebSocket();
    },
    
    startHeartbeat() {
      // 每15秒发送一次心跳，确保连接保持活跃
      this.heartbeatTimer = setInterval(() => {
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
          // 发送心跳消息
          if (process.env.NODE_ENV === 'development') {
            console.log("发送WebSocket心跳");
          }
          this.socket.send(JSON.stringify({type: 'heartbeat', timestamp: Date.now()}));
        } else if (this.socket && this.socket.readyState !== WebSocket.CONNECTING && !this.manualClosed) {
          // 如果连接已断开但不是手动关闭，尝试重新连接
          if (process.env.NODE_ENV === 'development') {
            console.log("心跳检测到连接已断开，尝试重新连接");
          }
          this.initWebSocket();
        }
      }, 15000);
    },
    
    stopHeartbeat() {
      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer);
        this.heartbeatTimer = null;
      }
    },
    
    closeWebSocket() {
      this.manualClosed = true; // 标记为手动关闭
      this.stopHeartbeat();
      
      // 清除重连定时器
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
      }
      
      // 关闭连接
      if (this.socket) {
        // 添加关闭原因
        try {
          this.socket.close(1000, "手动关闭连接"); // 1000表示正常关闭
        } catch (e) {
          console.error("关闭WebSocket连接时出错:", e);
        }
        this.socket = null;
        this.connected = false;
        console.log("WebSocket连接已手动关闭");
      }
    },
    handleMessage(data) {
      try {
        // 解析后端发送的消息
        const message = JSON.parse(data);
        
        // 添加消息ID和时间戳（如果后端没有提供）
        const notification = {
          id: message.id || new Date().getTime(),
          title: message.title || '系统通知',
          message: message.content,
          type: message.type || 'info',
          timestamp: message.timestamp || new Date().getTime(),
          read: false
        };
        
        // 添加到通知列表
        this.notifications.unshift(notification);
        
        // 更新未读计数
        this.unreadCount++;
        
        // 显示通知
        this.showNotification(notification);
      } catch (error) {
        console.error('解析WebSocket消息失败:', error);
      }
    },
    showNotification(notification) {
      // 添加到可见通知列表
      this.visibleNotifications.unshift(notification);
      
      // 限制可见通知的数量
      if (this.visibleNotifications.length > this.maxVisibleNotifications) {
        this.visibleNotifications.pop();
      }
      
      // 自动隐藏通知
      setTimeout(() => {
        this.hideNotification(notification.id);
      }, 5000); // 5秒后自动隐藏
    },
    hideNotification(id) {
      const index = this.visibleNotifications.findIndex(item => item.id === id);
      if (index !== -1) {
        this.visibleNotifications.splice(index, 1);
      }
    },
    getNotificationClass(type) {
      const typeMap = {
        'info': 'notification-info',
        'success': 'notification-success',
        'warning': 'notification-warning',
        'error': 'notification-error',
        'order': 'notification-order',
        'contract': 'notification-contract',
        'payment': 'notification-payment',
        'message': 'notification-message'
      };
      return typeMap[type] || 'notification-info';
    },
    getNotificationIcon(type) {
      const iconMap = {
        'info': 'el-icon-info',
        'success': 'el-icon-success',
        'warning': 'el-icon-warning',
        'error': 'el-icon-error',
        'order': 'el-icon-shopping-cart-full',
        'contract': 'el-icon-document-checked',
        'payment': 'el-icon-money',
        'message': 'el-icon-message'
      };
      return iconMap[type] || 'el-icon-info';
    },
    formatTime(timestamp) {
      if (!timestamp) return '';
      
      const date = new Date(timestamp);
      const now = new Date();
      
      // 如果是当天的消息，只显示时间
      if (date.toDateString() === now.toDateString()) {
        return date.getHours().toString().padStart(2, '0') + ':' +
               date.getMinutes().toString().padStart(2, '0');
      } else {
        // 否则显示月/日 时:分
        return (date.getMonth() + 1) + '/' + date.getDate() + ' ' +
               date.getHours().toString().padStart(2, '0') + ':' +
               date.getMinutes().toString().padStart(2, '0');
      }
    },
    // 处理页面可见性变化
    handleVisibilityChange() {
      if (document.visibilityState === 'visible') {
        // 页面变为可见时，检查连接状态并确保连接
        if (process.env.NODE_ENV === 'development') {
          console.log('页面变为可见，检查WebSocket连接状态');
        }
        if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
          if (process.env.NODE_ENV === 'development') {
            console.log('WebSocket连接不可用，重新连接');
          }
          this.initWebSocket();
        }
      }
    },
    // 处理页面刷新前
    handleBeforeUnload() {
      // 页面刷新或关闭前不需要特殊处理
      // WebSocket会自动关闭，并在下次打开页面时重连
      if (process.env.NODE_ENV === 'development') {
        console.log('页面即将刷新或关闭');
      }
    }
  }
}
</script>

<style scoped>
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.notification-item {
  width: 350px;
  margin-bottom: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  background-color: #fff;
  transition: all 0.3s;
}

.notification-content {
  display: flex;
  padding: 15px;
}

.notification-icon {
  flex: 0 0 24px;
  font-size: 24px;
  margin-right: 10px;
}

.notification-body {
  flex: 1;
}

.notification-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.notification-message {
  color: #606266;
  font-size: 14px;
}

.notification-time {
  margin-top: 5px;
  color: #909399;
  font-size: 12px;
}

.notification-close {
  flex: 0 0 16px;
  cursor: pointer;
  color: #909399;
  transition: color 0.3s;
}

.notification-close:hover {
  color: #606266;
}

.notification-info .notification-icon {
  color: #409eff;
}

.notification-success .notification-icon {
  color: #67c23a;
}

.notification-warning .notification-icon {
  color: #e6a23c;
}

.notification-error .notification-icon {
  color: #f56c6c;
}

.notification-order .notification-icon {
  color: #409eff;
}

.notification-contract .notification-icon {
  color: #409eff;
}

.notification-payment .notification-icon {
  color: #f56c6c;
}

.notification-message .notification-icon {
  color: #67c23a;
}

.notification-list-enter-active, .notification-list-leave-active {
  transition: all 0.5s;
}

.notification-list-enter, .notification-list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style> 