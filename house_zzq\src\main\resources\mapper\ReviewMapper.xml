<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.house.mapper.ReviewMapper">

    <!-- 评价结果映射 -->
    <resultMap id="ReviewResultMap" type="com.house.pojo.Review">
        <id column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="order_no" property="orderNo" />
        <result column="house_id" property="houseId" />
        <result column="owner_id" property="ownerId" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="user_avatar" property="userAvatar" />
        <result column="rating" property="rating" />
        <result column="location_rating" property="locationRating" />
        <result column="facility_rating" property="facilityRating" />
        <result column="service_rating" property="serviceRating" />
        <result column="value_rating" property="valueRating" />
        <result column="environment_rating" property="environmentRating" />
        <result column="cleanliness_rating" property="cleanlinessRating" />
        <result column="average_rating" property="averageRating" />
        <result column="content" property="content" />
        <result column="tags" property="tags" />
        <result column="images" property="images" />
        <result column="anonymous" property="anonymous" />
        <result column="reply" property="reply" />
        <result column="reply_time" property="replyTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    
    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, order_id, order_no, house_id, owner_id, user_id, user_name, user_avatar, 
        rating, location_rating, facility_rating, service_rating, value_rating, environment_rating, cleanliness_rating, 
        average_rating, content, tags, images, anonymous, reply, reply_time, create_time, update_time
    </sql>

    <!-- 查询房屋所有评价 -->
    <select id="selectReviewsByHouseIdXml" parameterType="java.lang.Integer" resultMap="ReviewResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_review
        WHERE house_id = #{houseId}
        ORDER BY create_time DESC
    </select>
    
    <!-- 根据订单ID查询评价 -->
    <select id="selectReviewByOrderIdXml" parameterType="java.lang.Integer" resultMap="ReviewResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tb_review
        WHERE order_id = #{orderId}
    </select>
    
    <!-- 插入评价 -->
    <insert id="insertReviewXml" parameterType="com.house.pojo.Review" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_review(
            order_id, order_no, house_id, owner_id, user_id, user_name, user_avatar,
            rating, location_rating, facility_rating, service_rating, value_rating, environment_rating, cleanliness_rating,
            average_rating, content, tags, images, anonymous, create_time, update_time
        )
        VALUES(
            #{orderId}, #{orderNo}, #{houseId}, #{ownerId}, #{userId}, #{userName}, #{userAvatar},
            #{rating}, #{locationRating}, #{facilityRating}, #{serviceRating}, #{valueRating}, #{environmentRating},
            #{cleanlinessRating}, #{averageRating}, #{content}, #{tags}, #{images}, #{anonymous}, NOW(), NOW()
        )
    </insert>
    
    <!-- 房东回复评价 -->
    <update id="replyReviewXml">
        UPDATE tb_review
        SET reply = #{reply},
            reply_time = NOW(),
            update_time = NOW()
        WHERE id = #{reviewId}
    </update>
    
    <!-- 删除评价 -->
    <delete id="deleteReviewXml" parameterType="java.lang.Integer">
        DELETE FROM tb_review
        WHERE id = #{reviewId}
    </delete>
</mapper> 