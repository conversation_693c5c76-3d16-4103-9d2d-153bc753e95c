<template>
  <el-aside :width="isCollapsed ? '64px' : '220px'" class="aside" :class="{ 'is-collapsed': isCollapsed }">
    <div class="logo-container">
      <h1>房屋租赁管理系统</h1>
    </div>
    <el-menu
      :default-active="activeMenu"
      class="el-menu-vertical"
      background-color="#304156"
      text-color="#bfcbd9"
      active-text-color="#409EFF"
      router
      unique-opened
      :collapse="isCollapsed"
    >
      <!-- 首页/仪表盘 - 所有用户可见 -->
      <el-menu-item index="/backstage/dashboard">
        <i class="fas fa-home"></i>
        <span slot="title">首页</span>
      </el-menu-item>
      
      <!-- 管理员控制台 - 仅管理员可见 -->
      <el-menu-item index="/backstage/admin" v-if="isAdmin">
        <i class="fas fa-tachometer-alt"></i>
        <span slot="title">管理员控制台</span>
      </el-menu-item>
      
      <!-- 用户管理 - 仅管理员可见 -->
      <el-submenu index="1" v-if="isAdmin">
        <template slot="title">
          <i class="fas fa-users"></i>
          <span>用户管理</span>
        </template>
        <el-menu-item index="/backstage/users">
          <i class="fas fa-user-circle"></i>
          用户列表
        </el-menu-item>
      </el-submenu>

      <!-- 房源管理 - 仅管理员可见 -->
      <el-submenu index="2" v-if="isAdmin">
        <template slot="title">
          <i class="fas fa-building"></i>
          <span>房源管理</span>
        </template>
        <el-menu-item index="/backstage/houses">
          <i class="fas fa-home"></i>
          房源列表
        </el-menu-item>
      </el-submenu>

      <!-- 订单管理 - 所有用户可见 -->
      <el-submenu index="3">
        <template slot="title">
          <i class="fas fa-list-alt"></i>
          <span>订单管理</span>
        </template>
        <el-menu-item index="/backstage/orders/applications">
          <i class="fas fa-clipboard-list"></i>
          订单列表
        </el-menu-item>
        <el-menu-item index="/backstage/contracts">
          <i class="fas fa-file-contract"></i>
          合同管理
        </el-menu-item>
      </el-submenu>

      <!-- 我的房源 - 房东和管理员可见 -->
      <el-submenu index="4" v-if="isOwner || isAdmin">
        <template slot="title">
          <i class="fas fa-home"></i>
          <span>我的房源</span>
        </template>
        <el-menu-item index="/backstage/my-houses">
          <i class="fas fa-list"></i>
          房源列表
        </el-menu-item>
        <el-menu-item index="/backstage/house-add">
          <i class="fas fa-plus-circle"></i>
          发布房源
        </el-menu-item>
      </el-submenu>

      <!-- 评价管理 -->
      <el-menu-item index="/backstage/reviews">
        <i class="fas fa-star"></i>
        <span slot="title">评价管理</span>
      </el-menu-item>

      <!-- 房源咨询消息 -->
      <el-menu-item index="/backstage/chat-messages">
        <i class="fas fa-comments"></i>
        <span slot="title">
          <el-badge :value="unreadChatCount" :hidden="unreadChatCount <= 0" class="chat-badge">
            房源咨询消息
          </el-badge>
        </span>
      </el-menu-item>

      <!-- 个人信息 -->
      <el-menu-item index="/backstage/profile">
        <i class="fas fa-user"></i>
        <span slot="title">个人信息</span>
      </el-menu-item>

      <!-- 系统设置 -->
      <el-menu-item index="/backstage/settings">
        <i class="fas fa-cog"></i>
        <span slot="title">系统设置</span>
      </el-menu-item>
    </el-menu>
  </el-aside>
</template>

<script>
import { isAdmin, isOwner } from '@/utils/auth'
import request from '@/utils/request'

export default {
  name: 'SidebarComponent',
  props: {
    isCollapsed: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isAdmin: false,
      isOwner: false,
      unreadChatCount: 0,
      userId: null
    }
  },
  computed: {
    activeMenu() {
      return this.$route.path
    }
  },
  created() {
    this.isAdmin = isAdmin(false) // 确保检查的是后台用户权限
    this.isOwner = isOwner(false) // 确保检查的是后台用户权限
    
    // 获取用户信息
    const userInfo = JSON.parse(localStorage.getItem('house_backstage_user_info')) || {}
    if (userInfo && userInfo.userInfo && userInfo.userInfo.id) {
      this.userId = userInfo.userInfo.id
      
      // 如果是房东，获取未读房源咨询消息数量
      if (this.isOwner && this.userId) {
        this.fetchUnreadChatCount()
        
        // 设置定时器，每分钟更新一次
        setInterval(() => {
          this.fetchPendingContractsCount()
          this.fetchUnreadChatCount()
        }, 60000)
      }
    }
  },
  methods: {

    // 获取未读房源咨询消息数量
    async fetchUnreadChatCount() {
      try {
        // 不再调用/api/chat/unreadCount/landlord接口，直接使用本地存储的消息数据或设置为0
        this.unreadChatCount = 0
      } catch (error) {
        console.error('获取未读房源咨询消息数量失败:', error)
      }
    }
  }
}
</script>

<style scoped>
.aside {
  background-color: #304156;
  overflow: hidden;
  transition: width 0.3s;
  height: 100%;
  position: relative;
  z-index: 10;
}

.aside.is-collapsed {
  width: 64px !important;
}

.logo-container {
  height: 60px;
  line-height: 60px;
  text-align: center;
  color: #fff;
  background-color: #2b3649;
  overflow: hidden;
}

.logo-container h1 {
  font-size: 18px;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.el-menu {
  border-right: none;
  height: calc(100% - 60px);
}

/* 折叠菜单时隐藏文字 */
.el-menu--collapse .el-submenu__title span {
  height: 0;
  width: 0;
  overflow: hidden;
  visibility: hidden;
  display: inline-block;
}

.el-menu--collapse .el-submenu__icon-arrow {
  display: none;
}

/* 徽章样式 */
.contract-badge >>> .el-badge__content,
.chat-badge >>> .el-badge__content {
  background-color: #f56c6c;
}

/* 图标样式 */
.fas {
  margin-right: 5px;
  width: 24px;
  text-align: center;
  font-size: 18px;
  vertical-align: middle;
}
</style> 