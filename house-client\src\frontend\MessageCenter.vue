<template>
  <div class="message-center-page content-scroll">
    <Header />
    <div class="container">
      <h2>消息中心</h2>
      
      <el-tabs v-model="activeTab" @tab-click="handleTabChange">
        <el-tab-pane label="全部消息" name="all"></el-tab-pane>
        <el-tab-pane label="订单通知" name="order">
          <span slot="label"><i class="el-icon-shopping-cart-full"></i> 订单通知</span>
        </el-tab-pane>
        <el-tab-pane label="合同通知" name="contract">
          <span slot="label"><i class="el-icon-document-checked"></i> 合同通知</span>
        </el-tab-pane>
        <el-tab-pane label="支付通知" name="payment">
          <span slot="label"><i class="el-icon-money"></i> 支付通知</span>
        </el-tab-pane>
        <el-tab-pane label="系统通知" name="system">
          <span slot="label"><i class="el-icon-info"></i> 系统通知</span>
        </el-tab-pane>
      </el-tabs>
      
      <div class="message-tools">
        <el-button 
          size="small"
          type="text"
          @click="markAllAsRead"
          :disabled="messages.length === 0 || allRead">
          <i class="el-icon-check"></i> 全部标为已读
        </el-button>
        <el-button
          size="small"
          type="text"
          @click="deleteAllMessages"
          :disabled="messages.length === 0">
          <i class="el-icon-delete"></i> 清空消息
        </el-button>
      </div>
      
      <div class="messages-container" v-loading="loading">
        <div v-if="messages.length > 0">
          <div 
            v-for="message in messages" 
            :key="message.id" 
            class="message-item" 
            :class="{'message-read': message.read}">
            <div class="message-icon" :class="getMessageClass(message.type)">
              <i :class="getMessageIcon(message.type)"></i>
            </div>
            <div class="message-content" @click="viewMessageDetail(message)">
              <div class="message-header">
                <div class="message-title">{{ message.title }}</div>
                <div class="message-time">{{ formatTime(message.timestamp) }}</div>
              </div>
              <div class="message-body">{{ message.message }}</div>
            </div>
            <div class="message-actions">
              <el-dropdown trigger="click" @command="handleCommand">
                <span class="el-dropdown-link">
                  <i class="el-icon-more"></i>
                </span>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item :command="{ type: 'read', id: message.id }" v-if="!message.read">标为已读</el-dropdown-item>
                  <el-dropdown-item :command="{ type: 'unread', id: message.id }" v-else>标为未读</el-dropdown-item>
                  <el-dropdown-item :command="{ type: 'delete', id: message.id }">删除</el-dropdown-item>
                  <el-dropdown-item :command="{ type: 'detail', id: message.id }">查看详情</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
          
          <!-- 分页 -->
          <div class="pagination">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total">
            </el-pagination>
          </div>
        </div>
        
        <!-- 无消息时显示 -->
        <el-empty v-else description="暂无消息通知"></el-empty>
      </div>
    </div>
    
    <!-- 消息详情对话框 -->
    <el-dialog
      title="消息详情"
      :visible.sync="dialogVisible"
      width="500px">
      <div v-if="currentMessage" class="message-detail">
        <div class="detail-header">
          <div class="detail-title">{{ currentMessage.title }}</div>
          <div class="detail-time">{{ formatDateTime(currentMessage.timestamp) }}</div>
        </div>
        <div class="detail-content">{{ currentMessage.message }}</div>
        <div v-if="currentMessage.linkType" class="detail-action">
          <el-button type="primary" @click="handleMessageAction(currentMessage)">
            {{ getActionText(currentMessage.linkType) }}
          </el-button>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
    
    <Footer />
  </div>
</template>

<script>
import Header from './components/Header.vue'
import Footer from './components/Footer.vue'
import { getUser } from '../utils/auth'
import axios from 'axios'

export default {
  name: 'MessageCenter',
  components: {
    Header,
    Footer
  },
  data() {
    return {
      loading: false,
      activeTab: 'all',
      messages: [],
      currentPage: 1,
      pageSize: 20,
      total: 0,
      dialogVisible: false,
      currentMessage: null,
      userInfo: {}
    }
  },
  computed: {
    allRead() {
      return this.messages.every(message => message.read);
    }
  },
  created() {
    // 获取用户信息
    const userData = getUser();
    if (userData && userData.userInfo) {
      this.userInfo = userData.userInfo;
    } else {
      this.$router.push('/frontend/login');
      return;
    }
    
    // 加载消息数据
    this.loadMessages();
  },
  methods: {
    loadMessages() {
      this.loading = true;
      
      const params = {
        userId: this.userInfo.id,
        type: this.activeTab === 'all' ? '' : this.activeTab,
        page: this.currentPage,
        limit: this.pageSize
      };
      
      // 调用真实API获取消息数据
      axios.get('/notification/list', {
        params: params
      })
        .then(response => {
          if (response.data && response.data.flag) {
            this.messages = response.data.data.list;
            this.total = response.data.data.total;
          } else {
            this.$message.error(response.data.message || '获取消息数据失败');
          }
          this.loading = false;
        })
        .catch(error => {
          console.error('获取消息数据失败:', error);
          this.$message.error('服务器错误，请稍后再试');
        this.loading = false;
        });
    },
    handleTabChange() {
      this.currentPage = 1;
      this.loadMessages();
    },
    handleSizeChange(size) {
      this.pageSize = size;
      this.loadMessages();
    },
    handleCurrentChange(page) {
      this.currentPage = page;
      this.loadMessages();
    },
    handleCommand(command) {
      const { type, id } = command;
      
      switch(type) {
        case 'read':
          this.markAsRead(id);
          break;
        case 'unread':
          this.markAsUnread(id);
          break;
        case 'delete':
          this.deleteMessage(id);
          break;
        case 'detail':
          this.viewMessageDetail(this.messages.find(m => m.id === id));
          break;
        default:
          break;
      }
    },
    markAsRead(id) {
      const index = this.messages.findIndex(m => m.id === id);
      if (index !== -1) {
        // API调用更新状态
        axios.post('/notification/read', { id })
          .then(response => {
            if (response.data && response.data.flag) {
        this.messages[index].read = true;
            } else {
              this.$message.error(response.data.message || '标记消息失败');
            }
          })
          .catch(error => {
            console.error('标记消息失败:', error);
            this.$message.error('服务器错误，请稍后再试');
          });
      }
    },
    markAsUnread(id) {
      const index = this.messages.findIndex(m => m.id === id);
      if (index !== -1) {
        // API调用更新状态
        axios.post('/notification/unread', { id })
          .then(response => {
            if (response.data && response.data.flag) {
        this.messages[index].read = false;
            } else {
              this.$message.error(response.data.message || '标记消息失败');
            }
          })
          .catch(error => {
            console.error('标记消息失败:', error);
            this.$message.error('服务器错误，请稍后再试');
          });
      }
    },
    markAllAsRead() {
      // API调用更新所有消息状态
      axios.post('/notification/read-all', { userId: this.userInfo.id })
        .then(response => {
          if (response.data && response.data.flag) {
      this.messages.forEach(message => {
        message.read = true;
      });
      this.$message.success('已将所有消息标记为已读');
          } else {
            this.$message.error(response.data.message || '标记所有消息失败');
          }
        })
        .catch(error => {
          console.error('标记所有消息失败:', error);
          this.$message.error('服务器错误，请稍后再试');
        });
    },
    deleteMessage(id) {
      this.$confirm('确认删除此消息?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // API调用删除消息
        axios.post('/notification/delete', { id })
          .then(response => {
            if (response.data && response.data.flag) {
        const index = this.messages.findIndex(m => m.id === id);
        if (index !== -1) {
          this.messages.splice(index, 1);
          this.total--;
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
              }
            } else {
              this.$message.error(response.data.message || '删除消息失败');
            }
          })
          .catch(error => {
            console.error('删除消息失败:', error);
            this.$message.error('服务器错误，请稍后再试');
          });
      }).catch(() => {
        // 用户取消操作
      });
    },
    deleteAllMessages() {
      this.$confirm('确认清空所有消息? 此操作不可恢复!', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // API调用清空消息
        axios.post('/notification/delete-all', { userId: this.userInfo.id })
          .then(response => {
            if (response.data && response.data.flag) {
        this.messages = [];
        this.total = 0;
        this.$message({
          type: 'success',
          message: '已清空所有消息!'
        });
            } else {
              this.$message.error(response.data.message || '清空消息失败');
            }
          })
          .catch(error => {
            console.error('清空消息失败:', error);
            this.$message.error('服务器错误，请稍后再试');
          });
      }).catch(() => {
        // 用户取消操作
      });
    },
    viewMessageDetail(message) {
      this.currentMessage = message;
      this.dialogVisible = true;
      
      // 如果消息未读，标记为已读
      if (!message.read) {
        this.markAsRead(message.id);
      }
    },
    handleMessageAction(message) {
      this.dialogVisible = false;
      
      switch(message.linkType) {
        case 'order':
          this.$router.push(`/frontend/order/detail/${message.linkId}`);
          break;
        case 'contract':
          this.$router.push(`/frontend/contract/${message.linkId}`);
          break;
        case 'payment':
          this.$router.push(`/frontend/order/pay/${message.linkId}`);
          break;
        case 'house':
          this.$router.push(`/frontend/house/detail/${message.linkId}`);
          break;
        default:
          break;
      }
    },
    getMessageClass(type) {
      const typeMap = {
        'info': 'icon-info',
        'success': 'icon-success',
        'warning': 'icon-warning',
        'error': 'icon-error',
        'order': 'icon-order',
        'contract': 'icon-contract',
        'payment': 'icon-payment',
        'system': 'icon-info'
      };
      return typeMap[type] || 'icon-info';
    },
    getMessageIcon(type) {
      const iconMap = {
        'info': 'el-icon-info',
        'success': 'el-icon-success',
        'warning': 'el-icon-warning',
        'error': 'el-icon-error',
        'order': 'el-icon-shopping-cart-full',
        'contract': 'el-icon-document-checked',
        'payment': 'el-icon-money',
        'system': 'el-icon-info'
      };
      return iconMap[type] || 'el-icon-info';
    },
    getActionText(linkType) {
      const actionMap = {
        'order': '查看订单',
        'contract': '查看合同',
        'payment': '去支付',
        'house': '查看房源',
      };
      return actionMap[linkType] || '查看详情';
    },
    formatTime(timestamp) {
      if (!timestamp) return '';
      
      const date = new Date(timestamp);
      const now = new Date();
      
      // 计算时间差（毫秒）
      const diff = now - date;
      
      // 如果在24小时内
      if (diff < 86400000) {
        // 如果在1小时内
        if (diff < 3600000) {
          // 如果在1分钟内
          if (diff < 60000) {
            return '刚刚';
          } else {
            return Math.floor(diff / 60000) + '分钟前';
          }
        } else {
          return Math.floor(diff / 3600000) + '小时前';
        }
      } else if (date.getFullYear() === now.getFullYear()) {
        // 如果是今年
        return (date.getMonth() + 1) + '月' + date.getDate() + '日';
      } else {
        // 其他年份
        return date.getFullYear() + '年' + (date.getMonth() + 1) + '月' + date.getDate() + '日';
      }
    },
    formatDateTime(timestamp) {
      if (!timestamp) return '';
      
      const date = new Date(timestamp);
      return date.getFullYear() + '年' + 
             (date.getMonth() + 1) + '月' + 
             date.getDate() + '日 ' + 
             date.getHours().toString().padStart(2, '0') + ':' + 
             date.getMinutes().toString().padStart(2, '0');
    }
  }
}
</script>

<style scoped>
.message-center-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.container {
  max-width: 900px;
  margin: 20px auto;
  padding: 0 20px;
  flex: 1;
}

h2 {
  text-align: center;
  margin-bottom: 20px;
  color: #303133;
}

.message-tools {
  display: flex;
  justify-content: flex-end;
  margin: 10px 0;
}

.messages-container {
  min-height: 400px;
}

.message-item {
  display: flex;
  align-items: flex-start;
  padding: 15px;
  margin-bottom: 10px;
  border-radius: 4px;
  background-color: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
  position: relative;
}

.message-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.message-read {
  opacity: 0.7;
}

.message-read::before {
  display: none;
}

.message-item::before {
  content: "";
  position: absolute;
  top: 20px;
  left: 0;
  width: 4px;
  height: 4px;
  background-color: #f56c6c;
  border-radius: 50%;
}

.message-icon {
  flex: 0 0 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  margin-right: 15px;
}

.icon-info {
  background-color: #409eff;
}

.icon-success {
  background-color: #67c23a;
}

.icon-warning {
  background-color: #e6a23c;
}

.icon-error {
  background-color: #f56c6c;
}

.icon-order {
  background-color: #409eff;
}

.icon-contract {
  background-color: #409eff;
}

.icon-payment {
  background-color: #f56c6c;
}

.message-icon i {
  font-size: 20px;
}

.message-content {
  flex: 1;
  cursor: pointer;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.message-title {
  font-weight: bold;
  color: #303133;
}

.message-time {
  color: #909399;
  font-size: 12px;
}

.message-body {
  color: #606266;
  font-size: 14px;
  line-height: 1.5;
}

.message-actions {
  margin-left: 10px;
}

.el-dropdown-link {
  cursor: pointer;
  color: #909399;
  font-size: 16px;
}

.pagination {
  margin-top: 30px;
  text-align: center;
}

.message-detail .detail-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.message-detail .detail-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.message-detail .detail-time {
  color: #909399;
  font-size: 14px;
}

.message-detail .detail-content {
  font-size: 14px;
  color: #606266;
  line-height: 1.8;
  margin-bottom: 20px;
}

.message-detail .detail-action {
  text-align: center;
  margin-top: 30px;
}
</style> 