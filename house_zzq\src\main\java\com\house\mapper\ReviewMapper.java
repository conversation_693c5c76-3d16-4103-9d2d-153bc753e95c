package com.house.mapper;

import com.house.pojo.Review;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

/**
 * 评价Mapper接口
 */
@Repository
@Mapper
public interface ReviewMapper {

    /**
     * 保存评价
     * @param review 评价对象
     * @return 影响行数
     */
    @Insert("INSERT INTO tb_review(order_id, order_no, house_id, owner_id, user_id, user_name, user_avatar, " +
            "rating, location_rating, facility_rating, service_rating, value_rating, environment_rating, cleanliness_rating, " +
            "average_rating, content, tags, images, anonymous, create_time, update_time) " +
            "VALUES(#{orderId}, #{orderNo}, #{houseId}, #{ownerId}, #{userId}, #{userName}, #{userAvatar}, " +
            "#{rating}, #{locationRating}, #{facilityRating}, #{serviceRating}, #{valueRating}, #{environmentRating}, " +
            "#{cleanlinessRating}, #{averageRating}, #{content}, #{tags}, #{images}, #{anonymous}, NOW(), NOW())")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insertReview(Review review);

    /**
     * 更新评价统计信息
     * @param houseId 房屋ID
     * @return 影响行数
     */
    @Insert("INSERT INTO tb_review_stats(house_id, total_reviews, average_rating, location_rating, facility_rating, " +
            "service_rating, value_rating, environment_rating, cleanliness_rating, update_time) " +
            "VALUES(#{houseId}, 1, #{averageRating}, #{locationRating}, #{facilityRating}, #{serviceRating}, " +
            "#{valueRating}, #{environmentRating}, #{cleanlinessRating}, NOW()) " +
            "ON DUPLICATE KEY UPDATE " +
            "total_reviews = total_reviews + 1, " +
            "average_rating = (average_rating * total_reviews + #{averageRating}) / (total_reviews + 1), " +
            "location_rating = (location_rating * total_reviews + #{locationRating}) / (total_reviews + 1), " +
            "facility_rating = (facility_rating * total_reviews + #{facilityRating}) / (total_reviews + 1), " +
            "service_rating = (service_rating * total_reviews + #{serviceRating}) / (total_reviews + 1), " +
            "value_rating = (value_rating * total_reviews + #{valueRating}) / (total_reviews + 1), " +
            "environment_rating = (environment_rating * total_reviews + #{environmentRating}) / (total_reviews + 1), " +
            "cleanliness_rating = (cleanliness_rating * total_reviews + #{cleanlinessRating}) / (total_reviews + 1), " +
            "update_time = NOW()")
    int updateReviewStats(Review review);

    /**
     * 更新评价标签统计
     * @param houseId 房屋ID
     * @param tagName 标签名称
     * @return 影响行数
     */
    @Insert("INSERT INTO tb_review_tag_stats(house_id, tag_name, count, update_time) " +
            "VALUES(#{houseId}, #{tagName}, 1, NOW()) " +
            "ON DUPLICATE KEY UPDATE " +
            "count = count + 1, " +
            "update_time = NOW()")
    int updateTagStats(@Param("houseId") Integer houseId, @Param("tagName") String tagName);

    /**
     * 查询房屋评价统计信息
     * @param houseId 房屋ID
     * @return 评价统计信息
     */
    @Select("SELECT house_id as houseId, total_reviews as totalReviews, average_rating as averageRating, " +
            "location_rating as locationRating, facility_rating as facilityRating, " +
            "service_rating as serviceRating, value_rating as valueRating, " +
            "environment_rating as environmentRating, cleanliness_rating as cleanlinessRating " +
            "FROM tb_review_stats WHERE house_id = #{houseId}")
    Map<String, Object> selectReviewStats(Integer houseId);

    /**
     * 查询房屋标签统计信息
     * @param houseId 房屋ID
     * @return 标签统计列表
     */
    @Select("SELECT tag_name as name, count FROM tb_review_tag_stats " +
            "WHERE house_id = #{houseId} ORDER BY count DESC, update_time DESC")
    List<Map<String, Object>> selectTagStats(Integer houseId);

    /**
     * 查询房屋所有评价
     * @param houseId 房屋ID
     * @return 评价列表
     */
    @Select("SELECT * FROM tb_review WHERE house_id = #{houseId} ORDER BY create_time DESC")
    List<Review> selectReviewsByHouseId(Integer houseId);

    /**
     * 查询房东的所有评价
     * @param ownerId 房东ID
     * @return 评价列表
     */
    @Select("SELECT * FROM tb_review WHERE owner_id = #{ownerId} ORDER BY create_time DESC")
    List<Review> selectReviewsByOwnerId(Integer ownerId);

    /**
     * 根据订单ID查询评价
     * @param orderId 订单ID
     * @return 评价对象
     */
    @Select("SELECT * FROM tb_review WHERE order_id = #{orderId}")
    Review selectReviewByOrderId(Integer orderId);

    /**
     * 房东回复评价
     * @param reviewId 评价ID
     * @param reply 回复内容
     * @return 影响行数
     */
    @Update("UPDATE tb_review SET reply = #{reply}, reply_time = NOW(), update_time = NOW() " +
            "WHERE id = #{reviewId}")
    int replyReview(@Param("reviewId") Integer reviewId, @Param("reply") String reply);

    /**
     * 删除评价
     * @param reviewId 评价ID
     * @return 影响行数
     */
    @Delete("DELETE FROM tb_review WHERE id = #{reviewId}")
    int deleteReview(Integer reviewId);
} 