<template>
  <div class="notification-container">
    <transition-group name="notification-list" tag="div">
      <div 
        v-for="notification in visibleNotifications" 
        :key="notification.id"
        class="notification-item"
        :class="getNotificationClass(notification.type)">
        <div class="notification-content">
          <div class="notification-icon">
            <i :class="getNotificationIcon(notification.type)"></i>
          </div>
          <div class="notification-body">
            <div class="notification-title">{{ notification.title }}</div>
            <div class="notification-message">{{ notification.message }}</div>
            <div class="notification-time">{{ formatTime(notification.timestamp) }}</div>
          </div>
          <div class="notification-close" @click="hideNotification(notification.id)">
            <i class="el-icon-close"></i>
          </div>
        </div>
      </div>
    </transition-group>
  </div>
</template>

<script>
import { getBackstageUser } from '../../utils/auth'

export default {
  name: 'BackstageWebSocketNotification',
  data() {
    return {
      socket: null,
      connected: false,
      notifications: [],
      visibleNotifications: [],
      unreadCount: 0,
      maxVisibleNotifications: 3,
      reconnectCount: 0,
      reconnectTimer: null,
      heartbeatTimer: null,
      manualClosed: false,
      needReconnect: true // 默认需要重连，在组件销毁时设为false
    }
  },
  created() {
    // 初始化WebSocket连接
    this.initWebSocket();
    
    // 添加页面可见性变化监听
    document.addEventListener('visibilitychange', this.handleVisibilityChange);
    
    // 添加页面刷新前的处理
    window.addEventListener('beforeunload', this.handleBeforeUnload);
  },
  beforeDestroy() {
    // 移除事件监听
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    window.removeEventListener('beforeunload', this.handleBeforeUnload);
    
    // 关闭WebSocket连接
    this.needReconnect = false; // 组件销毁前设置不需要重连
    this.closeWebSocket();
  },
  methods: {
    initWebSocket() {
      // 获取用户信息
      const userData = getBackstageUser();
      if (!userData || !userData.userInfo || !userData.token) {
        if (process.env.NODE_ENV === 'development') {
          console.log('后台用户未登录，不初始化WebSocket');
        }
        return;
      }
      
      const userId = userData.userInfo.id;
      const token = userData.token;
      
      try {
        // 检查是否已经有连接，如果有则关闭
        if (this.socket && this.socket.readyState !== WebSocket.CLOSED) {
          // 如果已经连接，不重复连接
          if (this.socket.readyState === WebSocket.OPEN) {
            if (process.env.NODE_ENV === 'development') {
              console.log('后台WebSocket已经连接，无需重新连接');
            }
            return;
          }
          // 如果正在连接中，也不重复连接
          if (this.socket.readyState === WebSocket.CONNECTING) {
            if (process.env.NODE_ENV === 'development') {
              console.log('后台WebSocket正在连接中，无需重新发起连接');
            }
            return;
          }
          // 关闭现有连接
          this.closeWebSocket();
        }
        
        // 重置手动关闭标志
        this.manualClosed = false;
        
        // 使用直接的WebSocket连接URL
        const encodedToken = encodeURIComponent(token);
        const wsUrl = `ws://localhost:9002/ws/chat/${userId}?houseId=0&landlordId=${userId}&token=${encodedToken}`;
        
        if (process.env.NODE_ENV === 'development') {
          console.log('尝试连接后台WebSocket:', wsUrl);
        }
        
        this.socket = new WebSocket(wsUrl);
        
        // WebSocket连接成功
        this.socket.onopen = () => {
          if (process.env.NODE_ENV === 'development') {
            console.log('后台WebSocket连接成功');
          }
          this.connected = true;
          // 连接成功后清除重连计时器
          if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
          }
          // 发送心跳
          this.startHeartbeat();
          
          // 将连接保存到全局Vue实例中，方便其他组件访问
          if (!this.$root.$data.backstageWebSocket || 
              this.$root.$data.backstageWebSocket.readyState !== WebSocket.OPEN) {
            this.$root.$data.backstageWebSocket = this.socket;
          }
          
          // 发送一个测试消息到服务器
          setTimeout(() => {
            if (this.socket && this.socket.readyState === WebSocket.OPEN) {
              const testMsg = {
                type: 'ping',
                timestamp: Date.now(),
                content: '测试连接'
              };
              this.socket.send(JSON.stringify(testMsg));
              console.log('发送测试消息:', testMsg);
            }
          }, 1000);
        };
        
        // 接收消息
        this.socket.onmessage = (event) => {
          if (process.env.NODE_ENV === 'development') {
            console.log('收到后台WebSocket消息:', event.data);
          }
          this.handleMessage(event.data);
          
          // 触发全局事件，通知其他组件
          try {
            const message = JSON.parse(event.data);
            this.$root.$emit('backstage-new-message', message);
            
            // 如果是聊天消息，直接显示通知
            if (message.type === 'chat') {
              this.$notify({
                title: '新的聊天消息',
                message: `收到消息: ${message.content}`,
                type: 'info',
                duration: 5000
              });
            }
          } catch (error) {
            console.error('解析消息失败:', error);
          }
        };
        
        // 连接关闭
        this.socket.onclose = (event) => {
          if (process.env.NODE_ENV === 'development') {
            console.log('后台WebSocket连接关闭，状态码:', event.code);
          }
          this.connected = false;
          this.stopHeartbeat();
          
          // 只要用户仍在登录状态，无论何种原因断开，都尝试立即重连
          if (!this.manualClosed) {
            if (process.env.NODE_ENV === 'development') {
              console.log('后台WebSocket连接断开，立即尝试重新连接');
            }
            // 不使用延迟重连策略，直接重新连接
            this.initWebSocket();
          }
        };
        
        // 连接错误
        this.socket.onerror = (error) => {
          console.error('后台WebSocket连接错误');
          this.connected = false;
          // 连接错误时也尝试重连
          this.reconnect();
        };
      } catch (error) {
        console.error('初始化后台WebSocket失败:', error);
        // 初始化失败时也尝试重连
        this.reconnect();
      }
    },
    
    reconnect() {
      // 立即重新连接，不再使用延迟和计数
      console.log('正在重新连接后台WebSocket...');
      this.initWebSocket();
    },
    
    startHeartbeat() {
      // 每15秒发送一次心跳，确保连接保持活跃
      this.heartbeatTimer = setInterval(() => {
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
          // 发送心跳消息
          if (process.env.NODE_ENV === 'development') {
            console.log("发送后台WebSocket心跳");
          }
          this.socket.send(JSON.stringify({type: 'heartbeat', timestamp: Date.now()}));
        } else if (this.socket && this.socket.readyState !== WebSocket.CONNECTING && !this.manualClosed) {
          // 如果连接已断开但不是手动关闭，尝试重新连接
          if (process.env.NODE_ENV === 'development') {
            console.log("心跳检测到后台连接已断开，尝试重新连接");
          }
          this.initWebSocket();
        }
      }, 15000);
    },
    
    stopHeartbeat() {
      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer);
        this.heartbeatTimer = null;
      }
    },
    
    closeWebSocket() {
      this.manualClosed = true; // 标记为手动关闭
      this.stopHeartbeat();
      
      // 清除重连定时器
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
      }
      
      // 关闭连接
      if (this.socket) {
        // 添加关闭原因
        try {
          this.socket.close(1000, "手动关闭连接"); // 1000表示正常关闭
        } catch (e) {
          console.error("关闭后台WebSocket连接时出错:", e);
        }
        this.socket = null;
        this.connected = false;
        console.log("后台WebSocket连接已手动关闭");
      }
    },
    
    handleMessage(data) {
      try {
        // 解析后端发送的消息
        const message = JSON.parse(data);
        
        // 触发全局事件，通知其他组件（如ChatMessages）
        this.$root.$emit('backstage-new-message', message);
        
        // 如果是聊天消息，特殊处理
        if (message.type === 'chat') {
          // 获取用户信息
          const userData = getBackstageUser();
          const landlordId = userData && userData.userInfo ? userData.userInfo.id : null;
          
          // 如果当前用户是接收者，显示通知
          if (message.toId == landlordId) {
            const notification = {
              id: message.id || new Date().getTime(),
              title: '新的咨询消息',
              message: message.content,
              type: 'info',
              timestamp: message.timestamp || new Date().getTime(),
              read: false,
              fromId: message.fromId,
              houseId: message.houseId
            };
            
            // 添加到通知列表
            this.notifications.unshift(notification);
            
            // 更新未读计数
            this.unreadCount++;
            
            // 显示通知
            this.showNotification(notification);
            
            return; // 已处理聊天消息，不再继续处理
          }
        }
        
        // 处理其他类型的通知消息
        const notification = {
          id: message.id || new Date().getTime(),
          title: message.title || '系统通知',
          message: message.content,
          type: message.type || 'info',
          timestamp: message.timestamp || new Date().getTime(),
          read: false
        };
        
        // 添加到通知列表
        this.notifications.unshift(notification);
        
        // 更新未读计数
        this.unreadCount++;
        
        // 显示通知
        this.showNotification(notification);
      } catch (error) {
        console.error('解析后台WebSocket消息失败:', error);
      }
    },
    
    showNotification(notification) {
      // 添加到可见通知列表
      this.visibleNotifications.unshift(notification);
      
      // 限制最大显示数量
      if (this.visibleNotifications.length > this.maxVisibleNotifications) {
        this.visibleNotifications.pop();
      }
      
      // 设置自动隐藏定时器
      setTimeout(() => {
        this.hideNotification(notification.id);
      }, 5000);
    },
    
    hideNotification(id) {
      // 从可见通知列表中移除
      const index = this.visibleNotifications.findIndex(n => n.id === id);
      if (index !== -1) {
        this.visibleNotifications.splice(index, 1);
      }
    },
    
    getNotificationClass(type) {
      switch (type) {
        case 'success':
          return 'notification-success';
        case 'warning':
          return 'notification-warning';
        case 'error':
          return 'notification-error';
        case 'info':
        default:
          return 'notification-info';
      }
    },
    
    getNotificationIcon(type) {
      switch (type) {
        case 'success':
          return 'el-icon-success';
        case 'warning':
          return 'el-icon-warning';
        case 'error':
          return 'el-icon-error';
        case 'info':
        default:
          return 'el-icon-info';
      }
    },
    
    formatTime(timestamp) {
      const date = new Date(timestamp);
      return date.toLocaleTimeString();
    },
    
    handleVisibilityChange() {
      if (document.visibilityState === 'visible') {
        // 页面变为可见时，检查连接状态
        if (!this.connected && !this.manualClosed) {
          console.log('页面可见，检测到后台WebSocket未连接，尝试重新连接');
          this.initWebSocket();
        }
      }
    },
    
    handleBeforeUnload() {
      // 页面刷新前关闭连接
      this.closeWebSocket();
    }
  }
}
</script>

<style scoped>
.notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  width: 320px;
}

.notification-item {
  margin-bottom: 10px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  background-color: #fff;
  padding: 15px;
  transition: all 0.3s;
}

.notification-content {
  display: flex;
  align-items: flex-start;
}

.notification-icon {
  margin-right: 10px;
  font-size: 20px;
}

.notification-body {
  flex: 1;
}

.notification-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.notification-message {
  font-size: 14px;
  color: #606266;
  margin-bottom: 5px;
}

.notification-time {
  font-size: 12px;
  color: #909399;
}

.notification-close {
  cursor: pointer;
  color: #909399;
}

.notification-close:hover {
  color: #606266;
}

.notification-success .notification-icon {
  color: #67C23A;
}

.notification-warning .notification-icon {
  color: #E6A23C;
}

.notification-error .notification-icon {
  color: #F56C6C;
}

.notification-info .notification-icon {
  color: #409EFF;
}

.notification-list-enter-active, .notification-list-leave-active {
  transition: all 0.3s;
}

.notification-list-enter, .notification-list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style> 