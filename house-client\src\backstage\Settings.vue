<template>
  <div class="settings">
    <div class="page-header">
      <h2>系统设置</h2>
    </div>

    <el-tabs v-model="activeTab">
      <el-tab-pane label="基本设置" name="basic">
        <div class="settings-form">
          <el-form :model="basicForm" :rules="basicRules" ref="basicForm" label-width="120px">
            <el-form-item label="系统名称" prop="systemName">
              <el-input v-model="basicForm.systemName"></el-input>
            </el-form-item>
            <el-form-item label="系统描述" prop="systemDescription">
              <el-input type="textarea" :rows="3" v-model="basicForm.systemDescription"></el-input>
            </el-form-item>
            <el-form-item label="联系电话" prop="contactPhone">
              <el-input v-model="basicForm.contactPhone"></el-input>
            </el-form-item>
            <el-form-item label="联系邮箱" prop="contactEmail">
              <el-input v-model="basicForm.contactEmail"></el-input>
            </el-form-item>
            <el-form-item label="系统公告" prop="announcement">
              <el-input type="textarea" :rows="4" v-model="basicForm.announcement"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveBasicSettings">保存设置</el-button>
              <el-button @click="resetForm('basicForm')">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>

      <el-tab-pane label="通知设置" name="notification">
        <div class="settings-form">
          <el-form :model="notificationForm" ref="notificationForm" label-width="120px">
            <el-form-item label="邮件通知">
              <el-switch v-model="notificationForm.emailNotification"></el-switch>
            </el-form-item>
            <el-form-item label="短信通知">
              <el-switch v-model="notificationForm.smsNotification"></el-switch>
            </el-form-item>
            <el-form-item label="系统消息">
              <el-switch v-model="notificationForm.systemNotification"></el-switch>
            </el-form-item>
            <el-form-item label="订单通知">
              <el-switch v-model="notificationForm.orderNotification"></el-switch>
            </el-form-item>
            <el-form-item label="评价通知">
              <el-switch v-model="notificationForm.reviewNotification"></el-switch>
            </el-form-item>
            <el-form-item label="租期提醒">
              <el-switch v-model="notificationForm.leaseReminder"></el-switch>
            </el-form-item>
            <el-form-item label="提前天数" v-if="notificationForm.leaseReminder">
              <el-input-number v-model="notificationForm.reminderDays" :min="1" :max="30"></el-input-number>
              <span class="unit">天</span>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveNotificationSettings">保存设置</el-button>
              <el-button @click="resetForm('notificationForm')">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>

      <el-tab-pane label="安全设置" name="security" v-if="isAdmin">
        <div class="settings-form">
          <el-form :model="securityForm" ref="securityForm" label-width="120px">
            <el-form-item label="登录失败锁定">
              <el-switch v-model="securityForm.loginLock"></el-switch>
            </el-form-item>
            <el-form-item label="失败次数" v-if="securityForm.loginLock">
              <el-input-number v-model="securityForm.maxFailures" :min="3" :max="10"></el-input-number>
              <span class="unit">次</span>
            </el-form-item>
            <el-form-item label="锁定时间" v-if="securityForm.loginLock">
              <el-input-number v-model="securityForm.lockTime" :min="5" :max="60"></el-input-number>
              <span class="unit">分钟</span>
            </el-form-item>
            <el-form-item label="密码过期时间">
              <el-input-number v-model="securityForm.passwordExpiry" :min="0" :max="365"></el-input-number>
              <span class="unit">天 (0表示不过期)</span>
            </el-form-item>
            <el-form-item label="密码复杂度">
              <el-select v-model="securityForm.passwordComplexity">
                <el-option label="低" value="low"></el-option>
                <el-option label="中" value="medium"></el-option>
                <el-option label="高" value="high"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveSecuritySettings">保存设置</el-button>
              <el-button @click="resetForm('securityForm')">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>

      <el-tab-pane label="备份恢复" name="backup" v-if="isAdmin">
        <div class="settings-form">
          <el-form label-width="120px">
            <el-form-item label="数据备份">
              <el-button type="primary" @click="backupData" :loading="backupLoading">立即备份</el-button>
              <span class="hint">上次备份时间: {{ lastBackupTime || '从未备份' }}</span>
            </el-form-item>
            <el-form-item label="自动备份">
              <el-switch v-model="backupForm.autoBackup"></el-switch>
            </el-form-item>
            <el-form-item label="备份频率" v-if="backupForm.autoBackup">
              <el-select v-model="backupForm.backupFrequency">
                <el-option label="每天" value="daily"></el-option>
                <el-option label="每周" value="weekly"></el-option>
                <el-option label="每月" value="monthly"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="备份历史">
              <el-table :data="backupHistory" style="width: 100%" v-loading="historyLoading">
                <el-table-column prop="date" label="备份时间" width="180"></el-table-column>
                <el-table-column prop="size" label="大小" width="120"></el-table-column>
                <el-table-column prop="type" label="类型" width="120"></el-table-column>
                <el-table-column label="操作">
                  <template slot-scope="scope">
                    <el-button size="mini" type="primary" @click="restoreBackup(scope.row)">恢复</el-button>
                    <el-button size="mini" type="success" @click="downloadBackup(scope.row)">下载</el-button>
                    <el-button size="mini" type="danger" @click="deleteBackup(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-form-item>
          </el-form>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import { getBackstageUser, isAdmin } from '../utils/auth'
import settingsApi from '../api/settings.js'

export default {
  name: 'Settings',
  data() {
    return {
      activeTab: 'basic',
      isAdmin: false,
      basicForm: {
        systemName: '租房平台管理系统',
        systemDescription: '为房东和租客提供高效、便捷的租房服务',
        contactPhone: '',
        contactEmail: '',
        announcement: ''
      },
      basicRules: {
        systemName: [
          { required: true, message: '请输入系统名称', trigger: 'blur' }
        ],
        contactEmail: [
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      },
      notificationForm: {
        emailNotification: true,
        smsNotification: false,
        systemNotification: true,
        orderNotification: true,
        reviewNotification: true,
        leaseReminder: true,
        reminderDays: 7
      },
      securityForm: {
        loginLock: true,
        maxFailures: 5,
        lockTime: 30,
        passwordExpiry: 90,
        passwordComplexity: 'medium'
      },
      backupForm: {
        autoBackup: false,
        backupFrequency: 'weekly'
      },
      backupLoading: false,
      historyLoading: false,
      lastBackupTime: '',
      backupHistory: []
    }
  },
  created() {
    this.checkUserRole()
    this.loadSettings()
  },
  methods: {
    checkUserRole() {
      this.isAdmin = isAdmin()
    },
    loadSettings() {
      // 加载基本设置
      settingsApi.getBasicSettings().then(res => {
        if (res.data.flag) {
          this.basicForm = { ...this.basicForm, ...res.data.data }
        }
      })

      // 加载通知设置
      settingsApi.getNotificationSettings().then(res => {
        if (res.data.flag) {
          this.notificationForm = { ...this.notificationForm, ...res.data.data }
        }
      })

      // 如果是管理员，加载安全设置和备份信息
      if (this.isAdmin) {
        settingsApi.getSecuritySettings().then(res => {
          if (res.data.flag) {
            this.securityForm = { ...this.securityForm, ...res.data.data }
          }
        })

        this.loadBackupHistory()
      }
    },
    saveBasicSettings() {
      this.$refs.basicForm.validate((valid) => {
        if (valid) {
          settingsApi.saveBasicSettings(this.basicForm).then(res => {
            if (res.data.flag) {
              this.$message.success('基本设置保存成功')
            } else {
              this.$message.error('基本设置保存失败')
            }
          }).catch(() => {
            this.$message.error('基本设置保存失败')
          })
        } else {
          return false
        }
      })
    },
    saveNotificationSettings() {
      settingsApi.saveNotificationSettings(this.notificationForm).then(res => {
        if (res.data.flag) {
          this.$message.success('通知设置保存成功')
        } else {
          this.$message.error('通知设置保存失败')
        }
      }).catch(() => {
        this.$message.error('通知设置保存失败')
      })
    },
    saveSecuritySettings() {
      settingsApi.saveSecuritySettings(this.securityForm).then(res => {
        if (res.data.flag) {
          this.$message.success('安全设置保存成功')
        } else {
          this.$message.error('安全设置保存失败')
        }
      }).catch(() => {
        this.$message.error('安全设置保存失败')
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    loadBackupHistory() {
      this.historyLoading = true
      settingsApi.getBackupHistory().then(res => {
        if (res.data.flag) {
          this.backupHistory = res.data.data.history || []
          this.lastBackupTime = res.data.data.lastBackupTime || ''
        } else {
          this.$message.error('获取备份历史失败')
        }
        this.historyLoading = false
      }).catch(() => {
        this.$message.error('获取备份历史失败')
        this.historyLoading = false
      })
    },
    backupData() {
      this.backupLoading = true
      settingsApi.createBackup().then(res => {
        if (res.data.flag) {
          this.$message.success('数据备份成功')
          this.lastBackupTime = new Date().toLocaleString()
          this.loadBackupHistory()
        } else {
          this.$message.error('数据备份失败')
        }
        this.backupLoading = false
      }).catch(() => {
        this.$message.error('数据备份失败')
        this.backupLoading = false
      })
    },
    restoreBackup(backup) {
      this.$confirm(`确定要恢复到 ${backup.date} 的备份吗? 当前数据将被覆盖。`, '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        settingsApi.restoreBackup(backup.id).then(res => {
          if (res.data.flag) {
            this.$message.success('数据恢复成功，系统将在3秒后刷新')
            setTimeout(() => {
              window.location.reload()
            }, 3000)
          } else {
            this.$message.error('数据恢复失败')
          }
        }).catch(() => {
          this.$message.error('数据恢复失败')
        })
      }).catch(() => {})
    },
    downloadBackup(backup) {
      window.open(`/api/settings/backup/download/${backup.id}`)
    },
    deleteBackup(backup) {
      this.$confirm(`确定要删除 ${backup.date} 的备份吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        settingsApi.deleteBackup(backup.id).then(res => {
          if (res.data.flag) {
            this.$message.success('备份删除成功')
            this.loadBackupHistory()
          } else {
            this.$message.error('备份删除失败')
          }
        }).catch(() => {
          this.$message.error('备份删除失败')
        })
      }).catch(() => {})
    }
  }
}
</script>

<style scoped>
.settings {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.settings-form {
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-top: 20px;
}

.unit {
  margin-left: 10px;
  color: #606266;
}

.hint {
  margin-left: 15px;
  color: #909399;
  font-size: 14px;
}
</style> 