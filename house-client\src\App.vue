<template>
  <div id="app">
    <router-view/>
  </div>
</template>

<script>
import jwt_decode from "jwt-decode";
import {getUser, removeUser} from '@/utils/auth'
import { Message } from 'element-ui';

export default {
  name: "app",
  created() {
    // 检查token是否存在
    const userData = getUser();
    if (userData && userData.token) {
      try {
        // 解析token
        const decode = jwt_decode(userData.token);
        
        // 检查token是否过期
        if (this.isTokenExpired(decode)) {
          // token已过期，清除用户信息并更新状态
          console.log('Token已过期，清除登录状态');
          removeUser();
          this.$store.dispatch("setIsAutnenticated", false);
          this.$store.dispatch("setUser", {});
          
          // 如果当前不在登录页，则跳转到登录页
          if (this.$route && !this.$route.path.includes('/login')) {
            Message.error('暂未登录，请重新登录');
            this.$router.push('/frontend/login');
          }
        } else {
          // token有效，更新认证状态
          this.$store.dispatch("setIsAutnenticated", !this.isEmpty(decode));
          this.$store.dispatch("setUser", decode);
        }
      } catch (error) {
        console.error('Token解析失败:', error);
        // token解析失败，清除用户信息
        removeUser();
        this.$store.dispatch("setIsAutnenticated", false);
        this.$store.dispatch("setUser", {});
        
        // 提示用户重新登录
        if (this.$route && !this.$route.path.includes('/login')) {
          Message.error('登录信息无效，请重新登录');
          this.$router.push('/frontend/login');
        }
      }
    }
  },
  methods: {
    isEmpty(value) {
      return (
        value === undefined ||
        value === null ||
        (typeof value === "object" && Object.keys(value).length === 0) ||
        (typeof value === "string" && value.trim().length === 0)
      );
    },
    isTokenExpired(decodedToken) {
      if (!decodedToken.exp) {
        return false;
      }
      
      // 获取当前时间（秒）
      const currentTime = Math.floor(Date.now() / 1000);
      
      // 如果过期时间小于当前时间，则token已过期
      return decodedToken.exp < currentTime;
    }
  }
};
</script>

<style>
html,
body,
#app {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
}
</style>
