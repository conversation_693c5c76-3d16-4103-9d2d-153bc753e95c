package com.house;

import com.house.websocket.WebSocketChatServer;
import com.house.websocket.WebSocketServer;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.orm.jpa.HibernateJpaAutoConfiguration;
import org.springframework.context.ConfigurableApplicationContext;

@SpringBootApplication
@MapperScan({"com.house.dao", "com.house.mapper"})
public class HouseApplication {

    public static void main(String[] args) {
        System.out.println("=== 房屋租赁平台启动中... ===");
        ConfigurableApplicationContext context = SpringApplication.run(HouseApplication.class, args);
        
        // 设置WebSocket的ApplicationContext
        WebSocketServer.setApplicationContext(context);
        WebSocketChatServer.setApplicationContext(context);
        
        // 打印应用信息
        System.out.println("=== 房屋租赁平台启动完成 ===");
        System.out.println("JWT拦截器是否已注册：" + (context.containsBean("jwtInterceptor") ? "是" : "否"));
        System.out.println("日志拦截器是否已注册：" + (context.containsBean("loggingInterceptor") ? "是" : "否"));
        System.out.println("WebMVC配置是否生效：" + (context.containsBean("webMvcConfig") ? "是" : "否"));
    }
}
