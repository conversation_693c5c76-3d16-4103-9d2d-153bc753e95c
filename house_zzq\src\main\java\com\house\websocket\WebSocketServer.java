package com.house.websocket;

import com.alibaba.fastjson.JSON;
import com.house.dao.UserListDao;
import com.house.pojo.UserList;
import com.house.service.UserService;
import com.house.utils.JwtUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import org.springframework.web.context.support.SpringBeanAutowiringSupport;

import jakarta.websocket.*;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * WebSocket服务端处理类
 * @ServerEndpoint 注解是一个类层次的注解，用于声明一个类是WebSocket的服务器端
 * 其中value是访问的URL
 */
@Slf4j
@Component
@ServerEndpoint("/ws/{system}/{userId}")
public class WebSocketServer {

    /**
     * 用于解决WebSocket中无法注入的问题
     */
    private static ApplicationContext applicationContext;
    
    /**
     * 设置应用上下文
     */
    public static void setApplicationContext(ApplicationContext context) {
        WebSocketServer.applicationContext = context;
    }
    
    /**
     * 获取UserListDao的实例
     */
    private static UserListDao getUserListDao() {
        if (applicationContext != null) {
            return applicationContext.getBean(UserListDao.class);
        }
        return null;
    }

    /**
     * 记录当前在线连接数
     */
    private static AtomicInteger onlineCount = new AtomicInteger(0);

    /**
     * 存放所有在线的客户端
     * key是系统类型:用户ID，value是WebSocket会话
     */
    private static Map<String, Session> clients = new ConcurrentHashMap<>();
    
    /**
     * 存放用户ID和用户名的映射
     * key是系统类型:用户ID，value是用户名
     */
    private static Map<String, String> userNames = new ConcurrentHashMap<>();
    
    /**
     * 存放用户ID和用户类型的映射
     * key是系统类型:用户ID，value是用户类型(1-管理员，2-用户，3-房东)
     */
    private static Map<String, Integer> userTypes = new ConcurrentHashMap<>();

    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("system") String system, @PathParam("userId") String userId) {
        // 获取用户信息
        UserInfo userInfo = getUserInfo(userId);
        
        // 生成唯一键
        String key = system + ":" + userId;
        
        // 将新用户添加到clients中
        clients.put(key, session);
        // 保存用户ID和用户名的映射
        userNames.put(key, userInfo.name);
        // 保存用户ID和用户类型的映射
        userTypes.put(key, userInfo.type);
        // 在线数加1
        addOnlineCount();
        
        // 获取用户类型描述
        String typeDesc = getUserTypeDesc(userInfo.type);
        
        // 只记录重要用户的连接信息
        if (userInfo.type == 1 || userInfo.type == 3) { // 管理员或房东
            log.info("用户{}（{} - {}）已连接，当前在线人数：{}", 
                    userInfo.name, system, typeDesc, getOnlineCount());
        }
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose(@PathParam("system") String system, @PathParam("userId") String userId) {
        // 生成唯一键
        String key = system + ":" + userId;
        
        // 获取用户名和类型
        String userName = userNames.getOrDefault(key, userId);
        Integer userType = userTypes.getOrDefault(key, 0);
        String typeDesc = getUserTypeDesc(userType);
        
        // 从clients中移除用户
        clients.remove(key);
        // 从用户名映射中移除
        userNames.remove(key);
        // 从用户类型映射中移除
        userTypes.remove(key);
        // 在线数减1
        subOnlineCount();
        
        // 只记录重要用户的断开连接信息
        if (userType == 1 || userType == 3) { // 管理员或房东
            log.info("用户{}（{} - {}）已断开连接，当前在线人数：{}", 
                    userName, system, typeDesc, getOnlineCount());
        }
    }

    /**
     * 用户信息类
     */
    private static class UserInfo {
        String name;
        Integer type;
        
        public UserInfo(String name, Integer type) {
            this.name = name;
            this.type = type != null ? type : 0;
        }
    }
    
    /**
     * 获取用户类型描述
     */
    private static String getUserTypeDesc(Integer type) {
        if (type == null) return "未知";
        
        switch (type) {
            case 1: return "管理员";
            case 2: return "用户";
            case 3: return "房东";
            default: return "未知";
        }
    }

    /**
     * 获取用户信息
     * @param userId 用户ID
     * @return 用户信息对象，包含用户名和类型
     */
    private UserInfo getUserInfo(String userId) {
        try {
            // 确保userId是有效的数字
            Integer userIdInt;
            try {
                userIdInt = Integer.parseInt(userId);
            } catch (NumberFormatException e) {
                log.debug("无效的用户ID格式: {}", userId);
                return new UserInfo("游客-" + userId, 0);
            }
            
            // 从数据库查询用户信息
            UserListDao userListDao = getUserListDao();
            if (userListDao != null) {
                // 先查询用户自身ID
                UserList userInfo = userListDao.findUserListById(userIdInt);
                if (userInfo != null && userInfo.getName() != null) {
                    // 只记录管理员和房东的查找信息
                    if (userInfo.getType() == 1 || userInfo.getType() == 3) {
                        log.info("通过ID直接找到用户: {}", userInfo.getName());
                    }
                    return new UserInfo(userInfo.getName(), userInfo.getType());
                }
                
                // 再尝试查询关联的用户ID
                userInfo = userListDao.selectUserInfoByCondition(null, userIdInt, null);
                if (userInfo != null && userInfo.getName() != null) {
                    return new UserInfo(userInfo.getName(), userInfo.getType());
                }
                
                // 最后尝试直接查询ID
                userInfo = userListDao.selectUserInfoByCondition(null, null, userIdInt);
                if (userInfo != null && userInfo.getName() != null) {
                    return new UserInfo(userInfo.getName(), userInfo.getType());
                }
                
                log.debug("在数据库中未找到用户ID: {}", userId);
            } else {
                log.warn("无法获取UserListDao实例");
            }
        } catch (Exception e) {
            log.warn("获取用户信息失败，使用用户ID代替");
        }
        
        // 如果获取失败，返回用户ID作为备用
        return new UserInfo("用户-" + userId, 0);
    }

    /**
     * 收到客户端消息后调用的方法
     */
    @OnMessage
    public void onMessage(String message, Session session, @PathParam("system") String system, @PathParam("userId") String userId) {
        // 生成唯一键
        String key = system + ":" + userId;
        
        try {
            // 获取用户名和类型
            String userName = userNames.getOrDefault(key, userId);
            Integer userType = userTypes.getOrDefault(key, 0);
            String typeDesc = getUserTypeDesc(userType);
            
            // 尝试解析消息
            Map<String, Object> msgMap = JSON.parseObject(message, Map.class);
            
            // 处理心跳消息
            if (msgMap.containsKey("type") && "heartbeat".equals(msgMap.get("type"))) {
                // 心跳消息不记录日志，直接返回
                return;
            }
            
            // 只记录重要用户的消息
            if (userType == 1 || userType == 3) { // 管理员或房东
                log.info("收到来自用户{}（{} - {}）的消息：{}", 
                        userName, system, typeDesc, message);
            }
            
            // 回复一条消息
            sendMessage(session, buildMessage("消息已收到", "服务器已收到您的消息", "success"));
        } catch (Exception e) {
            // 如果解析失败，按普通字符串处理
            String userName = userNames.getOrDefault(key, userId);
            Integer userType = userTypes.getOrDefault(key, 0);
            
            // 只记录重要用户的消息
            if (userType == 1 || userType == 3) { // 管理员或房东
                log.info("收到来自用户{}的消息（解析失败）：{}", userName, message);
            }
            
            sendMessage(session, buildMessage("消息已收到", "服务器已收到您的消息", "success"));
        }
    }

    /**
     * 发生错误时调用的方法
     */
    @OnError
    public void onError(Session session, Throwable error, @PathParam("system") String system, @PathParam("userId") String userId) {
        // 生成唯一键
        String key = system + ":" + userId;
        
        String userName = userNames.getOrDefault(key, userId);
        Integer userType = userTypes.getOrDefault(key, 0);
        String typeDesc = getUserTypeDesc(userType);
        
        log.error("WebSocket错误：用户{}（{} - {}）, 原因：{}", 
                userName, system, typeDesc, error.getMessage());
    }

    /**
     * 服务端主动推送消息
     */
    public void sendMessage(Session session, String message) {
        try {
            session.getBasicRemote().sendText(message);
        } catch (IOException e) {
            log.error("发送消息出错：{}", e.getMessage());
        }
    }

    /**
     * 向指定用户发送消息对象
     */
    public void sendMessage(String system, String userId, MessageDTO messageDTO) {
        String message = JSON.toJSONString(messageDTO);
        sendMessageToUser(system, userId, message);
    }
    
    /**
     * 向指定用户发送消息对象（兼容旧版本，默认发送到前台系统）
     */
    public void sendMessage(String userId, MessageDTO messageDTO) {
        sendMessage("frontend", userId, messageDTO);
    }

    /**
     * 向指定用户发送消息
     */
    public static void sendMessageToUser(String system, String userId, String message) {
        // 生成唯一键
        String key = system + ":" + userId;
        
        Session session = clients.get(key);
        if (session != null && session.isOpen()) {
            try {
                String userName = userNames.getOrDefault(key, userId);
                Integer userType = userTypes.getOrDefault(key, 0);
                
                // 只记录重要用户的消息发送
                if (userType == 1 || userType == 3) { // 管理员或房东
                    log.debug("向用户{}发送消息", userName);
                }
                
                session.getBasicRemote().sendText(message);
            } catch (IOException e) {
                String userName = userNames.getOrDefault(key, userId);
                log.error("发送消息给用户{}失败：{}", userName, e.getMessage());
            }
        } else {
            String userName = userNames.getOrDefault(key, userId);
            Integer userType = userTypes.getOrDefault(key, 0);
            
            // 只记录重要用户的消息发送失败
            if (userType == 1 || userType == 3) { // 管理员或房东
                log.warn("用户{}不在线，无法发送消息", userName);
            }
        }
    }
    
    /**
     * 向指定用户发送消息（兼容旧版本，默认发送到前台系统）
     */
    public static void sendMessageToUser(String userId, String message) {
        sendMessageToUser("frontend", userId, message);
    }

    /**
     * 向所有用户发送消息
     */
    public static void sendMessageToAll(String message) {
        for (Map.Entry<String, Session> entry : clients.entrySet()) {
            String key = entry.getKey();
            Session session = entry.getValue();
            
            if (session != null && session.isOpen()) {
                try {
                    session.getBasicRemote().sendText(message);
                } catch (IOException e) {
                    String userName = userNames.getOrDefault(key, key);
                    log.error("发送全局消息给用户{}失败：{}", userName, e.getMessage());
                }
            }
        }
    }

    /**
     * 向所有用户发送消息对象
     */
    public static void sendMessageToAll(MessageDTO messageDTO) {
        String message = JSON.toJSONString(messageDTO);
        sendMessageToAll(message);
    }

    /**
     * 构建消息对象
     */
    private String buildMessage(String title, String content, String type) {
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setTitle(title);
        messageDTO.setContent(content);
        messageDTO.setType(type);
        messageDTO.setTime(System.currentTimeMillis());
        return JSON.toJSONString(messageDTO);
    }

    /**
     * 获取当前在线人数
     */
    public static int getOnlineCount() {
        return onlineCount.get();
    }

    /**
     * 增加在线人数
     */
    public static void addOnlineCount() {
        onlineCount.incrementAndGet();
    }

    /**
     * 减少在线人数
     */
    public static void subOnlineCount() {
        onlineCount.decrementAndGet();
    }

    /**
     * 检查用户是否在线
     */
    public static boolean isUserOnline(String system, String userId) {
        // 生成唯一键
        String key = system + ":" + userId;
        return clients.containsKey(key);
    }
    
    /**
     * 检查用户是否在线（兼容旧版本，默认检查前台系统）
     */
    public static boolean isUserOnline(String userId) {
        return isUserOnline("frontend", userId);
    }

    /**
     * 获取在线用户ID列表
     */
    public static java.util.List<String> getOnlineUserList() {
        return new java.util.ArrayList<>(clients.keySet());
    }

    /**
     * 获取在线用户名列表
     */
    public static java.util.List<String> getOnlineUserNameList() {
        return new java.util.ArrayList<>(userNames.values());
    }

    /**
     * 获取在线用户详情
     */
    public static java.util.Map<String, String> getOnlineUserDetails() {
        return new java.util.HashMap<>(userNames);
    }
} 