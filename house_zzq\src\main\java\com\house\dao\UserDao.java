package com.house.dao;

import com.house.pojo.User;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;


@Repository
public interface UserDao {

	 User selectByUsernameAndPassword(@Param("username") String username,
									  @Param("password") String password);
	
	 User selectUserById(@Param("id") Integer id);
	 
	 /**
	  * 根据用户名查询用户
	  * @param username 用户名
	  * @return 用户对象，不存在则返回null
	  */
	 User selectByUsername(@Param("username") String username);
	
	 int insertUser(User user);
	
	 int deleteUser(int id);

	 int updateUser(User user);
	

}
