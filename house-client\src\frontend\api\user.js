import request from '../../utils/request'

const api_name = 'user'

export default {
  // 用户登录
  login(data) {
    return request({
      url: `/${api_name}/login`,
      method: 'post',
      data
    })
  },

  // 用户注册
  register(data) {
    return request({
      url: `/${api_name}/adduser`,
      method: 'post',
      data
    })
  },

  // 获取用户信息
  getUserInfo(userId) {
    return request({
      url: `/${api_name}/getuserinfo?userId=${userId}`,
      method: 'get'
    })
  },

  // 更新用户信息
  updateUserInfo(data) {
    return request({
      url: `/${api_name}/updateuser`,
      method: 'post',
      data
    })
  },

  // 修改密码
  changePassword(data) {
    return request({
      url: `/${api_name}/changepassword`,
      method: 'post',
      data
    })
  },

  // 检查用户名是否存在
  checkUsername(username) {
    return request({
      url: `/${api_name}/checkusername?username=${username}`,
      method: 'get'
    })
  }
} 