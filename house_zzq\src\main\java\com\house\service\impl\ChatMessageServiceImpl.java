package com.house.service.impl;

import com.house.dao.ChatMessageDao;
import com.house.pojo.ChatMessage;
import com.house.service.ChatMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 聊天消息Service实现类
 */
@Service
public class ChatMessageServiceImpl implements ChatMessageService {
    
    @Autowired
    private ChatMessageDao chatMessageDao;
    
    @Override
    @Transactional
    public boolean saveChatMessage(ChatMessage chatMessage) {
        // 设置默认值
        if (chatMessage.getMessageType() == null) {
            chatMessage.setMessageType("text");
        }
        if (chatMessage.getReadStatus() == null) {
            chatMessage.setReadStatus(0); // 0-未读
        }
        
        return chatMessageDao.saveChatMessage(chatMessage) > 0;
    }
    
    @Override
    public List<ChatMessage> getChatHistory(Long houseId, Long userId, Long landlordId) {
        return chatMessageDao.getChatHistory(houseId, userId, landlordId);
    }
    
    @Override
    @Transactional
    public boolean markMessagesAsRead(Long toUserId, Long fromUserId) {
        return chatMessageDao.markMessagesAsRead(toUserId, fromUserId) > 0;
    }
    
    @Override
    public int getUnreadMessageCount(Long userId) {
        return chatMessageDao.getUnreadMessageCount(userId);
    }
    
    @Override
    public List<ChatMessage> getLandlordMessages(Long landlordId) {
        return chatMessageDao.getLandlordMessages(landlordId);
    }
    
    @Override
    @Transactional
    public boolean deleteChatHistory(Long houseId, Long userId, Long landlordId) {
        return chatMessageDao.deleteChatHistory(houseId, userId, landlordId) > 0;
    }
} 