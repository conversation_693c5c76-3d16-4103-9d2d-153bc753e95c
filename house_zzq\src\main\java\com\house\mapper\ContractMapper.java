package com.house.mapper;

import com.house.pojo.Contract;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 合同Mapper接口
 */
@Mapper
public interface ContractMapper {

    /**
     * 插入合同
     */
    int insert(Contract contract);

    /**
     * 根据ID查询合同
     */
    Contract selectById(Integer id);

    /**
     * 根据合同编号查询
     */
    Contract selectByContractNo(String contractNo);

    /**
     * 根据订单ID查询
     */
    Contract selectByOrderId(Integer orderId);

    /**
     * 更新合同状态
     */
    int updateStatus(@Param("id") Integer id, @Param("status") String status);

    /**
     * 更新租客签署状态
     */
    int signByTenant(@Param("id") Integer id, @Param("tenantId") Integer tenantId, @Param("time") Date time);

    /**
     * 更新房东签署状态
     */
    int signByOwner(@Param("id") Integer id, @Param("ownerId") Integer ownerId, @Param("time") Date time);
    
    /**
     * 根据ID更新合同
     */
    int updateById(Contract contract);

    /**
     * 根据条件查询合同列表
     */
    List<Contract> selectList(Map<String, Object> params);

    /**
     * 根据条件查询合同数量
     */
    int selectCount(Map<String, Object> params);
} 