package com.house.service;

import com.house.pojo.Payment;
import com.github.pagehelper.PageInfo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 支付服务接口
 */
public interface PaymentService {

    /**
     * 创建支付记录
     * @param payment 支付记录对象
     * @return 创建的支付记录
     */
    Payment createPayment(Payment payment);

    /**
     * 创建押金支付记录
     * @param orderId 订单ID
     * @param userId 用户ID
     * @param amount 金额
     * @param method 支付方式
     * @return 创建的支付记录
     */
    Payment createDepositPayment(Integer orderId, Integer userId, BigDecimal amount, String method);

    /**
     * 创建租金支付记录
     * @param orderId 订单ID
     * @param userId 用户ID
     * @param amount 金额
     * @param method 支付方式
     * @return 创建的支付记录
     */
    Payment createRentPayment(Integer orderId, Integer userId, BigDecimal amount, String method);

    /**
     * 创建服务费支付记录
     * @param orderId 订单ID
     * @param userId 用户ID
     * @param amount 金额
     * @param method 支付方式
     * @return 创建的支付记录
     */
    Payment createServiceFeePayment(Integer orderId, Integer userId, BigDecimal amount, String method);

    /**
     * 根据支付单号获取支付记录
     * @param paymentNo 支付单号
     * @return 支付记录对象
     */
    Payment getPaymentByPaymentNo(String paymentNo);

    /**
     * 根据ID获取支付记录
     * @param id 支付记录ID
     * @return 支付记录对象
     */
    Payment getPaymentById(Integer id);

    /**
     * 更新支付状态
     * @param paymentNo 支付单号
     * @param status 状态
     * @param transactionId 交易流水号
     * @return 更新是否成功
     */
    boolean updatePaymentStatus(String paymentNo, String status, String transactionId);

    /**
     * 批量更新订单相关的支付记录状态
     * @param orderId 订单ID
     * @param status 状态
     * @param transactionId 交易流水号
     * @return 更新是否成功
     */
    boolean updateOrderPaymentStatus(Integer orderId, String status, String transactionId);

    /**
     * 获取订单的支付记录列表
     * @param orderId 订单ID
     * @return 支付记录列表
     */
    List<Payment> getPaymentsByOrderId(Integer orderId);

    /**
     * 获取用户的支付记录列表
     * @param userId 用户ID
     * @return 支付记录列表
     */
    List<Payment> getPaymentsByUserId(Integer userId);

    /**
     * 分页查询支付记录列表
     * @param params 查询参数，包括：
     *               - userId: 用户ID（可选）
     *               - orderId: 订单ID（可选）
     *               - type: 支付类型（可选）
     *               - status: 支付状态（可选）
     *               - startTime: 开始时间（可选）
     *               - endTime: 结束时间（可选）
     *               - page: 页码（默认1）
     *               - limit: 每页数量（默认10）
     * @return 分页支付记录列表
     */
    PageInfo<Payment> getPaymentList(Map<String, Object> params);

    /**
     * 处理支付回调
     * @param paymentNo 支付单号
     * @param transactionId 交易流水号
     * @param status 状态
     * @return 处理是否成功
     */
    boolean handlePaymentCallback(String paymentNo, String transactionId, String status);

    /**
     * 退款处理
     * @param paymentNo 支付单号
     * @param reason 退款原因
     * @return 退款是否成功
     */
    boolean refundPayment(String paymentNo, String reason);
} 