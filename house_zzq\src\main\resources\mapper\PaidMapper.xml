<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.house.dao.PaidDao">
    <select id="selectByCondition" resultType="com.house.pojo.Paid">
        select * from paid
        <where>
            <if test="status != '' and status != null">
                and status = #{status}
            </if>
            <if test="name != '' and name != null">
                and name LIKE '%${name}%'
            </if>
            <if test="address != '' and address != null">
                and address LIKE '%${address}%'
            </if>
            <if test="userlist_id != '' and userlist_id != null">
                and userlist_id = #{userlist_id}
            </if>
        </where>
        order by
        status DESC ,date DESC
    </select>

    <select id="selectPaidById" resultType="com.house.pojo.Paid">
        SELECT * FROM paid
        WHERE id = #{id}
    </select>

    <update id="updatePaid">
        UPDATE paid
        <set>
            <if test="price != null and price != ''">
                price = #{price},
            </if>
            <if test="date != null">
                date = #{date},
            </if>
            <if test="payDate != null">
                paydate = #{payDate},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
        </set>
        WHERE id = #{id}
    </update>

    <insert id="insertPaid">
        INSERT INTO paid
        (
        address,
        price,
        date,
        paydate,
        name,
        userlist_id,
        status,
        houseid
        )
        VALUES
        (
        #{address},
        #{price},
        #{date},
        #{payDate},
        #{name},
        #{userlist_id},
        #{status},
        #{houseId}
        )
    </insert>

    <delete id="deletePaidById">
        DELETE FROM paid
        WHERE id = #{id}
    </delete>
</mapper>