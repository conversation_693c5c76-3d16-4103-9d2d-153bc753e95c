package com.house.controller;

import com.house.common.Result;
import com.house.common.StatusCode;
import com.house.pojo.Notification;
import com.house.service.NotificationService;
import com.house.service.impl.NotificationServiceImpl;
import com.house.websocket.MessageDTO;
import com.house.websocket.WebSocketServer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 消息通知控制器
 */
@Slf4j
@RestController
@CrossOrigin(origins = "http://localhost:8080", allowCredentials = "true", maxAge = 3600)
@RequestMapping("/notification")
public class NotificationController {

    @Autowired
    private NotificationService notificationService;
    
    @Autowired
    private NotificationServiceImpl notificationServiceImpl;
    
    /**
     * 获取消息列表
     */
    @GetMapping("/list")
    public Result getNotificationList(@RequestParam Map<String, Object> params) {
        try {
            Integer userId = Integer.parseInt(params.get("userId").toString());
            String type = params.get("type") != null ? params.get("type").toString() : null;
            Integer page = params.get("page") != null ? Integer.parseInt(params.get("page").toString()) : 1;
            Integer limit = params.get("limit") != null ? Integer.parseInt(params.get("limit").toString()) : 20;
            
            // 创建参数Map
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("userId", userId);
            if (type != null && !type.isEmpty()) {
                queryParams.put("type", type);
            }
            
            // 调用服务获取消息列表
            List<Notification> list = notificationServiceImpl.getUserNotifications(userId);
            int total = list.size();
            
            // 封装结果
            Map<String, Object> result = new HashMap<>();
            result.put("list", list);
            result.put("total", total);
            
            return new Result(true, StatusCode.SUCCESS, "获取消息列表成功", result);
        } catch (Exception e) {
            log.error("获取消息列表失败: {}", e.getMessage());
            return new Result(false, StatusCode.ERROR, "获取消息列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 标记消息为已读
     */
    @PostMapping("/read")
    public Result markAsRead(@RequestBody Map<String, Object> params) {
        try {
            Integer id = Integer.parseInt(params.get("id").toString());
            boolean success = notificationServiceImpl.markAsRead(id);
            
            if (success) {
                return new Result(true, StatusCode.SUCCESS, "标记消息为已读成功");
            } else {
                return new Result(false, StatusCode.ERROR, "标记消息为已读失败");
            }
        } catch (Exception e) {
            log.error("标记消息为已读失败: {}", e.getMessage());
            return new Result(false, StatusCode.ERROR, "标记消息为已读失败: " + e.getMessage());
        }
    }
    
    /**
     * 标记消息为未读
     */
    @PostMapping("/unread")
    public Result markAsUnread(@RequestBody Map<String, Object> params) {
        try {
            Integer id = Integer.parseInt(params.get("id").toString());
            boolean success = notificationServiceImpl.markAsUnread(id);
            
            if (success) {
                return new Result(true, StatusCode.SUCCESS, "标记消息为未读成功");
            } else {
                return new Result(false, StatusCode.ERROR, "标记消息为未读失败");
            }
        } catch (Exception e) {
            log.error("标记消息为未读失败: {}", e.getMessage());
            return new Result(false, StatusCode.ERROR, "标记消息为未读失败: " + e.getMessage());
        }
    }
    
    /**
     * 标记所有消息为已读
     */
    @PostMapping("/read-all")
    public Result markAllAsRead(@RequestBody Map<String, Object> params) {
        try {
            Integer userId = Integer.parseInt(params.get("userId").toString());
            boolean success = notificationServiceImpl.markAllAsRead(userId);
            
            if (success) {
                return new Result(true, StatusCode.SUCCESS, "标记所有消息为已读成功");
            } else {
                return new Result(false, StatusCode.ERROR, "标记所有消息为已读失败");
            }
        } catch (Exception e) {
            log.error("标记所有消息为已读失败: {}", e.getMessage());
            return new Result(false, StatusCode.ERROR, "标记所有消息为已读失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除消息
     */
    @PostMapping("/delete")
    public Result deleteNotification(@RequestBody Map<String, Object> params) {
        try {
            Integer id = Integer.parseInt(params.get("id").toString());
            boolean success = notificationServiceImpl.deleteNotification(id);
            
            if (success) {
                return new Result(true, StatusCode.SUCCESS, "删除消息成功");
            } else {
                return new Result(false, StatusCode.ERROR, "删除消息失败");
            }
        } catch (Exception e) {
            log.error("删除消息失败: {}", e.getMessage());
            return new Result(false, StatusCode.ERROR, "删除消息失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除所有消息
     */
    @PostMapping("/delete-all")
    public Result deleteAllNotifications(@RequestBody Map<String, Object> params) {
        try {
            Integer userId = Integer.parseInt(params.get("userId").toString());
            boolean success = notificationServiceImpl.deleteAllNotifications(userId);
            
            if (success) {
                return new Result(true, StatusCode.SUCCESS, "删除所有消息成功");
            } else {
                return new Result(false, StatusCode.ERROR, "删除所有消息失败");
            }
        } catch (Exception e) {
            log.error("删除所有消息失败: {}", e.getMessage());
            return new Result(false, StatusCode.ERROR, "删除所有消息失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取未读消息数量
     */
    @GetMapping("/unread-count")
    public Result getUnreadCount(@RequestParam Integer userId) {
        try {
            int count = notificationServiceImpl.getUserUnreadCount(userId);
            return new Result(true, StatusCode.SUCCESS, "获取未读消息数量成功", count);
        } catch (Exception e) {
            log.error("获取未读消息数量失败: {}", e.getMessage());
            return new Result(false, StatusCode.ERROR, "获取未读消息数量失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送消息通知
     */
    @PostMapping("/send")
    public Result sendNotification(@RequestBody Map<String, Object> params) {
        try {
            String userId = params.get("userId").toString();
            String title = params.get("title").toString();
            String content = params.get("content").toString();
            String type = params.get("type").toString();
            String linkId = params.get("linkId") != null ? params.get("linkId").toString() : null;
            String linkType = params.get("linkType") != null ? params.get("linkType").toString() : null;
            
            Long messageId = notificationService.sendNotification(userId, title, content, type, linkId, linkType);
            
            Map<String, Object> result = new HashMap<>();
            result.put("messageId", messageId);
            
            return new Result(true, StatusCode.SUCCESS, "消息发送成功", result);
        } catch (Exception e) {
            log.error("发送消息失败: {}", e.getMessage());
            return new Result(false, StatusCode.ERROR, "发送消息失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送全局通知
     */
    @PostMapping("/sendAll")
    public Result sendNotificationToAll(@RequestBody Map<String, Object> params) {
        try {
            String title = params.get("title").toString();
            String content = params.get("content").toString();
            String type = params.get("type").toString();
            
            boolean success = notificationService.sendNotificationToAll(title, content, type);
            
            if (success) {
                return new Result(true, StatusCode.SUCCESS, "全局消息发送成功");
            } else {
                return new Result(false, StatusCode.ERROR, "全局消息发送失败");
            }
        } catch (Exception e) {
            log.error("发送全局消息失败: {}", e.getMessage());
            return new Result(false, StatusCode.ERROR, "发送全局消息失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送订单通知
     */
    @PostMapping("/order")
    public Result sendOrderNotification(@RequestBody Map<String, Object> params) {
        try {
            String userId = params.get("userId").toString();
            String title = params.get("title").toString();
            String content = params.get("content").toString();
            String orderId = params.get("orderId").toString();
            
            Long messageId = notificationService.sendOrderNotification(userId, title, content, orderId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("messageId", messageId);
            
            return new Result(true, StatusCode.SUCCESS, "订单消息发送成功", result);
        } catch (Exception e) {
            log.error("发送订单消息失败: {}", e.getMessage());
            return new Result(false, StatusCode.ERROR, "发送订单消息失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送合同通知
     */
    @PostMapping("/contract")
    public Result sendContractNotification(@RequestBody Map<String, Object> params) {
        try {
            String userId = params.get("userId").toString();
            String title = params.get("title").toString();
            String content = params.get("content").toString();
            String contractId = params.get("contractId").toString();
            
            Long messageId = notificationService.sendContractNotification(userId, title, content, contractId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("messageId", messageId);
            
            return new Result(true, StatusCode.SUCCESS, "合同消息发送成功", result);
        } catch (Exception e) {
            log.error("发送合同消息失败: {}", e.getMessage());
            return new Result(false, StatusCode.ERROR, "发送合同消息失败: " + e.getMessage());
        }
    }
    
    /**
     * 发送支付通知
     */
    @PostMapping("/payment")
    public Result sendPaymentNotification(@RequestBody Map<String, Object> params) {
        try {
            String userId = params.get("userId").toString();
            String title = params.get("title").toString();
            String content = params.get("content").toString();
            String paymentId = params.get("paymentId").toString();
            
            Long messageId = notificationService.sendPaymentNotification(userId, title, content, paymentId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("messageId", messageId);
            
            return new Result(true, StatusCode.SUCCESS, "支付消息发送成功", result);
        } catch (Exception e) {
            log.error("发送支付消息失败: {}", e.getMessage());
            return new Result(false, StatusCode.ERROR, "发送支付消息失败: " + e.getMessage());
        }
    }
    
    /**
     * 测试消息通知
     */
    @GetMapping("/test")
    public Result testNotification(@RequestParam String userId) {
        try {
            notificationService.sendNotification(
                userId,
                "测试消息",
                "这是一条测试消息，用于验证WebSocket通知功能是否正常。",
                "info"
            );
            
            return new Result(true, StatusCode.SUCCESS, "测试消息发送成功");
        } catch (Exception e) {
            log.error("发送测试消息失败: {}", e.getMessage());
            return new Result(false, StatusCode.ERROR, "发送测试消息失败: " + e.getMessage());
        }
    }

    /**
     * 发送消息
     * @param userId 用户ID
     * @param message 消息对象
     * @return 结果
     */
    @PostMapping("/send/{userId}")
    public Result sendNotification(@PathVariable String userId, @RequestBody MessageDTO message) {
        WebSocketServer.sendMessageToUser("frontend", userId, message.toJsonString());
        return new Result(true, StatusCode.SUCCESS, "消息已发送");
    }
    
    /**
     * 发送消息到后台
     * @param userId 用户ID
     * @param message 消息对象
     * @return 结果
     */
    @PostMapping("/send-backstage/{userId}")
    public Result sendBackstageNotification(@PathVariable String userId, @RequestBody MessageDTO message) {
        WebSocketServer.sendMessageToUser("backstage", userId, message.toJsonString());
        return new Result(true, StatusCode.SUCCESS, "后台消息已发送");
    }

    /**
     * 广播消息
     * @param message 消息对象
     * @return 结果
     */
    @PostMapping("/broadcast")
    public Result broadcastNotification(@RequestBody MessageDTO message) {
        WebSocketServer.sendMessageToAll(message.toJsonString());
        return new Result(true, StatusCode.SUCCESS, "广播消息已发送");
    }
    
    /**
     * 获取在线用户信息
     * @return 在线用户信息
     */
    @GetMapping("/online-users")
    public Result getOnlineUsers() {
        Map<String, Object> data = new HashMap<>();
        data.put("onlineCount", WebSocketServer.getOnlineCount());
        data.put("onlineUsers", WebSocketServer.getOnlineUserList());
        return new Result(true, StatusCode.SUCCESS, "获取在线用户信息成功", data);
    }
    
    /**
     * 检查用户是否在线
     * @param userId 用户ID
     * @return 用户在线状态
     */
    @GetMapping("/check-online/{userId}")
    public Result checkUserOnline(@PathVariable String userId) {
        boolean isOnline = WebSocketServer.isUserOnline("frontend", userId);
        return new Result(true, StatusCode.SUCCESS, "获取用户在线状态成功", isOnline);
    }
    
    /**
     * 检查后台用户是否在线
     * @param userId 用户ID
     * @return 用户在线状态
     */
    @GetMapping("/check-backstage-online/{userId}")
    public Result checkBackstageUserOnline(@PathVariable String userId) {
        boolean isOnline = WebSocketServer.isUserOnline("backstage", userId);
        return new Result(true, StatusCode.SUCCESS, "获取后台用户在线状态成功", isOnline);
    }
} 