package com.house.pojo;

import lombok.Data;

import jakarta.persistence.Column;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.Date;

@Data
@Table(name = "paid")
public class Paid {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "`id`")
    private Integer id;

    @Column(name = "address")
    private String address;

    @Column(name = "price")
    private Double price;

    @Column(name = "date")
    private Date date;

    @Column(name = "paydate")
    private Date payDate;

    @Column(name = "name")
    private String name;

    @Column(name = "userlist_id")
    private Integer userlist_id;

    @Column(name = "status")
    private String status;

    @Column(name = "houseid")
    private Integer houseId;


}
