<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.house.dao.HouseListDao">
    <resultMap id="HouseResultType" type="com.house.pojo.HouseList">
        <id column="houseid" property="houseId"></id>
        <result column="address" property="address"></result>
        <result column="price" property="price"></result>
        <result column="status" property="status"></result>
        <result column="detail" property="detail"></result>
        <result column="userlist_id" property="userlist_Id"></result>
        <result column="userlist_name" property="userlist_Name"></result>
    </resultMap>
    <select id="selectHouseListByCondition" resultMap="HouseResultType">
        select * from house
        <where>
            <if test="address != '' and address != null">
                and address like '%${address}%'
            </if>
            <if test="status != '' and status != null">
                and status = #{status}
            </if>
            <if test="userListId != null">
                and userlist_id = #{userListId}
            </if>
        </where>
        order by
        status DESC ,house_id ASC,address ASC
    </select>

    <select id="selectHouseById" resultMap="HouseResultType">
        SELECT *
        FROM house
        WHERE house_id=#{houseId}
    </select>

    <delete id="deleteHouseListById">
        DELETE FROM
        house
        WHERE house_id=#{houseId}
    </delete>

    <insert id="insertHouseList">
        INSERT INTO
        house (address,price,status,detail,userlist_id,userlist_name)
        VALUES (#{address},#{price},#{status},#{detail},#{userlist_Id},#{userlist_Name})
    </insert>

    <update id="updateHouseList">
        UPDATE house
        SET
        address=#{address},
        price=#{price},
        status=#{status},
        detail=#{detail},
        userlist_id=#{userlist_Id},
        userlist_name=#{userlist_Name}
        WHERE house_id=#{houseId}
    </update>
</mapper>