package com.house.websocket;

import com.alibaba.fastjson.JSON;
import com.house.dao.UserListDao;
import com.house.pojo.ChatMessage;
import com.house.pojo.UserList;
import com.house.service.ChatMessageService;
import com.house.utils.JwtUtil;
import io.jsonwebtoken.Claims;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import jakarta.websocket.*;
import jakarta.websocket.server.PathParam;
import jakarta.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * WebSocket房源聊天服务端处理类
 * @ServerEndpoint 注解是一个类层次的注解，用于声明一个类是WebSocket的服务器端
 */
@Slf4j
@Component
@ServerEndpoint("/ws/chat/{userId}")
public class WebSocketChatServer {

    /**
     * 用于解决WebSocket中无法注入的问题
     */
    private static ApplicationContext applicationContext;
    
    /**
     * 设置应用上下文
     */
    public static void setApplicationContext(ApplicationContext context) {
        WebSocketChatServer.applicationContext = context;
    }
    
    /**
     * 获取UserListDao的实例
     */
    private static UserListDao getUserListDao() {
        if (applicationContext != null) {
            return applicationContext.getBean(UserListDao.class);
        }
        return null;
    }
    
    /**
     * 获取ChatMessageService的实例
     */
    private static ChatMessageService getChatMessageService() {
        if (applicationContext != null) {
            return applicationContext.getBean(ChatMessageService.class);
        }
        return null;
    }
    
    /**
     * 获取JwtUtil的实例
     */
    private static JwtUtil getJwtUtil() {
        if (applicationContext != null) {
            return applicationContext.getBean(JwtUtil.class);
        }
        return null;
    }

    /**
     * 存放所有在线的聊天客户端
     * key是用户ID，value是WebSocket会话
     */
    private static Map<String, Session> chatClients = new ConcurrentHashMap<>();
    
    /**
     * 存放用户ID和用户信息的映射
     * key是用户ID，value是用户信息对象
     */
    private static Map<String, UserInfo> userInfoMap = new ConcurrentHashMap<>();

    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("userId") String userId) {
        // 获取查询参数
        Map<String, String> queryParams = session.getRequestParameterMap().entrySet().stream()
                .collect(java.util.stream.Collectors.toMap(
                        Map.Entry::getKey,
                        e -> e.getValue().get(0)
                ));
        
        String houseId = queryParams.get("houseId");
        String landlordId = queryParams.get("landlordId");
        String token = queryParams.get("token");
        
        // 获取用户信息（姓名和类型）
        UserInfo userInfo = getUserInfo(userId);
        
        // 只记录重要用户的连接信息
        if (userInfo.type == 1 || userInfo.type == 3) { // 管理员或房东
            log.info("用户{}（{}）尝试连接聊天，房源ID：{}", 
                    userInfo.name, getUserTypeDesc(userInfo.type), houseId);
        }
        
        // 验证token
        try {
            if (token == null || token.isEmpty()) {
                log.warn("WebSocket聊天连接失败：token为空");
                session.close();
                return;
            }
            
            // 获取JwtUtil实例
            JwtUtil jwtUtil = getJwtUtil();
            if (jwtUtil == null) {
                log.error("WebSocket聊天连接失败：无法获取JwtUtil实例");
                session.close();
                return;
            }
            
            // 验证token有效性
            try {
                // 解析token前进行简单检查
                if (!token.contains(".") || token.split("\\.").length != 3) {
                    log.warn("WebSocket聊天连接失败：token格式无效");
                    session.close();
                    return;
                }
                
                Claims claims = jwtUtil.parseJWT(token);
                String tokenUserId = claims.getId(); // JWT的ID字段存储的是用户ID
                
                if (tokenUserId == null || !tokenUserId.equals(userId)) {
                    log.warn("WebSocket聊天连接失败：token无效或用户ID不匹配");
                    session.close();
                    return;
                }
                
                // 只记录重要用户的token验证成功信息
                if (userInfo.type == 1 || userInfo.type == 3) { // 管理员或房东
                    log.info("用户{}的token验证成功", userInfo.name);
                }
            } catch (io.jsonwebtoken.security.SignatureException e) {
                log.warn("WebSocket聊天连接失败：token签名验证失败");
                
                // 对于签名验证失败的情况，我们暂时允许连接（临时解决方案）
                if (userInfo.type == 1 || userInfo.type == 3) { // 管理员或房东
                    log.info("临时解决方案：尽管token验证失败，仍允许用户{}连接", userInfo.name);
                }
                
                // 将新用户添加到chatClients中
                chatClients.put(userId, session);
                
                // 保存用户信息到Map中
                userInfoMap.put(userId, userInfo);
                
                // 保存房间信息到session属性中
                session.getUserProperties().put("userId", userId);
                session.getUserProperties().put("houseId", houseId);
                session.getUserProperties().put("landlordId", landlordId);
                
                // 如果是房东用户，特殊处理
                if (userId.equals(landlordId) || "0".equals(houseId)) {
                    if (userInfo.type == 3) { // 房东
                        log.info("房东用户{}连接成功，可接收所有房源消息", userInfo.name);
                    }
                }
                
                // 发送历史聊天记录
                sendHistoryMessages(session, userId, houseId, landlordId);
                
                return;
            } catch (Exception e) {
                log.warn("WebSocket聊天连接失败：token解析异常");
                session.close();
                return;
            }
        } catch (Exception e) {
            log.error("WebSocket聊天连接失败：token验证异常");
            try {
                session.close();
            } catch (IOException ex) {
                log.error("关闭WebSocket会话失败");
            }
            return;
        }
        
        // 将新用户添加到chatClients中
        chatClients.put(userId, session);
        
        // 保存用户信息到Map中
        userInfoMap.put(userId, userInfo);
        
        // 保存房间信息到session属性中
        session.getUserProperties().put("userId", userId);
        session.getUserProperties().put("houseId", houseId);
        session.getUserProperties().put("landlordId", landlordId);
        
        // 如果是房东用户，特殊处理
        if (userId.equals(landlordId) || "0".equals(houseId)) {
            if (userInfo.type == 3) { // 房东
                log.info("房东用户{}连接成功，可接收所有房源消息", userInfo.name);
            }
        }
        
        // 发送历史聊天记录
        sendHistoryMessages(session, userId, houseId, landlordId);
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose(Session session, @PathParam("userId") String userId) {
        // 获取用户信息
        UserInfo userInfo = userInfoMap.getOrDefault(userId, new UserInfo("用户-" + userId, 0));
        
        // 从chatClients中移除用户
        chatClients.remove(userId);
        
        // 从用户信息映射中移除
        userInfoMap.remove(userId);
        
        // 获取房间信息
        String houseId = (String) session.getUserProperties().get("houseId");
        String landlordId = (String) session.getUserProperties().get("landlordId");
        
        // 只记录重要用户的断开连接信息
        if (userInfo.type == 1 || userInfo.type == 3) { // 管理员或房东
            log.info("用户{}离开聊天，房源ID：{}", userInfo.name, houseId);
        }
    }
    
    /**
     * 用户信息类
     */
    private static class UserInfo {
        String name;
        Integer type;
        
        public UserInfo(String name, Integer type) {
            this.name = name;
            this.type = type != null ? type : 0;
        }
    }
    
    /**
     * 获取用户类型描述
     */
    private static String getUserTypeDesc(Integer type) {
        if (type == null) return "未知";
        
        switch (type) {
            case 1: return "管理员";
            case 2: return "用户";
            case 3: return "房东";
            default: return "未知";
        }
    }

    /**
     * 获取用户信息
     * @param userId 用户ID
     * @return 用户信息对象，包含用户名和类型
     */
    private UserInfo getUserInfo(String userId) {
        try {
            // 确保userId是有效的数字
            Integer userIdInt;
            try {
                userIdInt = Integer.parseInt(userId);
            } catch (NumberFormatException e) {
                return new UserInfo("游客-" + userId, 0);
            }
            
            // 从数据库查询用户信息
            UserListDao userListDao = getUserListDao();
            if (userListDao != null) {
                // 先查询用户自身ID
                UserList userInfo = userListDao.findUserListById(userIdInt);
                if (userInfo != null && userInfo.getName() != null) {
                    return new UserInfo(userInfo.getName(), userInfo.getType());
                }
                
                // 再尝试查询关联的用户ID
                userInfo = userListDao.selectUserInfoByCondition(null, userIdInt, null);
                if (userInfo != null && userInfo.getName() != null) {
                    return new UserInfo(userInfo.getName(), userInfo.getType());
                }
                
                // 最后尝试直接查询ID
                userInfo = userListDao.selectUserInfoByCondition(null, null, userIdInt);
                if (userInfo != null && userInfo.getName() != null) {
                    return new UserInfo(userInfo.getName(), userInfo.getType());
                }
            } else {
                log.warn("无法获取UserListDao实例");
            }
        } catch (Exception e) {
            log.warn("获取用户信息失败，使用用户ID代替");
        }
        
        // 如果获取失败，返回用户ID作为备用
        return new UserInfo("用户-" + userId, 0);
    }

    /**
     * 收到客户端消息后调用的方法
     */
    @OnMessage
    public void onMessage(String message, Session session, @PathParam("userId") String userId) {
        try {
            // 检查消息是否为空
            if (message == null || message.trim().isEmpty()) {
                log.warn("收到空消息，忽略处理");
                return;
            }

            // 获取用户信息
            UserInfo userInfo = userInfoMap.getOrDefault(userId, getUserInfo(userId));

            // 解析消息
            Map<String, Object> msgMap;
            try {
                msgMap = JSON.parseObject(message, Map.class);
                if (msgMap == null) {
                    log.warn("消息解析结果为null，忽略处理");
                    return;
                }
            } catch (Exception e) {
                log.warn("消息解析失败: {}, 原始消息: {}", e.getMessage(), message);
                return;
            }

            // 处理心跳消息
            if (msgMap.containsKey("type") && "heartbeat".equals(msgMap.get("type"))) {
                // 心跳消息不记录日志，直接返回
                return;
            }

            // 获取房间信息
            String houseId = (String) session.getUserProperties().get("houseId");
            String landlordId = (String) session.getUserProperties().get("landlordId");

            // 检查必要的房间信息
            if (houseId == null || landlordId == null) {
                log.warn("房间信息不完整，houseId: {}, landlordId: {}", houseId, landlordId);
                return;
            }
            
            // 只记录重要用户的消息
            if (userInfo.type == 1 || userInfo.type == 3) { // 管理员或房东
                log.info("收到消息: 用户{}, 房源ID={}", userInfo.name, houseId);
            }
            
            // 添加发送者ID
            msgMap.put("fromId", userId);

            // 添加时间戳（如果没有）
            if (!msgMap.containsKey("timestamp") || msgMap.get("timestamp") == null) {
                msgMap.put("timestamp", System.currentTimeMillis());
            }

            // 确定接收者ID
            String toUserId = userId.equals(landlordId) ?
                    (String) msgMap.get("toId") : landlordId;

            // 检查接收者ID是否有效
            if (toUserId == null || toUserId.trim().isEmpty()) {
                log.warn("接收者ID为空，无法发送消息");
                return;
            }

            // 获取接收者信息
            UserInfo toUserInfo = userInfoMap.getOrDefault(toUserId, getUserInfo(toUserId));
            
            // 只记录重要用户的消息转发
            if (userInfo.type == 1 || userInfo.type == 3 || toUserInfo.type == 1 || toUserInfo.type == 3) {
                log.debug("消息转发: 从用户{} 到用户{}, 房源ID: {}", 
                        userInfo.name, toUserInfo.name, houseId);
            }
            
            // 保存消息到数据库
            ChatMessageService chatMessageService = getChatMessageService();
            if (chatMessageService != null) {
                try {
                    // 检查消息内容
                    String content = (String) msgMap.get("content");
                    if (content == null || content.trim().isEmpty()) {
                        log.warn("消息内容为空，不保存到数据库");
                        return;
                    }

                    ChatMessage chatMessage = new ChatMessage();
                    chatMessage.setHouseId(Long.parseLong(houseId));
                    chatMessage.setFromUserId(Long.parseLong(userId));
                    chatMessage.setToUserId(Long.parseLong(toUserId));
                    chatMessage.setContent(content);
                    chatMessage.setMessageType("text"); // 默认为文本消息
                    chatMessage.setReadStatus(0); // 未读
                
                    chatMessageService.saveChatMessage(chatMessage);

                    // 添加消息ID到msgMap
                    if (chatMessage.getId() != null) {
                        msgMap.put("id", chatMessage.getId());
                    }

                    // 设置消息类型为chat
                    msgMap.put("type", "chat");

                    // 转换为JSON字符串
                    String jsonMessage = JSON.toJSONString(msgMap);
                
                    // 转发消息给接收者
                    Session receiverSession = chatClients.get(toUserId);
                    if (receiverSession != null && receiverSession.isOpen()) {
                        try {
                            // 只记录重要用户的消息发送
                            if (toUserInfo.type == 1 || toUserInfo.type == 3) {
                                log.debug("接收者{}在线，直接转发消息", toUserInfo.name);
                            }
                            receiverSession.getBasicRemote().sendText(jsonMessage);
                        } catch (IOException e) {
                            log.error("发送消息给接收者{}失败: {}", toUserInfo.name, e.getMessage());
                            // 移除无效的会话
                            chatClients.remove(toUserId);
                        }
                    } else {
                        // 检查是否有房东的其他会话（可能使用了不同的houseId）
                        boolean messageSent = false;
                        for (Map.Entry<String, Session> entry : chatClients.entrySet()) {
                            String clientId = entry.getKey();
                            Session clientSession = entry.getValue();

                            if (clientId != null && clientId.equals(toUserId) && clientSession != null && clientSession.isOpen()) {
                                try {
                                    UserInfo clientInfo = userInfoMap.getOrDefault(clientId, getUserInfo(clientId));

                                    // 只记录重要用户的消息发送
                                    if (clientInfo.type == 1 || clientInfo.type == 3) {
                                        log.debug("找到接收者{}的其他会话，发送消息", clientInfo.name);
                                    }
                                    clientSession.getBasicRemote().sendText(jsonMessage);
                                    messageSent = true;
                                    break;
                                } catch (IOException e) {
                                    log.error("发送消息给客户端{}失败: {}", clientId, e.getMessage());
                                    // 移除无效的会话
                                    chatClients.remove(clientId);
                                }
                            }
                        }
                    
                        if (!messageSent && (toUserInfo.type == 1 || toUserInfo.type == 3)) {
                            log.debug("接收者{}不在线，消息已保存到数据库", toUserInfo.name);
                        }
                    }
                } catch (NumberFormatException e) {
                    log.error("数字格式转换失败: {}", e.getMessage());
                } catch (Exception e) {
                    log.error("保存消息到数据库失败: {}", e.getMessage());
                }
            } else {
                log.error("无法获取ChatMessageService实例，消息未能保存");
            }
        } catch (Exception e) {
            log.error("处理聊天消息失败: {}", e.getMessage());
        }
    }

    /**
     * 发生错误时调用的方法
     */
    @OnError
    public void onError(Session session, Throwable error, @PathParam("userId") String userId) {
        // 获取用户信息
        UserInfo userInfo = userInfoMap.getOrDefault(userId, getUserInfo(userId));
        
        // 获取房间信息
        String houseId = (String) session.getUserProperties().get("houseId");
        String landlordId = (String) session.getUserProperties().get("landlordId");
        
        // 只记录错误的简要信息，不输出堆栈跟踪
        log.error("聊天WebSocket错误：用户 - {}，房源 - {}，原因：{}", 
                userInfo.name, houseId, error.getMessage());
    }

    /**
     * 发送历史聊天记录
     */
    private void sendHistoryMessages(Session session, String userId, String houseId, String landlordId) {
        try {
            // 检查参数有效性
            if (session == null || !session.isOpen() ||
                userId == null || houseId == null || landlordId == null) {
                log.warn("发送历史消息参数无效");
                return;
            }

            ChatMessageService chatMessageService = getChatMessageService();
            if (chatMessageService != null) {
                List<ChatMessage> history = chatMessageService.getChatHistory(
                        Long.parseLong(houseId),
                        Long.parseLong(userId),
                        Long.parseLong(landlordId));

                if (history != null && !history.isEmpty()) {
                    for (ChatMessage msg : history) {
                        try {
                            // 检查消息对象的完整性
                            if (msg.getFromUserId() == null || msg.getToUserId() == null ||
                                msg.getContent() == null || msg.getCreateTime() == null) {
                                log.warn("历史消息数据不完整，跳过");
                                continue;
                            }

                            // 构建消息对象
                            Map<String, Object> msgMap = new ConcurrentHashMap<>();
                            msgMap.put("fromId", msg.getFromUserId().toString());
                            msgMap.put("toId", msg.getToUserId().toString());
                            msgMap.put("content", msg.getContent());
                            msgMap.put("timestamp", msg.getCreateTime().getTime());
                            msgMap.put("type", "chat");
                            if (msg.getId() != null) {
                                msgMap.put("id", msg.getId());
                            }

                            // 发送消息
                            session.getBasicRemote().sendText(JSON.toJSONString(msgMap));
                        } catch (Exception e) {
                            log.error("发送单条历史消息失败: {}", e.getMessage());
                        }
                    }

                    // 将消息标记为已读
                    try {
                        chatMessageService.markMessagesAsRead(
                                Long.parseLong(userId),
                                Long.parseLong(landlordId));
                    } catch (Exception e) {
                        log.error("标记消息为已读失败: {}", e.getMessage());
                    }
                }
            }
        } catch (NumberFormatException e) {
            log.error("发送历史消息时数字格式转换失败: {}", e.getMessage());
        } catch (Exception e) {
            log.error("发送历史消息失败: {}", e.getMessage());
        }
    }

    /**
     * 向指定用户发送消息
     */
    public static void sendMessageToUser(String userId, String message) {
        // 检查参数有效性
        if (userId == null || userId.trim().isEmpty() || message == null) {
            log.warn("发送消息参数无效: userId={}, message={}", userId, message != null ? "非空" : "null");
            return;
        }

        Session session = chatClients.get(userId);
        if (session != null && session.isOpen()) {
            try {
                // 获取用户信息
                UserInfo userInfo = userInfoMap.getOrDefault(userId,
                        new UserInfo("用户-" + userId, 0));

                session.getBasicRemote().sendText(message);

                // 只记录重要用户的消息发送
                if (userInfo.type == 1 || userInfo.type == 3) {
                    log.debug("成功发送消息给用户{}", userInfo.name);
                }
            } catch (IOException e) {
                // 获取用户信息
                UserInfo userInfo = userInfoMap.getOrDefault(userId,
                        new UserInfo("用户-" + userId, 0));

                log.error("发送消息给用户{}失败: {}", userInfo.name, e.getMessage());

                // 移除无效的会话
                chatClients.remove(userId);
                userInfoMap.remove(userId);
            }
        } else {
            // 获取用户信息（此时可能需要查询数据库）
            UserInfo userInfo = userInfoMap.getOrDefault(userId,
                    new UserInfo("用户-" + userId, 0));

            // 只记录重要用户的消息发送失败
            if (userInfo.type == 1 || userInfo.type == 3) {
                log.debug("用户{}不在线，无法发送消息", userInfo.name);
            }
        }
    }
} 