package com.house.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 聊天消息实体类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChatMessage {
    /**
     * 消息ID
     */
    private Long id;
    
    /**
     * 房源ID
     */
    private Long houseId;
    
    /**
     * 发送者ID
     */
    private Long fromUserId;
    
    /**
     * 接收者ID
     */
    private Long toUserId;
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * 消息类型：text-文本，image-图片，等
     */
    private String messageType;
    
    /**
     * 阅读状态：0-未读，1-已读
     */
    private Integer readStatus;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    
    /**
     * 房源地址（非数据库字段，关联查询）
     */
    private String houseAddress;
    
    /**
     * 发送者用户名（非数据库字段，关联查询）
     */
    private String fromUserName;
    
    /**
     * 发送者头像（非数据库字段，关联查询）
     */
    private String fromUserAvatar;
    
    /**
     * 接收者用户名（非数据库字段，关联查询）
     */
    private String toUserName;
    
    /**
     * 接收者头像（非数据库字段，关联查询）
     */
    private String toUserAvatar;
} 