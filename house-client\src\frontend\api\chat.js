import request from '../../utils/request'

/**
 * 获取聊天记录
 * @param {Number} houseId 房源ID
 * @param {Number} userId 用户ID
 * @param {Number} landlordId 房东ID
 */
export function getChatHistory(houseId, userId, landlordId) {
  return request({
    url: `/chat/history`,
    method: 'get',
    params: { houseId, userId, landlordId }
  })
}

/**
 * 将消息标记为已读
 * @param {Number} toUserId 接收者ID
 * @param {Number} fromUserId 发送者ID
 */
export function markMessagesAsRead(toUserId, fromUserId) {
  return request({
    url: `/chat/markRead`,
    method: 'put',
    params: { toUserId, fromUserId }
  })
}

/**
 * 获取用户未读消息数量
 * @param {Number} userId 用户ID
 */
export function getUnreadMessageCount(userId) {
  return request({
    url: `/chat/unreadCount/${userId}`,
    method: 'get'
  })
}

/**
 * 删除聊天记录
 * @param {Number} houseId 房源ID
 * @param {Number} userId 用户ID
 * @param {Number} landlordId 房东ID
 */
export function deleteChatHistory(houseId, userId, landlordId) {
  return request({
    url: `/chat/delete`,
    method: 'delete',
    params: { houseId, userId, landlordId }
  })
}

export default {
  getChatHistory,
  markMessagesAsRead,
  getUnreadMessageCount,
  deleteChatHistory
} 