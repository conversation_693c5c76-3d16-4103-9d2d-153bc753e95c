package com.house.mapper;

import com.house.pojo.Order;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 订单数据访问接口
 */
@Mapper
@Repository
public interface OrderMapper {

    /**
     * 插入订单
     */
    @Insert("INSERT INTO tb_order(order_no, house_id, tenant_id, owner_id, start_date, end_date, " +
            "duration, monthly_price, deposit, service_fee, total_price, status, create_time, update_time) " +
            "VALUES(#{orderNo}, #{houseId}, #{tenantId}, #{ownerId}, #{startDate}, #{endDate}, " +
            "#{duration}, #{monthlyPrice}, #{deposit}, #{serviceFee}, #{totalPrice}, #{status}, " +
            "#{createTime}, #{updateTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Order order);

    /**
     * 根据ID查询订单
     */
    @Select("SELECT * FROM tb_order WHERE id = #{id}")
    Order selectById(Integer id);

    /**
     * 根据订单号查询订单
     */
    @Select("SELECT * FROM tb_order WHERE order_no = #{orderNo}")
    Order selectByOrderNo(String orderNo);

    /**
     * 更新订单
     */
    @Update("UPDATE tb_order SET house_id = #{houseId}, tenant_id = #{tenantId}, owner_id = #{ownerId}, " +
            "start_date = #{startDate}, end_date = #{endDate}, duration = #{duration}, " +
            "monthly_price = #{monthlyPrice}, deposit = #{deposit}, service_fee = #{serviceFee}, " +
            "total_price = #{totalPrice}, status = #{status}, deposit_pay_time = #{depositPayTime}, " +
            "contract_sign_time = #{contractSignTime}, check_in_time = #{checkInTime}, " +
            "reviewed = #{reviewed}, update_time = #{updateTime} " +
            "WHERE id = #{id}")
    int update(Order order);

    /**
     * 更新订单状态
     */
    @Update("UPDATE tb_order SET status = #{status}, update_time = NOW() WHERE order_no = #{orderNo}")
    int updateStatusByOrderNo(@Param("orderNo") String orderNo, @Param("status") String status);

    /**
     * 更新押金支付时间
     */
    @Update("UPDATE tb_order SET deposit_pay_time = #{payTime}, status = 'pending', update_time = NOW() " +
            "WHERE order_no = #{orderNo}")
    int updateDepositPayTime(@Param("orderNo") String orderNo, @Param("payTime") Date payTime);

    /**
     * 更新合同签署时间
     */
    @Update("UPDATE tb_order SET contract_sign_time = #{signTime}, update_time = NOW() " +
            "WHERE order_no = #{orderNo}")
    int updateContractSignTime(@Param("orderNo") String orderNo, @Param("signTime") Date signTime);

    /**
     * 更新入住时间
     */
    @Update("UPDATE tb_order SET check_in_time = #{checkInTime}, status = 'renting', update_time = NOW() " +
            "WHERE order_no = #{orderNo}")
    int updateCheckInTime(@Param("orderNo") String orderNo, @Param("checkInTime") Date checkInTime);

    /**
     * 标记订单为已评价
     */
    @Update("UPDATE tb_order SET reviewed = 1, update_time = NOW() WHERE id = #{id}")
    int markAsReviewed(Integer id);

    /**
     * 查询租客的订单列表
     */
    @Select("SELECT * FROM tb_order WHERE tenant_id = #{tenantId} ORDER BY create_time DESC")
    List<Order> selectByTenantId(Integer tenantId);

    /**
     * 查询房东的订单列表
     */
    @Select("SELECT * FROM tb_order WHERE owner_id = #{ownerId} ORDER BY create_time DESC")
    List<Order> selectByOwnerId(Integer ownerId);

    /**
     * 查询房源的订单列表
     */
    @Select("SELECT * FROM tb_order WHERE house_id = #{houseId} ORDER BY create_time DESC")
    List<Order> selectByHouseId(Integer houseId);

    /**
     * 条件查询订单列表
     */
    List<Order> selectByCondition(Map<String, Object> params);

    /**
     * 统计用户各状态的订单数量
     */
    @Select("SELECT status, COUNT(*) as count FROM tb_order WHERE tenant_id = #{userId} OR owner_id = #{userId} GROUP BY status")
    List<Map<String, Object>> countOrdersByUserIdGroupByStatus(Integer userId);

    /**
     * 查询即将到期的订单
     */
    @Select("SELECT * FROM tb_order WHERE status = 'renting' AND end_date BETWEEN #{startDate} AND #{endDate}")
    List<Order> selectExpiringOrders(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询待续租的订单
     */
    @Select("SELECT * FROM tb_order WHERE status = 'renting' AND end_date <= DATE_ADD(CURDATE(), INTERVAL 30 DAY)")
    List<Order> selectRenewalOrders();
} 