<template>
  <div class="review-form-container">
    <el-form ref="reviewForm" :model="reviewForm" :rules="rules" label-width="100px">
      <el-form-item label="总体评分" prop="rating">
        <el-rate
          v-model="reviewForm.rating"
          :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
          :texts="['很差', '较差', '一般', '较好', '很好']"
          show-text
          :max="5"
        ></el-rate>
      </el-form-item>
      
      <div class="aspect-ratings">
        <h4>各方面评分</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="位置便利性">
              <el-rate v-model="reviewForm.locationRating" :max="5"></el-rate>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="房屋设施">
              <el-rate v-model="reviewForm.facilityRating" :max="5"></el-rate>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="房东服务">
              <el-rate v-model="reviewForm.serviceRating" :max="5"></el-rate>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="性价比">
              <el-rate v-model="reviewForm.valueRating" :max="5"></el-rate>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="周边环境">
              <el-rate v-model="reviewForm.environmentRating" :max="5"></el-rate>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="卫生情况">
              <el-rate v-model="reviewForm.cleanlinessRating" :max="5"></el-rate>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      
      <el-form-item label="评价内容" prop="content">
        <el-input
          type="textarea"
          v-model="reviewForm.content"
          :rows="4"
          placeholder="请分享您的租住体验，如房屋状况、房东服务、周边环境等..."
        ></el-input>
      </el-form-item>
      
      <el-form-item label="上传图片">
        <el-upload
          action="#"
          list-type="picture-card"
          :http-request="uploadImage"
          :limit="6"
          :file-list="fileList"
          :on-preview="handlePictureCardPreview"
          :on-remove="handleRemove"
        >
          <i class="el-icon-plus"></i>
        </el-upload>
        <el-dialog :visible.sync="dialogVisible">
          <img width="100%" :src="dialogImageUrl" alt="">
        </el-dialog>
      </el-form-item>
      
      <el-form-item label="推荐标签">
        <el-checkbox-group v-model="reviewForm.tags">
          <el-checkbox label="性价比高">性价比高</el-checkbox>
          <el-checkbox label="交通便利">交通便利</el-checkbox>
          <el-checkbox label="设施完善">设施完善</el-checkbox>
          <el-checkbox label="环境安静">环境安静</el-checkbox>
          <el-checkbox label="房东热情">房东热情</el-checkbox>
          <el-checkbox label="家具齐全">家具齐全</el-checkbox>
          <el-checkbox label="购物方便">购物方便</el-checkbox>
          <el-checkbox label="干净整洁">干净整洁</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      
      <el-form-item label="是否匿名">
        <el-switch v-model="reviewForm.anonymous"></el-switch>
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="submitReview">提交评价</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { getOrderByOrderNo } from './api/order'
import reviewApi from './api/review'

export default {
  name: 'ReviewForm',
  props: {
    orderId: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      loading: false,
      orderInfo: null,
      orderNo: '',
      houseId: null,
      ownerId: null,
      reviewForm: {
        rating: 5,
        locationRating: 5,
        facilityRating: 5,
        serviceRating: 5,
        valueRating: 5,
        environmentRating: 5,
        cleanlinessRating: 5,
        content: '',
        tags: [],
        images: [],
        anonymous: false
      },
      fileList: [],
      dialogImageUrl: '',
      dialogVisible: false,
      rules: {
        rating: [
          { required: true, message: '请选择总体评分', trigger: 'change' }
        ],
        content: [
          { required: true, message: '请填写评价内容', trigger: 'blur' },
          { min: 10, message: '评价内容最少10个字', trigger: 'blur' }
        ]
      }
    }
  },
  mounted() {
    this.fetchOrderInfo();
  },
  methods: {
    // 获取订单信息
    async fetchOrderInfo() {
      this.loading = true;
      try {
        // 从路由参数或props中获取订单ID
        const orderId = this.$route.params.orderId || this.orderId;
        if (!orderId) {
          this.$message.error('订单ID不能为空');
          this.$router.push('/frontend/orderlist');
          return;
        }

        // 根据订单ID获取订单信息
        const res = await this.$http.get(`/orders/${orderId}`);
        if (res.data && res.data.flag) {
          this.orderInfo = res.data.data;
          this.orderNo = this.orderInfo.orderNo;
          this.houseId = this.orderInfo.houseId;
          this.ownerId = this.orderInfo.ownerId;

          // 检查订单是否已评价
          try {
            const checkRes = await reviewApi.checkOrderReviewed(orderId);
            if (checkRes.data && checkRes.data.flag && checkRes.data.data) {
              this.$message.warning('该订单已经评价过了');
              this.$router.push('/frontend/orderlist');
              return;
            }
          } catch (error) {
            console.error('检查评价状态失败:', error);
          }
        } else {
          this.$message.error('获取订单信息失败');
          this.$router.push('/frontend/orderlist');
        }
      } catch (error) {
        console.error('获取订单信息失败:', error);
        this.$message.error('获取订单信息失败');
        this.$router.push('/frontend/orderlist');
      } finally {
        this.loading = false;
      }
    },
    // 上传图片
    async uploadImage(options) {
      try {
        // 创建FormData对象
        const formData = new FormData();
        formData.append('file', options.file);
        
        // 调用上传API
        const res = await this.$http.post('/upload/image', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        });
        
        if (res.data && res.data.flag) {
          // 上传成功，将图片URL添加到列表中
          const imageUrl = res.data.data;
          this.reviewForm.images.push(imageUrl);
          this.fileList.push({
            name: options.file.name,
            url: imageUrl
          });
          options.onSuccess();
        } else {
          options.onError(new Error(res.data.message || '上传失败'));
        }
      } catch (error) {
        console.error('上传图片失败:', error);
        options.onError(new Error('上传图片失败'));
      }
    },
    
    // 处理图片预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    
    // 处理移除图片
    handleRemove(file, fileList) {
      this.fileList = fileList;
      // 从images数组中移除对应的URL
      const index = this.reviewForm.images.indexOf(file.url);
      if (index !== -1) {
        this.reviewForm.images.splice(index, 1);
      }
    },
    
    // 提交评价
    submitReview() {
      this.$refs.reviewForm.validate(async (valid) => {
        if (valid) {
          try {
            // 计算各方面评分的平均值作为总评分
            const aspectRatings = [
              this.reviewForm.locationRating,
              this.reviewForm.facilityRating,
              this.reviewForm.serviceRating,
              this.reviewForm.valueRating,
              this.reviewForm.environmentRating,
              this.reviewForm.cleanlinessRating
            ];
            const averageRating = aspectRatings.reduce((sum, rating) => sum + rating, 0) / aspectRatings.length;
            
            // 构建评价数据
            const reviewData = {
              orderId: this.$route.params.orderId || this.orderId,
              orderNo: this.orderNo,
              houseId: this.houseId,
              ownerId: this.ownerId,
              rating: this.reviewForm.rating,
              content: this.reviewForm.content,
              locationRating: this.reviewForm.locationRating,
              facilityRating: this.reviewForm.facilityRating,
              serviceRating: this.reviewForm.serviceRating,
              valueRating: this.reviewForm.valueRating,
              environmentRating: this.reviewForm.environmentRating,
              cleanlinessRating: this.reviewForm.cleanlinessRating,
              averageRating: averageRating.toFixed(1),
              tags: this.reviewForm.tags.join(','),
              images: this.reviewForm.images.join(','),
              anonymous: this.reviewForm.anonymous
            };
            
            // 调用API提交评价
            const res = await reviewApi.submitReview(reviewData);
            
            if (res.data && res.data.flag) {
              this.$message.success('评价提交成功');
              this.$emit('review-submitted', res.data.data);
              this.resetForm();
            } else {
              this.$message.error(res.data.message || '评价提交失败');
            }
          } catch (error) {
            console.error('提交评价失败:', error);
            this.$message.error('提交评价失败');
          }
        } else {
          return false;
        }
      });
    },
    
    // 重置表单
    resetForm() {
      this.$refs.reviewForm.resetFields();
      this.fileList = [];
      this.reviewForm.images = [];
      this.reviewForm.tags = [];
    }
  }
}
</script>

<style scoped>
.review-form-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.aspect-ratings {
  margin: 20px 0;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.aspect-ratings h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #606266;
  font-weight: normal;
}
</style> 