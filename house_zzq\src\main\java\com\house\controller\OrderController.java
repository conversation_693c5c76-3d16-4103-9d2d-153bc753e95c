package com.house.controller;

import com.github.pagehelper.PageInfo;
import com.house.common.Result;
import com.house.common.StatusCode;
import com.house.exception.OrderOperationException;
import com.house.pojo.Order;
import com.house.service.OrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 订单控制器
 */
@Slf4j
@RestController
@CrossOrigin(origins = "http://localhost:8080", allowCredentials = "true", maxAge = 3600)
@RequestMapping("/orders")
public class OrderController {

    @Autowired
    private OrderService orderService;

    /**
     * 创建订单
     */
    @PostMapping
    public Result createOrder(@RequestBody Order order) {
        try {
            Order createdOrder = orderService.createOrder(order);
            return new Result(true, StatusCode.SUCCESS, "创建订单成功", createdOrder);
        } catch (OrderOperationException e) {
            return new Result(false, StatusCode.ERROR, e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, StatusCode.ERROR, "创建订单失败");
        }
    }

    /**
     * 根据订单号获取订单详情
     */
    @GetMapping("/{orderNo}")
    public Result getOrderByOrderNo(@PathVariable String orderNo) {
        log.info("开始获取订单详情，订单号: {}", orderNo);
        try {
            Order order = orderService.getOrderByOrderNo(orderNo);
            if (order != null) {
                log.info("成功获取订单详情，订单ID: {}, 状态: {}", order.getId(), order.getStatus());
                return new Result(true, StatusCode.SUCCESS, "获取订单成功", order);
            } else {
                log.warn("订单不存在，订单号: {}", orderNo);
                return new Result(false, StatusCode.ERROR, "订单不存在");
            }
        } catch (Exception e) {
            log.error("获取订单失败，订单号: {}, 错误信息: {}", orderNo, e.getMessage(), e);
            return new Result(false, StatusCode.ERROR, "获取订单失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取订单详情
     */
    @GetMapping("/id/{id}")
    public Result getOrderById(@PathVariable Integer id) {
        try {
            Order order = orderService.getOrderById(id);
            if (order != null) {
                return new Result(true, StatusCode.SUCCESS, "获取订单成功", order);
            } else {
                return new Result(false, StatusCode.ERROR, "订单不存在");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, StatusCode.ERROR, "获取订单失败");
        }
    }

    /**
     * 更新订单状态
     */
    @PutMapping("/{orderNo}/status")
    public Result updateOrderStatus(@PathVariable String orderNo, @RequestParam String status) {
        try {
            boolean result = orderService.updateOrderStatus(orderNo, status);
            if (result) {
                return new Result(true, StatusCode.SUCCESS, "更新订单状态成功");
            } else {
                return new Result(false, StatusCode.ERROR, "更新订单状态失败");
            }
        } catch (OrderOperationException e) {
            return new Result(false, StatusCode.ERROR, e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, StatusCode.ERROR, "更新订单状态失败");
        }
    }

    /**
     * 取消订单
     */
    @PutMapping("/{orderNo}/cancel")
    public Result cancelOrder(@PathVariable String orderNo) {
        try {
            boolean result = orderService.cancelOrder(orderNo);
            if (result) {
                return new Result(true, StatusCode.SUCCESS, "取消订单成功");
            } else {
                return new Result(false, StatusCode.ERROR, "取消订单失败");
            }
        } catch (OrderOperationException e) {
            return new Result(false, StatusCode.ERROR, e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, StatusCode.ERROR, "取消订单失败");
        }
    }

    /**
     * 支付押金
     */
    @PutMapping("/{orderNo}/pay-deposit")
    public Result payDeposit(@PathVariable String orderNo, @RequestParam String paymentMethod) {
        try {
            boolean result = orderService.payDeposit(orderNo, paymentMethod);
            if (result) {
                return new Result(true, StatusCode.SUCCESS, "支付押金成功");
            } else {
                return new Result(false, StatusCode.ERROR, "支付押金失败");
            }
        } catch (OrderOperationException e) {
            return new Result(false, StatusCode.ERROR, e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, StatusCode.ERROR, "支付押金失败");
        }
    }

    /**
     * 通用支付接口
     */
    @PostMapping("/pay")
    public Result payOrder(@RequestBody Map<String, Object> paymentData) {
        try {
            String orderNo = (String) paymentData.get("orderNo");
            String paymentMethod = (String) paymentData.get("paymentMethod");
            Double amount = Double.valueOf(paymentData.get("amount").toString());
            
            if (orderNo == null || paymentMethod == null) {
                return new Result(false, StatusCode.ERROR, "订单号和支付方式不能为空");
            }
            
            // 获取订单
            Order order = orderService.getOrderByOrderNo(orderNo);
            if (order == null) {
                return new Result(false, StatusCode.ERROR, "订单不存在");
            }
            
            // 根据订单状态判断支付类型
            boolean result = false;
            if ("unpaid".equals(order.getStatus())) {
                // 支付押金
                result = orderService.payDeposit(orderNo, paymentMethod);
                if (result) {
                    return new Result(true, StatusCode.SUCCESS, "支付押金成功");
                } else {
                    return new Result(false, StatusCode.ERROR, "支付押金失败");
                }
            } else if ("renting".equals(order.getStatus())) {
                // 支付租金
                result = orderService.payRent(orderNo, paymentMethod);
                if (result) {
                    return new Result(true, StatusCode.SUCCESS, "支付租金成功");
                } else {
                    return new Result(false, StatusCode.ERROR, "支付租金失败");
                }
            } else {
                return new Result(false, StatusCode.ERROR, "当前订单状态不需要支付");
            }
        } catch (OrderOperationException e) {
            return new Result(false, StatusCode.ERROR, e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, StatusCode.ERROR, "支付失败");
        }
    }

    /**
     * 支付租金
     */
    @PutMapping("/{orderNo}/pay-rent")
    public Result payRent(@PathVariable String orderNo, @RequestParam String paymentMethod) {
        try {
            boolean result = orderService.payRent(orderNo, paymentMethod);
            if (result) {
                return new Result(true, StatusCode.SUCCESS, "支付租金成功");
            } else {
                return new Result(false, StatusCode.ERROR, "支付租金失败");
            }
        } catch (OrderOperationException e) {
            return new Result(false, StatusCode.ERROR, e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, StatusCode.ERROR, "支付租金失败");
        }
    }

    /**
     * 确认入住
     */
    @PutMapping("/{orderNo}/check-in")
    public Result confirmCheckIn(@PathVariable String orderNo) {
        try {
            boolean result = orderService.confirmCheckIn(orderNo);
            if (result) {
                return new Result(true, StatusCode.SUCCESS, "确认入住成功");
            } else {
                return new Result(false, StatusCode.ERROR, "确认入住失败");
            }
        } catch (OrderOperationException e) {
            return new Result(false, StatusCode.ERROR, e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, StatusCode.ERROR, "确认入住失败");
        }
    }

    /**
     * 完成订单
     */
    @PutMapping("/{orderNo}/complete")
    public Result completeOrder(@PathVariable String orderNo) {
        try {
            boolean result = orderService.completeOrder(orderNo);
            if (result) {
                return new Result(true, StatusCode.SUCCESS, "完成订单成功");
            } else {
                return new Result(false, StatusCode.ERROR, "完成订单失败");
            }
        } catch (OrderOperationException e) {
            return new Result(false, StatusCode.ERROR, e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, StatusCode.ERROR, "完成订单失败");
        }
    }

    /**
     * 分页获取订单列表
     */
    @GetMapping
    public Result getOrderList(@RequestParam Map<String, Object> params) {
        try {
            PageInfo<Order> pageInfo = orderService.getOrderList(params);
            return new Result(true, StatusCode.SUCCESS, "获取订单列表成功", pageInfo);
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, StatusCode.ERROR, "获取订单列表失败");
        }
    }

    /**
     * 获取用户订单统计
     */
    @GetMapping("/stats/{userId}")
    public Result getOrderStatsByUserId(@PathVariable Integer userId) {
        try {
            Map<String, Object> stats = orderService.getOrderStatsByUserId(userId);
            return new Result(true, StatusCode.SUCCESS, "获取订单统计成功", stats);
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, StatusCode.ERROR, "获取订单统计失败");
        }
    }
    
    /**
     * 批准租房申请
     */
    @PutMapping("/{orderNo}/approve")
    public Result approveApplication(@PathVariable String orderNo, @RequestBody Map<String, Object> requestBody) {
        try {
            String status = (String) requestBody.get("status");
            String remark = (String) requestBody.get("remark");
            
            if (!"approved".equals(status)) {
                return new Result(false, StatusCode.ERROR, "状态参数错误");
            }
            
            Order order = orderService.getOrderByOrderNo(orderNo);
            if (order == null) {
                return new Result(false, StatusCode.ERROR, "申请不存在");
            }
            
            // 检查申请状态是否为application
            if (!"application".equals(order.getStatus())) {
                return new Result(false, StatusCode.ERROR, "申请状态错误，无法批准");
            }
            
            // 更新订单状态为approved
            boolean result = orderService.updateOrderStatus(orderNo, "approved");
            if (result) {
                return new Result(true, StatusCode.SUCCESS, "申请已批准", order);
            } else {
                return new Result(false, StatusCode.ERROR, "批准申请失败");
            }
        } catch (OrderOperationException e) {
            return new Result(false, StatusCode.ERROR, e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, StatusCode.ERROR, "批准申请失败");
        }
    }
} 