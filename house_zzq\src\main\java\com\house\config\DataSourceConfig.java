package com.house.config;

import com.alibaba.druid.pool.DruidDataSource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import javax.sql.DataSource;

@Configuration
public class DataSourceConfig {
    @Value("${spring.datasource.druid.url}")
    String url;

    @Value("${spring.datasource.druid.username}")
    String username;

    @Value("${spring.datasource.druid.password}")
    String password;

    @Value("${spring.datasource.druid.driver-class-name}")
    String driverClassName;

    @Bean
    public DataSource dataSource(Environment env) {
        DruidDataSource druidDataSource = new DruidDataSource();
        druidDataSource.setUrl(url);
        druidDataSource.setPassword(password);
        druidDataSource.setUsername(username);
        druidDataSource.setDriverClassName(driverClassName);
        return druidDataSource;
    }
}
