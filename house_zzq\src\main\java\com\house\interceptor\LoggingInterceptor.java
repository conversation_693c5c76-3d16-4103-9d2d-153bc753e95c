package com.house.interceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import java.util.Enumeration;

/**
 * 日志拦截器
 * 用于记录请求和响应的详细信息
 */
@Slf4j
@Component
public class LoggingInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 记录请求开始时间
        long startTime = System.currentTimeMillis();
        request.setAttribute("startTime", startTime);

        // 记录请求详细信息
        String requestURI = request.getRequestURI();
        String method = request.getMethod();
        String remoteAddr = request.getRemoteAddr();
        
        StringBuilder logMessage = new StringBuilder();
        logMessage.append("\n========================================== 请求开始 ==========================================\n");
        logMessage.append("【请求URI】: ").append(requestURI).append("\n");
        logMessage.append("【请求方法】: ").append(method).append("\n");
        logMessage.append("【客户端IP】: ").append(remoteAddr).append("\n");
        
        // 记录请求头
        logMessage.append("【请求头信息】:\n");
        Enumeration<String> headerNames = request.getHeaderNames();
        while (headerNames.hasMoreElements()) {
            String headerName = headerNames.nextElement();
            String headerValue = request.getHeader(headerName);
            logMessage.append("    ").append(headerName).append(": ").append(headerValue).append("\n");
        }
        
        // 记录请求参数
        logMessage.append("【请求参数】:\n");
        Enumeration<String> parameterNames = request.getParameterNames();
        while (parameterNames.hasMoreElements()) {
            String paramName = parameterNames.nextElement();
            String paramValue = request.getParameter(paramName);
            logMessage.append("    ").append(paramName).append("=").append(paramValue).append("\n");
        }
        
        log.info(logMessage.toString());
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        // 不做任何处理
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 记录请求完成信息
        long startTime = (Long) request.getAttribute("startTime");
        long endTime = System.currentTimeMillis();
        long executionTime = endTime - startTime;
        
        String requestURI = request.getRequestURI();
        int status = response.getStatus();
        
        StringBuilder logMessage = new StringBuilder();
        logMessage.append("\n========================================== 请求完成 ==========================================\n");
        logMessage.append("【请求URI】: ").append(requestURI).append("\n");
        logMessage.append("【响应状态】: ").append(status).append("\n");
        logMessage.append("【处理时间】: ").append(executionTime).append("ms\n");
        
        // 记录响应头
        logMessage.append("【响应头信息】:\n");
        for (String headerName : response.getHeaderNames()) {
            String headerValue = response.getHeader(headerName);
            logMessage.append("    ").append(headerName).append(": ").append(headerValue).append("\n");
        }
        
        log.info(logMessage.toString());
    }
} 