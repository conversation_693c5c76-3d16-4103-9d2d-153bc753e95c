package com.house.pojo;

import lombok.Data;

import java.util.Date;

/**
 * 消息通知实体类
 */
@Data
public class Notification {

    /**
     * 通知ID
     */
    private Integer id;

    /**
     * 接收用户ID
     */
    private Integer userId;

    /**
     * 通知标题
     */
    private String title;

    /**
     * 通知内容
     */
    private String content;

    /**
     * 通知类型：
     * info - 普通信息
     * success - 成功信息
     * warning - 警告信息
     * error - 错误信息
     * order - 订单通知
     * contract - 合同通知
     * payment - 支付通知
     */
    private String type;

    /**
     * 关联ID，如订单ID、合同ID等
     */
    private String linkId;

    /**
     * 关联类型：
     * order - 订单
     * contract - 合同
     * payment - 支付
     * house - 房源
     */
    private String linkType;

    /**
     * 是否已读
     */
    private Boolean read;

    /**
     * 已读时间
     */
    private Date readTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
} 