package com.house.controller;

import com.house.common.Result;
import com.house.common.StatusCode;
import com.house.pojo.Review;
import com.house.pojo.User;
import com.house.pojo.UserList;
import com.house.service.ReviewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 评价Controller
 */
@RestController
@RequestMapping("/reviews")
public class ReviewController {

    @Autowired
    private ReviewService reviewService;

    /**
     * 获取房屋评价统计信息
     * @param houseId 房屋ID
     * @return 评价统计信息
     */
    @GetMapping("/stats/{houseId}")
    public Result getReviewStats(@PathVariable Integer houseId) {
        try {
            Map<String, Object> stats = reviewService.getReviewStats(houseId);
            return new Result(true, StatusCode.SUCCESS, "获取评价统计成功", stats);
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, StatusCode.ERROR, "获取评价统计失败");
        }
    }

    /**
     * 获取房屋所有评价
     * @param houseId 房屋ID
     * @return 评价列表
     */
    @GetMapping("/house/{houseId}")
    public Result getReviewsByHouseId(@PathVariable Integer houseId) {
        try {
            List<Review> reviews = reviewService.getReviewsByHouseId(houseId);
            return new Result(true, StatusCode.SUCCESS, "获取评价列表成功", reviews);
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, StatusCode.ERROR, "获取评价列表失败");
        }
    }

    /**
     * 获取房东的所有评价
     * @param ownerId 房东ID
     * @return 评价列表
     */
    @GetMapping("/owner/{ownerId}")
    public Result getReviewsByOwnerId(@PathVariable Integer ownerId) {
        try {
            List<Review> reviews = reviewService.getReviewsByOwnerId(ownerId);
            return new Result(true, StatusCode.SUCCESS, "获取房东评价列表成功", reviews);
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, StatusCode.ERROR, "获取房东评价列表失败");
        }
    }

    /**
     * 提交评价
     * @param review 评价对象
     * @return 提交结果
     */
    @PostMapping
    public Result addReview(@RequestBody Review review) {
        try {
            // 获取当前登录用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            UserList user = (UserList) authentication.getPrincipal();
            
            // 设置评价用户信息
            review.setUserId(user.getId());
            review.setUserName(user.getName());
            review.setUserAvatar(user.getAvatar());
            
            // 添加评价
            Review savedReview = reviewService.addReview(review);
            return new Result(true, StatusCode.SUCCESS, "提交评价成功", savedReview);
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, StatusCode.ERROR, "提交评价失败");
        }
    }

    /**
     * 房东回复评价
     * @param reviewId 评价ID
     * @param reply 回复内容
     * @return 回复结果
     */
    @PostMapping("/{reviewId}/reply")
    public Result replyReview(@PathVariable Integer reviewId, @RequestParam String reply) {
        try {
            boolean success = reviewService.replyReview(reviewId, reply);
            if (success) {
                return new Result(true, StatusCode.SUCCESS, "回复评价成功");
            } else {
                return new Result(false, StatusCode.ERROR, "回复评价失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, StatusCode.ERROR, "回复评价失败");
        }
    }

    /**
     * 检查用户是否已评价订单
     * @param orderId 订单ID
     * @return 是否已评价
     */
    @GetMapping("/check/{orderId}")
    public Result checkReviewed(@PathVariable Integer orderId) {
        try {
            boolean hasReviewed = reviewService.hasReviewed(orderId);
            return new Result(true, StatusCode.SUCCESS, "查询成功", hasReviewed);
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, StatusCode.ERROR, "查询失败");
        }
    }
} 