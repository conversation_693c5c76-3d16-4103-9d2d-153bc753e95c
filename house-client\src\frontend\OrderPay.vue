<template>
  <div class="order-pay-page content-scroll">
    <Header />
    <div class="container">
      <div class="pay-card">
        <h2>支付订单</h2>
        <el-divider></el-divider>
        
        <div class="pay-content" v-loading="loading">
          <!-- 订单信息 -->
          <div class="order-summary">
            <div class="summary-header">
              <div class="order-no">订单编号：{{ orderNo }}</div>
              <div class="countdown" v-if="countdown > 0">
                支付剩余时间：<span class="time">{{ formatCountdown }}</span>
              </div>
            </div>
            
            <div class="summary-content">
              <div class="house-info" v-if="order && order.house">
                <div class="house-image">
                  <img :src="order.house.images ? order.house.images.split(',')[0] : '/img/showcase.jpg'" alt="房屋图片">
                </div>
                <div class="house-details">
                  <h3>{{ order.house.title }}</h3>
                  <p class="address">{{ order.house.address }}</p>
                  <p class="lease-term">租期：{{ formatDate(order.startDate) }} 至 {{ formatDate(order.endDate) }}（{{ order.duration }}个月）</p>
                </div>
              </div>
              
              <div class="price-info">
                <div class="price-item">
                  <span class="label">月租：</span>
                  <span class="value">¥{{ order ? order.monthlyPrice : 0 }}</span>
                </div>
                <div class="price-item">
                  <span class="label">押金：</span>
                  <span class="value">¥{{ order ? order.deposit : 0 }}</span>
                </div>
                <div class="price-item">
                  <span class="label">服务费：</span>
                  <span class="value">¥{{ order ? order.serviceFee : 0 }}</span>
                </div>
                <div class="price-item total">
                  <span class="label">总计：</span>
                  <span class="value total-price">¥{{ order ? order.totalPrice : 0 }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 支付方式选择 -->
          <div class="payment-methods">
            <h3>选择支付方式</h3>
            <div class="methods-container">
              <div class="method-item" :class="{active: payMethod === 'alipay'}" @click="payMethod = 'alipay'">
                <i class="el-icon-money"></i>
                <span class="method-name">支付宝</span>
              </div>
              <div class="method-item" :class="{active: payMethod === 'wechat'}" @click="payMethod = 'wechat'">
                <i class="el-icon-chat-dot-square"></i>
                <span class="method-name">微信支付</span>
              </div>
              <div class="method-item" :class="{active: payMethod === 'bank'}" @click="payMethod = 'bank'">
                <i class="el-icon-bank-card"></i>
                <span class="method-name">银行卡支付</span>
              </div>
            </div>
          </div>
          
          <!-- 支付区域 -->
          <div class="payment-action">
            <el-button type="primary" size="large" :disabled="!payMethod" @click="submitPayment" :loading="paying">
              立即支付 ¥{{ order ? order.totalPrice : 0 }}
            </el-button>
          </div>
          
          <!-- 支付提示 -->
          <div class="payment-tips">
            <el-alert
              title="安全提示"
              type="info"
              description="请在新打开的页面上完成支付，支付完成前请不要关闭此窗口。"
              show-icon>
            </el-alert>
          </div>
        </div>
        
        <!-- 底部按钮 -->
        <div class="pay-footer">
          <el-button @click="goBack">返回</el-button>
          <el-button type="danger" @click="cancelOrder">取消订单</el-button>
        </div>
      </div>
    </div>
    <Footer />
    
    <!-- 支付成功对话框 -->
    <el-dialog
      title="支付成功"
      :visible.sync="paySuccessDialogVisible"
      width="30%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false">
      <div class="success-content">
        <i class="el-icon-success success-icon"></i>
        <h3>支付成功</h3>
        <p>您的订单已完成支付，感谢您的使用!</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="goToContract">查看合同</el-button>
        <el-button @click="goToOrderList">返回订单列表</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Header from './components/Header'
import Footer from './components/Footer'
import { getOrderByOrderNo, cancelOrder, payOrder } from './api/order'
import { getUserInfo } from '../utils/auth'
import moment from 'moment'

export default {
  name: 'OrderPay',
  components: {
    Header,
    Footer
  },
  data() {
    return {
      orderNo: '',
      order: null,
      loading: false,
      paying: false,
      payMethod: '',
      countdown: 15 * 60, // 15分钟支付倒计时
      countdownTimer: null,
      paySuccessDialogVisible: false,
      userInfo: null
    }
  },
  computed: {
    // 格式化倒计时
    formatCountdown() {
      const minutes = Math.floor(this.countdown / 60)
      const seconds = this.countdown % 60
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    }
  },
  created() {
    this.orderNo = this.$route.params.orderNo
    if (!this.orderNo) {
      this.$message.error('订单号不存在')
      this.$router.push('/frontend/orderlist')
      return
    }
    
    // 获取用户信息
    this.userInfo = getUserInfo()
    if (!this.userInfo) {
      this.$message.warning('请先登录')
      this.$router.push('/frontend/login?redirect=/frontend/orderpay/' + this.orderNo)
      return
    }
    
    // 获取订单详情
    this.fetchOrderDetail()
    
    // 开始倒计时
    this.startCountdown()
  },
  beforeDestroy() {
    // 清除倒计时定时器
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer)
    }
  },
  methods: {
    // 获取订单详情
    async fetchOrderDetail() {
      this.loading = true
      
      try {
        const res = await getOrderByOrderNo(this.orderNo)
        if (res.data && res.data.success) {
          this.order = res.data.data
          
          // 检查用户权限
          if (this.userInfo.id !== this.order.tenantId) {
            this.$message.warning('您无权支付此订单')
            this.$router.push('/frontend/orderlist')
            return
          }
          
          // 检查订单状态
          if (this.order.status !== 'unpaid') {
            this.$message.warning('此订单无需支付')
            this.$router.push('/frontend/orderdetail/' + this.orderNo)
            return
          }
          
          // 模拟添加房源信息
          this.order.house = {
            title: '精装修两室一厅，采光好',
            address: '北京市海淀区西二旗大街XX号',
            images: '/img/showcase.jpg'
          }
        } else {
          this.$message.error('获取订单信息失败')
          this.$router.push('/frontend/orderlist')
        }
      } catch (error) {
        console.error('获取订单信息失败:', error)
        this.$message.error('获取订单信息失败')
        this.$router.push('/frontend/orderlist')
      } finally {
        this.loading = false
      }
    },
    
    // 开始倒计时
    startCountdown() {
      this.countdownTimer = setInterval(() => {
        if (this.countdown > 0) {
          this.countdown--
        } else {
          clearInterval(this.countdownTimer)
          this.$message.warning('支付超时，请重新下单')
          this.$router.push('/frontend/orderlist')
        }
      }, 1000)
    },
    
    // 格式化日期
    formatDate(dateStr) {
      return moment(dateStr).format('YYYY-MM-DD')
    },
    
    // 提交支付
    async submitPayment() {
      if (!this.payMethod) {
        this.$message.warning('请选择支付方式')
        return
      }
      
      this.paying = true
      
      try {
        // 模拟支付过程
        setTimeout(async () => {
          const paymentData = {
            orderNo: this.orderNo,
            paymentMethod: this.payMethod,
            amount: this.order.totalPrice
          }
          
          const res = await payOrder(paymentData)
          if (res.data && res.data.success) {
            // 清除倒计时
            if (this.countdownTimer) {
              clearInterval(this.countdownTimer)
            }
            
            // 显示支付成功对话框
            this.paySuccessDialogVisible = true
          } else {
            this.$message.error(res.data && res.data.message ? res.data.message : '支付失败')
          }
          
          this.paying = false
        }, 2000)
      } catch (error) {
        console.error('支付失败:', error)
        this.$message.error('支付失败')
        this.paying = false
      }
    },
    
    // 取消订单
    async cancelOrder() {
      try {
        await this.$confirm('确定要取消订单吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const res = await cancelOrder(this.orderNo)
        if (res.data && res.data.success) {
          this.$message.success('订单已取消')
          this.$router.push('/frontend/orderlist')
        } else {
          this.$message.error(res.data.message || '取消订单失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('取消订单失败:', error)
          this.$message.error('取消订单失败')
        }
      }
    },
    
    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },
    
    // 查看合同
    goToContract() {
      this.$router.push('/frontend/contract/' + this.orderNo)
    },
    
    // 返回订单列表
    goToOrderList() {
      this.$router.push('/frontend/orderlist')
    }
  }
}
</script>

<style scoped>
.order-pay-page {
  background-color: #f8f8f8;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.container {
  flex: 1;
  max-width: 1200px;
  margin: 30px auto;
  padding: 0 20px;
}

.pay-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 30px;
}

h2 {
  font-size: 24px;
  color: #333;
  margin-bottom: 20px;
}

h3 {
  font-size: 18px;
  color: #333;
  margin: 20px 0 15px;
}

.order-summary {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 30px;
}

.summary-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px dashed #ebeef5;
}

.order-no {
  font-size: 14px;
  color: #606266;
}

.countdown {
  font-size: 14px;
  color: #f56c6c;
}

.time {
  font-weight: bold;
}

.summary-content {
  display: flex;
}

.house-info {
  display: flex;
  flex: 3;
  border-right: 1px solid #ebeef5;
  padding-right: 20px;
}

.house-image {
  width: 120px;
  height: 80px;
  overflow: hidden;
  border-radius: 4px;
  margin-right: 15px;
}

.house-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.house-details {
  flex: 1;
}

.house-details h3 {
  margin: 0 0 8px;
  font-size: 16px;
  color: #303133;
}

.house-details p {
  margin: 5px 0;
  font-size: 13px;
  color: #606266;
}

.address {
  color: #909399;
}

.price-info {
  flex: 1;
  padding-left: 20px;
}

.price-item {
  margin: 8px 0;
  font-size: 14px;
  color: #606266;
}

.price-item.total {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px dashed #ebeef5;
}

.label {
  display: inline-block;
  width: 70px;
  color: #909399;
}

.value {
  font-weight: bold;
}

.total-price {
  color: #f56c6c;
  font-size: 18px;
}

.payment-methods {
  margin-bottom: 30px;
}

.methods-container {
  display: flex;
  gap: 20px;
}

.method-item {
  flex: 1;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s;
}

.method-item:hover {
  border-color: #c6e2ff;
}

.method-item.active {
  border-color: #409EFF;
  background-color: #ecf5ff;
}

.method-item i {
  font-size: 28px;
  margin-bottom: 10px;
  display: block;
}

.method-item .method-name {
  display: block;
  font-size: 16px;
}

.payment-action {
  text-align: center;
  margin: 30px 0;
}

.payment-action .el-button {
  padding: 12px 40px;
  font-size: 18px;
}

.payment-tips {
  margin: 30px 0;
}

.pay-footer {
  margin-top: 40px;
  text-align: center;
}

.success-content {
  text-align: center;
  padding: 20px 0;
}

.success-icon {
  font-size: 60px;
  color: #67c23a;
  margin-bottom: 20px;
}

.success-content h3 {
  font-size: 20px;
  margin-bottom: 10px;
}

.success-content p {
  color: #606266;
}

@media (max-width: 768px) {
  .summary-content {
    flex-direction: column;
  }
  
  .house-info {
    border-right: none;
    border-bottom: 1px solid #ebeef5;
    padding-right: 0;
    padding-bottom: 15px;
    margin-bottom: 15px;
  }
  
  .price-info {
    padding-left: 0;
  }
  
  .methods-container {
    flex-direction: column;
  }
}
</style> 