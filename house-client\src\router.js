import Vue from 'vue'
import Router from 'vue-router'
import Page404 from './components/404.vue'
import { getFrontendUser, getBackstageUser, isAdmin, isOwner, removeBackstageUser } from './utils/auth'

Vue.use(Router)

const router = new Router({
  mode: 'history',
  routes: [
    {
      path: '/',
      redirect: '/frontend/home'
    },
    // 后台管理路由
    {
      path: '/backstage/login',
      name: 'BackstageLogin',
      component: () => import('./backstage/Login'),
      meta: { allowAnonymous: true }
    },
    {
      path: '/backstage/register',
      name: 'BackstageRegister',
      component: () => import('./backstage/Register'),
      meta: { allowAnonymous: true }
    },
    
    // 后台管理系统
    {
      path: '/backstage',
      component: () => import('./backstage/Layout'),
      redirect: to => {
        // 根据用户角色决定重定向到哪个页面
        if (isAdmin(false)) {
          return '/backstage/admin';
        }
        return '/backstage/dashboard';
      },
      meta: { requiresBackstageAuth: true, requiresOwnerOrAdmin: true },
      children: [
        {
          path: 'dashboard',
          name: 'BackstageDashboard',
          component: () => import('./backstage/Dashboard'),
          meta: { 
            requiresBackstageAuth: true,
            requiresOwnerOrAdmin: true,
            title: '首页'
          }
        },
        {
          path: 'admin',
          name: 'AdminDashboard',
          component: () => import('./backstage/AdminDashboard'),
          meta: { 
            requiresAdmin: true,
            title: '管理员控制台'
          }
        },
        {
          path: 'users',
          name: 'BackstageUsers',
          component: () => import('./backstage/UserList'),
          meta: { 
            requiresAdmin: true,
            title: '用户管理'
          }
        },
        {
          path: 'houses',
          name: 'BackstageHouses',
          component: () => import('./backstage/HouseManagement'),
          meta: { 
            requiresAdmin: true,
            title: '房源管理'
          }
        },
        {
          path: 'my-houses',
          name: 'BackstageMyHouses',
          component: () => import('./backstage/MyHouses'),
          meta: { 
            requiresBackstageAuth: true,
            requiresOwnerOrAdmin: true,
            title: '我的房源'
          }
        },
        {
          path: 'house-add',
          name: 'BackstageHouseAdd',
          component: () => import('./backstage/HouseAdd'),
          meta: { 
            requiresBackstageAuth: true,
            requiresOwnerOrAdmin: true,
            title: '发布房源'
          }
        },
        {
          path: 'orders/applications',
          name: 'BackstageApplications',
          component: () => import('./backstage/ApplicationList'),
          meta: {
            requiresBackstageAuth: true,
            requiresOwnerOrAdmin: true,
            title: '订单列表'
          }
        },
        {
          path: 'contracts',
          name: 'BackstageContracts',
          component: () => import('./backstage/ContractManagement'),
          meta: { 
            requiresBackstageAuth: true,
            requiresOwnerOrAdmin: true,
            title: '合同管理'
          }
        },

        {
          path: 'reviews',
          name: 'BackstageReviews',
          component: () => import('./backstage/ReviewReply'),
          meta: { 
            requiresBackstageAuth: true,
            requiresOwnerOrAdmin: true,
            title: '评价管理'
          }
        },
        {
          path: 'tenant-review/:orderId',
          name: 'TenantReview',
          component: () => import('./backstage/TenantReviewForm'),
          meta: { 
            requiresBackstageAuth: true,
            requiresOwnerOrAdmin: true,
            title: '评价租客',
            hidden: true
          }
        },
        {
          path: 'chat-messages',
          name: 'BackstageChatMessages',
          component: () => import('./backstage/ChatMessages'),
          meta: { 
            requiresBackstageAuth: true,
            requiresOwnerOrAdmin: true,
            title: '房源咨询消息'
          }
        },
        {
          path: 'profile',
          name: 'BackstageProfile',
          component: () => import('./backstage/Profile'),
          meta: { 
            requiresBackstageAuth: true,
            requiresOwnerOrAdmin: true,
            title: '个人信息'
          }
        },
        {
          path: 'settings',
          name: 'BackstageSettings',
          component: () => import('./backstage/Settings'),
          meta: { 
            requiresBackstageAuth: true,
            requiresOwnerOrAdmin: true,
            title: '系统设置'
          }
        }
      ]
    },
  
    // 前台独立路由页面
    {
      path: '/frontend/login',
      name: 'FrontendLogin',
      component: () => import('./frontend/Login'),
      meta: { allowAnonymous: true }
    },
    {
      path: '/frontend/register',
      name: 'FrontendRegister',
      component: () => import('./frontend/Register'),
      meta: { allowAnonymous: true }
    },
    {
      path: '/frontend/houselist',
      name: 'FrontendHouseList',
      component: () => import('./frontend/HouseList'),
      meta: { allowAnonymous: true }
    },
    {
      path: '/frontend/housedetail/:id',
      name: 'FrontendHouseDetail',
      component: () => import('./frontend/HouseDetail'),
      meta: { allowAnonymous: true }
    },
    {
      path: '/frontend/usercenter',
      name: 'FrontendUserCenter',
      component: () => import('./frontend/UserCenter'),
      meta: { requiresAuth: true }
    },
    {
      path: '/frontend/apply/:id',
      name: 'RentApplication',
      component: () => import('./frontend/RentApplication'),
      meta: { requiresAuth: true }
    },
    {
      path: '/frontend/orderlist',
      name: 'OrderList',
      component: () => import('./frontend/OrderList'),
      meta: { requiresAuth: true }
    },
    {
      path: '/frontend/review/:orderId',
      name: 'Review',
      component: () => import('./frontend/ReviewForm'),
      meta: { requiresAuth: true }
    },
    {
      path: '/frontend/userreviews',
      name: 'UserReviews',
      component: () => import('./frontend/UserReviews'),
      meta: { requiresAuth: true }
    },
    {
      path: '/frontend/messagecenter',
      name: 'MessageCenter',
      component: () => import('./frontend/MessageCenter'),
      meta: { requiresAuth: true }
    },
    {
      path: '/frontend/orderconfirm/:id',
      name: 'OrderConfirm',
      component: () => import('./frontend/OrderConfirm'),
      meta: { requiresAuth: true }
    },
    {
      path: '/frontend/orderpay/:orderNo',
      name: 'OrderPay',
      component: () => import('./frontend/OrderPay'),
      meta: { requiresAuth: true }
    },
    {
      path: '/frontend/payment/:orderNo',
      name: 'PaymentPage',
      component: () => import('./frontend/PaymentPage'),
      meta: { requiresAuth: true }
    },
    {
      path: '/frontend/contract/:orderNo',
      name: 'Contract',
      component: () => import('./frontend/Contract'),
      meta: { requiresAuth: true }
    },
    {
      path: '/frontend/orderdetail/:orderNo',
      name: 'OrderDetail',
      component: () => import('./frontend/OrderDetail'),
      meta: { requiresAuth: true }
    },
    {
      path: '/frontend/checkin/:orderNo',
      name: 'CheckIn',
      component: () => import('./frontend/CheckIn'),
      meta: { requiresAuth: true }
    },
    
    // 前台主路由
    {
      path: '/frontend',
      component: () => import('./frontend/Home'),
      children: [
        {
          path: 'home',
          name: 'FrontendHome',
          component: () => import('./frontend/Home'),
          meta: { allowAnonymous: true }
        }
      ]
    },
    {
      path: '*',
      name: '404',
      component: Page404,
      meta: { allowAnonymous: true }
    }
  ]
})

// 全局路由守卫
router.beforeEach((to, from, next) => {
  const userInfo = getFrontendUser(); // 获取前台用户信息
  const isLoggedInFrontend = userInfo && userInfo.token;

  const backstageUserInfo = getBackstageUser(); // 获取后台用户信息
  const isLoggedInBackstage = backstageUserInfo && backstageUserInfo.token;
  
  // 允许匿名访问的路由
  if (to.matched.some(record => record.meta.allowAnonymous)) {
    next();
    return;
  }
  
  // 前台路由守卫
  if (to.matched.some(record => record.meta.requiresAuth)) {
    if (!isLoggedInFrontend) {
      // 未登录跳转到前台登录页
      next('/frontend/login');
      return;
    }
    
    // 已登录用户允许访问
    next();
    return;
  }

  // 后台路由守卫 - 需要房东或管理员权限
  if (to.matched.some(record => record.meta.requiresOwnerOrAdmin)) {
    if (!isLoggedInBackstage) {
      // 未登录跳转到后台登录页
      next('/backstage/login');
      return;
    }
    
    // 检查是否是房东或管理员
    if (!isAdmin(false) && !isOwner(false)) {
      // 既不是房东也不是管理员，清除后台登录状态并跳转到后台登录页
      console.log('用户没有后台权限，清除登录状态并跳转到后台登录页');
      removeBackstageUser();
      next('/backstage/login');
      return;
    }
    
    // 房东或管理员允许访问
    next();
    return;
  }

  // 后台路由守卫
  if (to.matched.some(record => record.meta.requiresBackstageAuth)) {
    if (!isLoggedInBackstage) {
      // 未登录跳转到后台登录页
      next('/backstage/login');
      return;
    }
    
    // 已登录用户允许访问
    next();
    return;
  }

  // 需要管理员权限的路由
  if (to.matched.some(record => record.meta.requiresAdmin)) {
    if (!isLoggedInBackstage) { // 只检查后台管理员权限
      // 未登录跳转到后台登录页
      next('/backstage/login');
      return;
    }
    
    if (!isAdmin(false)) { // 只检查后台管理员权限
      // 非管理员跳转到前台首页
      next('/frontend/home');
      return;
    }
    
    // 管理员允许访问
    next();
    return;
  }
  
  // 需要房东权限的路由
  if (to.matched.some(record => record.meta.requiresOwner)) {
    if (!isLoggedInBackstage) { // 只检查后台房东权限
      // 未登录跳转到后台登录页
      next('/backstage/login');
      return;
    }
    
    if (!isOwner(false)) { // 只检查后台房东权限
      // 非房东跳转到前台首页
      next('/frontend/home');
      return;
    }
    
    // 房东允许访问
    next();
    return;
  }
  
  // 默认允许访问
  next();
});

export default router;
