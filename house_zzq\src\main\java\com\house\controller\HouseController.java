package com.house.controller;

import com.github.pagehelper.PageInfo;
import com.house.common.Result;
import com.house.dto.HousePageQueryDTO;
import com.house.dto.HouseQueryDTO;
import com.house.pojo.House;
import com.house.service.HouseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 房源控制器
 */
@RestController
@CrossOrigin(origins = "*", maxAge = 3600)
public class HouseController {
    
    @Autowired
    private HouseService houseService;
    
    /**
     * 获取所有房源列表
     *
     * @return 房源列表
     */
    @GetMapping("/house/getallhouselist")
    public Result<List<House>> getAllHouseList() {
        List<House> houseList = houseService.getAllHouseList();
        return Result.success("获取房源列表成功", houseList);
    }
    
    /**
     * 获取热门房源列表（按点击量排序）
     *
     * @param limit 获取数量
     * @return 热门房源列表
     */
    @GetMapping("/house/gethothouses")
    public Result<List<House>> getHotHouses(@RequestParam(value = "limit", defaultValue = "4") int limit) {
        List<House> hotHouses = houseService.getHotHouseList(limit);
        return Result.success("获取热门房源成功", hotHouses);
    }
    
    /**
     * 获取推荐房源列表（首页展示用）
     *
     * @param limit 获取数量
     * @return 推荐房源列表
     */
    @GetMapping("/house/getrecommendhouses")
    public Result<List<House>> getRecommendHouses(@RequestParam(value = "limit", defaultValue = "4") int limit) {
        List<House> hotHouses = houseService.getHotHouseList(limit);
        return Result.success("获取推荐房源成功", hotHouses);
    }
    
    /**
     * 根据条件查询房源
     *
     * @param queryDTO 查询条件
     * @return 房源列表
     */
    @PostMapping("/house/gethouselistbycondition")
    public Result<List<House>> getHouseListByCondition(@RequestBody HouseQueryDTO queryDTO) {
        List<House> houseList = houseService.getHouseListByCondition(queryDTO);
        return Result.success("获取房源列表成功", houseList);
    }
    
    /**
     * 获取房东的房源列表
     *
     * @param ownerId 房东用户ID
     * @return 房源列表
     */
    @GetMapping("/house/getownerhouselist")
    public Result<List<House>> getOwnerHouseList(@RequestParam("ownerId") Integer ownerId) {
        List<House> houseList = houseService.getHouseListByUserId(ownerId);
        return Result.success("获取房东房源列表成功", houseList);
    }
    
    /**
     * 分页查询房东的房源列表
     *
     * @param queryDTO 查询条件
     * @return 分页房源列表
     */
    @PostMapping("/house/getownerhousepage")
    public Result<PageInfo<House>> getOwnerHouseListPage(@RequestBody HousePageQueryDTO queryDTO) {
        PageInfo<House> pageInfo = houseService.getHouseListByOwnerIdPage(queryDTO);
        return Result.success("获取房东房源列表成功", pageInfo);
    }
    
    /**
     * 获取房源详情
     *
     * @param houseId 房源ID
     * @return 房源详情
     */
    @GetMapping("/house/gethousedetail")
    public Result<House> getHouseDetail(@RequestParam("houseId") Integer houseId) {
        House house = houseService.getHouseDetail(houseId);
        if (house != null) {
            return Result.success("获取房源详情成功", house);
        } else {
            return Result.error("房源不存在");
        }
    }
    
    /**
     * 获取房源详情（RESTful风格）
     *
     * @param houseId 房源ID
     * @return 房源详情
     */
    @GetMapping("/houses/{houseId}")
    public Result<House> getHouseById(@PathVariable("houseId") Integer houseId) {
        House house = houseService.getHouseDetail(houseId);
        if (house != null) {
            return Result.success("获取房源详情成功", house);
        } else {
            return Result.error("房源不存在");
        }
    }
    
    /**
     * 增加房源点击量
     *
     * @param houseId 房源ID
     * @return 操作结果
     */
    @GetMapping("/house/increaseviewcount")
    public Result<Object> increaseViewCount(@RequestParam("houseId") Integer houseId) {
        boolean result = houseService.increaseViewCount(houseId);
        if (result) {
            return Result.success("增加点击量成功");
        } else {
            return Result.error("增加点击量失败");
        }
    }
    
    /**
     * 增加房源点击量（RESTful风格）
     *
     * @param houseId 房源ID
     * @return 操作结果
     */
    @PutMapping("/house/increaseviewcount/{houseId}")
    public Result<Object> increaseViewCountRestful(@PathVariable("houseId") Integer houseId) {
        boolean result = houseService.increaseViewCount(houseId);
        if (result) {
            return Result.success("增加点击量成功");
        } else {
            return Result.error("增加点击量失败");
        }
    }
    
    /**
     * 获取用户发布的房源
     *
     * @param userId 用户ID
     * @return 房源列表
     */
    @GetMapping("/house/gethouselistbyuserid")
    public Result<List<House>> getHouseListByUserId(@RequestParam("userId") Integer userId) {
        List<House> houseList = houseService.getHouseListByUserId(userId);
        return Result.success("获取用户房源列表成功", houseList);
    }
    
    /**
     * 添加房源
     *
     * @param house 房源信息
     * @return 添加结果
     */
    @PostMapping("/house/addhouse")
    public Result<House> addHouse(@RequestBody House house) {
        boolean result = houseService.addHouse(house);
        if (result) {
            return Result.success("添加房源成功", house);
        } else {
            return Result.error("添加房源失败");
        }
    }
    
    /**
     * 更新房源信息
     *
     * @param house 房源信息
     * @return 更新结果
     */
    @PostMapping("/house/updatehouse")
    public Result<House> updateHouse(@RequestBody House house) {
        boolean result = houseService.updateHouse(house);
        if (result) {
            return Result.success("更新房源成功", house);
        } else {
            return Result.error("更新房源失败");
        }
    }
    
    /**
     * 删除房源
     *
     * @param houseId 房源ID
     * @return 删除结果
     */
    @DeleteMapping("/house/deletehouse")
    public Result<Object> deleteHouse(@RequestParam("houseId") Integer houseId) {
        boolean result = houseService.deleteHouse(houseId);
        if (result) {
            return Result.success("删除房源成功");
        } else {
            return Result.error("删除房源失败");
        }
    }
}
