package com.house.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.house.constants.OrderStatus;
import com.house.exception.OrderOperationException;
import com.house.mapper.OrderMapper;
import com.house.mapper.HouseMapper;
import com.house.dao.HouseListDao;
import com.house.dao.UserDao;
import com.house.pojo.House;
import com.house.pojo.HouseList;
import com.house.pojo.Order;
import com.house.pojo.User;
import com.house.pojo.UserList;
import com.house.dao.UserListDao;
import com.house.service.NotificationService;
import com.house.service.OrderService;
import com.house.service.PaymentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 订单服务实现类
 */
@Slf4j
@Service
public class OrderServiceImpl implements OrderService {

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private HouseMapper houseMapper;

    @Autowired
    private HouseListDao houseListDao;

    @Autowired
    private UserDao userDao;

    @Autowired
    private UserListDao userListDao;

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private NotificationService notificationService;




    /**
     * 生成唯一订单号
     */
    private String generateOrderNo() {
        return "ORD" + System.currentTimeMillis() + String.format("%04d", new Random().nextInt(10000));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Order createOrder(Order order) {
        // 检查房源是否存在
        HouseList houseList = houseListDao.selectHouseById(order.getHouseId());
        if (houseList == null) {
            throw new OrderOperationException("房源不存在");
        }

        // 检查租客是否存在
        UserList tenant = userListDao.findUserListById(order.getTenantId());
        if (tenant == null) {
            throw new OrderOperationException("租客用户不存在");
        }
        
        // 检查租客个人信息是否存在
        UserList tenantInfo = userListDao.findUserListById(order.getTenantId());
        if (tenantInfo == null) {
            throw new OrderOperationException("租客个人信息不存在");
        }

        // 检查房东是否存在
        Integer ownerId;
        if (order.getOwnerId() != null && order.getOwnerId() > 0) {
            // 优先使用前端传入的ownerId
            ownerId = order.getOwnerId();
        } else {
            // 如果前端没传或传入无效值，则从房源中获取
            ownerId = houseList.getUserlist_Id();
            order.setOwnerId(ownerId);
        }

        UserList owner = userListDao.findUserListById(ownerId);
        if (owner == null) {
            throw new OrderOperationException("房东不存在");
        }
        
        // 检查房东个人信息是否存在
        UserList ownerInfo = userListDao.findUserListById(ownerId);
        if (ownerInfo == null) {
            throw new OrderOperationException("房东个人信息不存在");
        }

        // 生成订单号
        order.setOrderNo(generateOrderNo());
        // 设置初始状态为租房申请，而不是直接设置为待支付
        order.setStatus("application");
        // 设置时间
        Date now = new Date();
        order.setCreateTime(now);
        order.setUpdateTime(now);

        // 插入订单
        orderMapper.insert(order);

        // 发送通知给租客
        notificationService.sendNotification(
                String.valueOf(order.getTenantId()),
                "租房申请已提交",
                "您的租房申请 " + order.getOrderNo() + " 已成功提交，请等待房东审核。",
                "order",
                order.getOrderNo(),
                "order"
        );

        // 发送通知给房东
        notificationService.sendNotification(
                String.valueOf(order.getOwnerId()),
                "新租房申请",
                "您有一个新的租房申请 " + order.getOrderNo() + "，租客 " + tenantInfo.getName() + " 申请租赁您的房源 " + houseList.getAddress() + "。",
                "order",
                order.getOrderNo(),
                "order"
        );

        return order;
    }

    @Override
    public Order getOrderByOrderNo(String orderNo) {
        log.debug("开始查询订单，订单号: {}", orderNo);
        Order order = orderMapper.selectByOrderNo(orderNo);
        if (order != null) {
            log.debug("查询到订单基本信息: ID={}, 状态={}, 房源ID={}, 租客ID={}, 房东ID={}",
                     order.getId(), order.getStatus(), order.getHouseId(), order.getTenantId(), order.getOwnerId());
            // 获取房源信息
            if (order.getHouseId() != null) {
                try {
                    House house = houseMapper.getHouseDetail(order.getHouseId());
                    if (house != null) {
                        order.setHouseAddress(house.getAddress());
                        order.setHouseArea(house.getArea());
                        order.setHouse(house); // 设置完整的房源信息
                    }
                } catch (Exception e) {
                    log.error("获取房源信息失败: {}", e.getMessage());
                }
            }

            // 获取房东信息
            if (order.getOwnerId() != null) {
                try {
                    UserList owner = userListDao.findUserListById(order.getOwnerId());
                    if (owner != null) {
                        order.setOwner(owner); // 设置完整的房东信息
                    }
                } catch (Exception e) {
                    log.error("获取房东信息失败: {}", e.getMessage());
                }
            }

            // 获取租客信息
            if (order.getTenantId() != null) {
                try {
                    UserList tenant = userListDao.findUserListById(order.getTenantId());
                    if (tenant != null) {
                        order.setTenantName(tenant.getName());
                        order.setTenantPhone(tenant.getPhone());
                    }
                } catch (Exception e) {
                    log.error("获取租客信息失败: {}", e.getMessage());
                }
            }
        } else {
            log.warn("未找到订单，订单号: {}", orderNo);
        }
        log.debug("完成订单查询，订单号: {}, 结果: {}", orderNo, order != null ? "成功" : "失败");
        return order;
    }

    @Override
    public Order getOrderById(Integer id) {
        return orderMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOrderStatus(String orderNo, String status) {
        Order order = orderMapper.selectByOrderNo(orderNo);
        if (order == null) {
            throw new OrderOperationException("订单不存在");
        }

        int result = orderMapper.updateStatusByOrderNo(orderNo, status);
        if (result > 0) {
            // 发送通知
            String message = OrderStatus.getStatusDesc(status);
            if (message.isEmpty()) {
                message = "订单状态已更新为" + OrderStatus.getStatusText(status);
            }

            // 发送通知给租客
            notificationService.sendNotification(
                    String.valueOf(order.getTenantId()),
                    "订单状态更新",
                    "您的订单 " + orderNo + " " + message,
                    "info",
                    orderNo,
                    "order"
            );

            // 发送通知给房东
            notificationService.sendNotification(
                    String.valueOf(order.getOwnerId()),
                    "订单状态更新",
                    "订单 " + orderNo + " " + message,
                    "info",
                    orderNo,
                    "order"
            );

            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelOrder(String orderNo) {
        Order order = orderMapper.selectByOrderNo(orderNo);
        if (order == null) {
            throw new OrderOperationException("订单不存在");
        }

        // 检查订单状态是否可以取消
        if (!OrderStatus.canCancel(order.getStatus())) {
            throw new OrderOperationException("当前状态的订单不能取消");
        }

        int result = orderMapper.updateStatusByOrderNo(orderNo, OrderStatus.CANCELLED);
        if (result > 0) {
            // 发送通知给租客
            notificationService.sendNotification(
                    String.valueOf(order.getTenantId()),
                    "订单已取消",
                    "您的订单 " + orderNo + " 已成功取消。",
                    "warning",
                    orderNo,
                    "order"
            );

            // 发送通知给房东
            notificationService.sendNotification(
                    String.valueOf(order.getOwnerId()),
                    "订单已取消",
                    "订单 " + orderNo + " 已被取消。",
                    "warning",
                    orderNo,
                    "order"
            );

            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean payDeposit(String orderNo, String paymentMethod) {
        Order order = orderMapper.selectByOrderNo(orderNo);
        if (order == null) {
            throw new OrderOperationException("订单不存在");
        }

        // 检查订单状态 - 必须在合同签署后才能支付押金
        if (!OrderStatus.UNPAID.equals(order.getStatus())) {
            throw new OrderOperationException("订单状态不正确，请先签署合同后再支付押金");
        }

        // 检查是否已签署合同
        if (order.getContractSignTime() == null) {
            throw new OrderOperationException("合同尚未签署，无法支付押金");
        }

        // 创建支付记录 - 首次支付包含押金、首月租金、服务费
        // 1. 押金支付记录
        paymentService.createDepositPayment(order.getId(), order.getTenantId(), order.getDeposit(), paymentMethod);

        // 2. 首月租金支付记录
        paymentService.createRentPayment(order.getId(), order.getTenantId(), order.getMonthlyPrice(), paymentMethod);

        // 3. 服务费支付记录
        paymentService.createServiceFeePayment(order.getId(), order.getTenantId(), order.getServiceFee(), paymentMethod);

        // 模拟支付成功，批量更新订单相关的所有支付记录状态
        String transactionId = "TXN" + System.currentTimeMillis();
        paymentService.updateOrderPaymentStatus(order.getId(), "success", transactionId);
        
        // 更新订单状态和押金支付时间
        Date now = new Date();
        int result = orderMapper.updateDepositPayTime(orderNo, now);

        if (result > 0) {
            // 发送通知给租客
            notificationService.sendNotification(
                    String.valueOf(order.getTenantId()),
                    "押金支付成功",
                    "您的订单 " + orderNo + " 押金已支付成功，请签署合同。",
                    "success",
                    orderNo,
                    "order"
            );

            // 发送通知给房东
            notificationService.sendNotification(
                    String.valueOf(order.getOwnerId()),
                    "押金支付通知",
                    "订单 " + orderNo + " 的租客已支付押金，请签署合同。",
                    "info",
                    orderNo,
                    "order"
            );

            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean payRent(String orderNo, String paymentMethod) {
        Order order = orderMapper.selectByOrderNo(orderNo);
        if (order == null) {
            throw new OrderOperationException("订单不存在");
        }

        // 检查订单状态
        if (!OrderStatus.RENTING.equals(order.getStatus())) {
            throw new OrderOperationException("订单状态不正确，无法支付租金");
        }

        // 创建支付记录
        paymentService.createRentPayment(order.getId(), order.getTenantId(), order.getMonthlyPrice(), paymentMethod);

        // 发送通知给租客
        notificationService.sendNotification(
                String.valueOf(order.getTenantId()),
                "租金支付成功",
                "您的订单 " + orderNo + " 本月租金已支付成功。",
                "success",
                orderNo,
                "order"
        );

        // 发送通知给房东
        notificationService.sendNotification(
                String.valueOf(order.getOwnerId()),
                "租金支付通知",
                "订单 " + orderNo + " 的租客已支付本月租金。",
                "info",
                orderNo,
                "order"
        );

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean confirmCheckIn(String orderNo) {
        Order order = orderMapper.selectByOrderNo(orderNo);
        if (order == null) {
            throw new OrderOperationException("订单不存在");
        }

        // 检查订单状态
        if (!OrderStatus.PENDING.equals(order.getStatus())) {
            throw new OrderOperationException("订单状态不正确，无法确认入住");
        }

        // 检查是否已签署合同
        if (order.getContractSignTime() == null) {
            throw new OrderOperationException("合同尚未签署，无法确认入住");
        }

        // 更新入住时间和状态
        Date now = new Date();
        int result = orderMapper.updateCheckInTime(orderNo, now);

        if (result > 0) {
            // 发送通知给租客
            notificationService.sendNotification(
                    String.valueOf(order.getTenantId()),
                    "确认入住成功",
                    "您的订单 " + orderNo + " 已确认入住，祝您入住愉快！",
                    "success",
                    orderNo,
                    "order"
            );

            // 发送通知给房东
            notificationService.sendNotification(
                    String.valueOf(order.getOwnerId()),
                    "租客已入住",
                    "订单 " + orderNo + " 的租客已确认入住。",
                    "info",
                    orderNo,
                    "order"
            );

            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean completeOrder(String orderNo) {
        Order order = orderMapper.selectByOrderNo(orderNo);
        if (order == null) {
            throw new OrderOperationException("订单不存在");
        }

        // 检查订单状态
        if (!OrderStatus.RENTING.equals(order.getStatus())) {
            throw new OrderOperationException("订单状态不正确，无法完成订单");
        }

        // 更新订单状态
        int result = orderMapper.updateStatusByOrderNo(orderNo, OrderStatus.COMPLETED);

        if (result > 0) {
            // 发送通知给租客
            notificationService.sendNotification(
                    String.valueOf(order.getTenantId()),
                    "租约已完成",
                    "您的订单 " + orderNo + " 租约已结束，感谢您的使用！",
                    "success",
                    orderNo,
                    "order"
            );

            // 发送通知给房东
            notificationService.sendNotification(
                    String.valueOf(order.getOwnerId()),
                    "租约已完成",
                    "订单 " + orderNo + " 的租约已结束。",
                    "info",
                    orderNo,
                    "order"
            );

            return true;
        }
        return false;
    }

    @Override
    public PageInfo<Order> getOrderList(Map<String, Object> params) {
        // 获取分页参数
        Integer pageNum = params.get("page") == null ? 1 : Integer.parseInt(params.get("page").toString());
        Integer pageSize = params.get("limit") == null ? 10 : Integer.parseInt(params.get("limit").toString());

        // 开始分页
        PageHelper.startPage(pageNum, pageSize);
        // 条件查询
        List<Order> list = orderMapper.selectByCondition(params);
        
        // 为每个订单添加房源信息和申请人信息
        for (Order order : list) {
            try {
                // 添加房源信息
                if (order.getHouseId() != null) {
                    HouseList house = houseListDao.selectHouseById(order.getHouseId());
                    if (house != null) {
                        order.setHouseAddress(house.getAddress());
                        // HouseList没有area字段，直接从地址中提取区域信息
                        String address = house.getAddress();
                        if (address != null && address.length() > 0) {
                            // 尝试从地址中提取区域信息，假设格式为"xx区xxx"
                            int idx = address.indexOf("区");
                            if (idx > 0 && idx < address.length()) {
                                order.setHouseArea(address.substring(0, idx + 1));
                            } else {
                                order.setHouseArea("未知区域");
                            }
                        } else {
                            order.setHouseArea("未知区域");
                        }
                    }
                }
                
                // 添加申请人信息
                if (order.getTenantId() != null) {
                    UserList tenant = userListDao.findUserListById(order.getTenantId());
                    if (tenant != null) {
                        order.setTenantName(tenant.getName());
                        order.setTenantPhone(tenant.getPhone());
                    }
                }
            } catch (Exception e) {
                // 记录异常但不影响整体流程
                System.err.println("获取订单关联信息失败: " + e.getMessage());
            }
        }
        
        // 返回分页结果
        return new PageInfo<>(list);
    }

    @Override
    public Map<String, Object> getOrderStatsByUserId(Integer userId) {
        Map<String, Object> result = new HashMap<>();
        // 查询各状态订单数量
        List<Map<String, Object>> statusStats = orderMapper.countOrdersByUserIdGroupByStatus(userId);
        
        // 初始化各状态数量为0
        result.put("unpaid", 0);
        result.put("pending", 0);
        result.put("renting", 0);
        result.put("completed", 0);
        result.put("cancelled", 0);
        
        // 填充实际数量
        for (Map<String, Object> stat : statusStats) {
            String status = (String) stat.get("status");
            Long count = (Long) stat.get("count");
            result.put(status, count);
        }
        
        return result;
    }
} 