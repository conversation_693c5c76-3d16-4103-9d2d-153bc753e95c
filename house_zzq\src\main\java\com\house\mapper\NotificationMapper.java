package com.house.mapper;

import com.house.pojo.Notification;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 消息通知数据访问接口
 */
@Mapper
@Repository
public interface NotificationMapper {

    /**
     * 插入消息通知
     */
    @Insert("INSERT INTO tb_notification(user_id, title, content, type, link_id, link_type, `read`, create_time, update_time) " +
            "VALUES(#{userId}, #{title}, #{content}, #{type}, #{linkId}, #{linkType}, #{read}, #{createTime}, #{updateTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Notification notification);

    /**
     * 根据ID查询消息通知
     */
    @Select("SELECT * FROM tb_notification WHERE id = #{id}")
    Notification selectById(Integer id);

    /**
     * 更新消息通知
     */
    @Update("UPDATE tb_notification SET title = #{title}, content = #{content}, type = #{type}, " +
            "link_id = #{linkId}, link_type = #{linkType}, `read` = #{read}, read_time = #{readTime}, " +
            "update_time = #{updateTime} WHERE id = #{id}")
    int update(Notification notification);

    /**
     * 删除消息通知
     */
    @Delete("DELETE FROM tb_notification WHERE id = #{id}")
    int deleteById(Integer id);

    /**
     * 标记消息为已读
     */
    @Update("UPDATE tb_notification SET `read` = 1, read_time = #{readTime} WHERE id = #{id}")
    int markAsRead(@Param("id") Integer id, @Param("readTime") Date readTime);

    /**
     * 标记用户的所有消息为已读
     */
    @Update("UPDATE tb_notification SET `read` = 1, read_time = #{readTime} WHERE user_id = #{userId} AND `read` = 0")
    int markAllAsRead(@Param("userId") Integer userId, @Param("readTime") Date readTime);

    /**
     * 查询用户的消息列表
     */
    @Select("SELECT * FROM tb_notification WHERE user_id = #{userId} ORDER BY create_time DESC")
    List<Notification> selectByUserId(Integer userId);

    /**
     * 查询用户的未读消息列表
     */
    @Select("SELECT * FROM tb_notification WHERE user_id = #{userId} AND `read` = 0 ORDER BY create_time DESC")
    List<Notification> selectUnreadByUserId(Integer userId);

    /**
     * 查询用户的未读消息数量
     */
    @Select("SELECT COUNT(*) FROM tb_notification WHERE user_id = #{userId} AND `read` = 0")
    int countUnreadByUserId(Integer userId);

    /**
     * 条件查询消息列表
     */
    List<Notification> selectByCondition(Map<String, Object> params);

    /**
     * 删除用户的所有消息
     */
    @Delete("DELETE FROM tb_notification WHERE user_id = #{userId}")
    int deleteByUserId(Integer userId);
} 