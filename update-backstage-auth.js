const fs = require('fs');
const path = require('path');

// 后台页面目录
const backstageDir = path.join(__dirname, 'house-client/src/backstage');

// 需要替换的文件后缀
const fileExtensions = ['.vue', '.js'];

// 替换函数
function replaceInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    
    // 替换import语句
    let updated = content.replace(
      /import\s+\{\s*getUser\s*\}/g, 
      'import { getBackstageUser }'
    );
    
    updated = updated.replace(
      /import\s+\{\s*getUser,/g, 
      'import { getBackstageUser,'
    );
    
    // 替换函数调用
    updated = updated.replace(/getUser\(\)/g, 'getBackstageUser()');
    
    // 如果内容有变化，写回文件
    if (content !== updated) {
      fs.writeFileSync(filePath, updated, 'utf8');
      console.log(`Updated: ${filePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error processing file ${filePath}:`, error);
    return false;
  }
}

// 处理目录中的所有文件
function processDirectory(directory) {
  const files = fs.readdirSync(directory);
  let updatedCount = 0;
  
  files.forEach(file => {
    const filePath = path.join(directory, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // 递归处理子目录
      updatedCount += processDirectory(filePath);
    } else if (fileExtensions.includes(path.extname(filePath))) {
      // 处理匹配的文件
      if (replaceInFile(filePath)) {
        updatedCount++;
      }
    }
  });
  
  return updatedCount;
}

// 开始处理
console.log('Starting to update backstage auth methods...');
const updatedCount = processDirectory(backstageDir);
console.log(`Completed! Updated ${updatedCount} files.`); 