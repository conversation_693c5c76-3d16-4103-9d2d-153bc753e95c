package com.house.interceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.util.ContentCachingRequestWrapper;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 请求响应日志拦截器
 * 以美观的格式记录请求和响应的详细信息
 */
@Slf4j
@Component
public class RequestResponseLoggingInterceptor implements HandlerInterceptor {

    // 需要跳过详细日志的URL路径
    private static final String[] SKIP_URLS = {
        "/images/",
        "/file/upload",
        "/file/download"
    };
    
    // 不记录响应体的内容类型
    private static final String[] BINARY_CONTENT_TYPES = {
        "image/",
        "audio/",
        "video/",
        "application/octet-stream",
        "multipart/form-data"
    };

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        if (request instanceof ContentCachingRequestWrapper && response instanceof ContentCachingResponseWrapper) {
            // 检查是否需要跳过详细日志
            String requestURI = request.getRequestURI();
            if (shouldSkipDetailedLogging(requestURI)) {
                // 简化的日志输出
                log.info("请求路径: {} {}, 状态: {}", request.getMethod(), requestURI, response.getStatus());
                return;
            }
            
            logRequestAndResponse((ContentCachingRequestWrapper) request, (ContentCachingResponseWrapper) response);
        }
    }

    private boolean shouldSkipDetailedLogging(String requestURI) {
        for (String skipUrl : SKIP_URLS) {
            if (requestURI.contains(skipUrl)) {
                return true;
            }
        }
        return false;
    }
    
    private boolean isBinaryContent(String contentType) {
        if (contentType == null) {
            return false;
        }
        
        for (String binaryType : BINARY_CONTENT_TYPES) {
            if (contentType.startsWith(binaryType)) {
                return true;
            }
        }
        return false;
    }

    private void logRequestAndResponse(ContentCachingRequestWrapper request, ContentCachingResponseWrapper response) {
        String requestMethod = request.getMethod();
        String requestURI = request.getRequestURI();
        int status = response.getStatus();
        
        // 获取内容类型
        String contentType = response.getContentType();
        
        // 对于二进制响应，简化日志输出
        if (isBinaryContent(contentType)) {
            log.info("请求路径: {} {}, 状态: {}, 内容类型: {} (二进制内容，不显示详情)", 
                    requestMethod, requestURI, status, contentType);
            return;
        }
        
        // 对于普通响应，继续使用详细日志
        String requestBody = getRequestBody(request);
        String responseBody = getResponseBody(response);

        StringBuilder logMessage = new StringBuilder();
        logMessage.append("\n请求路径: ").append(requestMethod).append(" ").append(requestURI).append("\n");
        
        // 限制请求体和响应体的长度，防止日志过长
        String shortenedRequestBody = (requestBody.length() > 1000) 
                ? requestBody.substring(0, 1000) + "... (截断，总长度: " + requestBody.length() + ")" 
                : requestBody;
                
        String shortenedResponseBody = (responseBody.length() > 1000) 
                ? responseBody.substring(0, 1000) + "... (截断，总长度: " + responseBody.length() + ")" 
                : responseBody;
        
        // 记录请求信息
        if (!requestBody.isEmpty()) {
            logMessage.append("请求体: ").append(shortenedRequestBody).append("\n");
        }
        
        // 记录响应信息
        logMessage.append("响应状态: ").append(status).append("\n");
        
        if (!responseBody.isEmpty()) {
            logMessage.append("响应体: ").append(shortenedResponseBody);
        }

        log.info(logMessage.toString());
    }

    private String getRequestBody(ContentCachingRequestWrapper request) {
        byte[] buf = request.getContentAsByteArray();
        if (buf.length > 0) {
            return new String(buf, 0, buf.length, StandardCharsets.UTF_8);
        }
        return "";
    }

    private String getResponseBody(ContentCachingResponseWrapper response) {
        byte[] buf = response.getContentAsByteArray();
        if (buf.length > 0) {
            try {
                return new String(buf, 0, buf.length, StandardCharsets.UTF_8);
            } catch (Exception e) {
                return "[无法读取响应体]";
            }
        }
        return "";
    }
} 