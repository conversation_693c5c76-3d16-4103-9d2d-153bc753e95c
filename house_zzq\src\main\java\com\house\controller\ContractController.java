package com.house.controller;

import com.github.pagehelper.PageInfo;
import com.house.common.Result;
import com.house.common.StatusCode;
import com.house.pojo.Contract;
import com.house.pojo.Order;
import com.house.service.ContractService;
import com.house.service.OrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import com.house.dto.ContractDTO;
import java.util.Date;

/**
 * 合同控制器
 */
@RestController
@CrossOrigin(origins = "http://localhost:8080", allowCredentials = "true", maxAge = 3600)
@RequestMapping("/contracts")
public class ContractController {

    @Autowired
    private ContractService contractService;

    @Autowired
    private OrderService orderService;

    /**
     * 创建合同
     */
    @PostMapping
    public Result createContract(@RequestBody Map<String, Object> contractData) {
        try {
            // 从请求中获取数据
            String orderNo = (String) contractData.get("orderNo");
            if (orderNo == null) {
                return new Result(false, StatusCode.ERROR, "订单号不能为空");
            }

            // 获取订单信息
            Order order = orderService.getOrderByOrderNo(orderNo);
            if (order == null) {
                return new Result(false, StatusCode.ERROR, "订单不存在");
            }

            // 创建合同对象
            Contract contract = new Contract();
            contract.setOrderId(order.getId());
            contract.setTenantId(order.getTenantId());
            contract.setOwnerId(order.getOwnerId());

            // 创建合同内容（可以根据需要自定义）
            StringBuilder contentBuilder = new StringBuilder();
            contentBuilder.append("房屋租赁合同\n\n");
            contentBuilder.append("甲方（房东）：").append(order.getOwnerId()).append("\n");
            contentBuilder.append("乙方（租客）：").append(order.getTenantId()).append("\n");
            contentBuilder.append("租赁房屋地址：").append(contractData.get("address") != null ? contractData.get("address") : "").append("\n");
            contentBuilder.append("租赁期限：").append(order.getDuration()).append("个月").append("\n");
            contentBuilder.append("开始日期：").append(contractData.get("startDate") != null ? contractData.get("startDate") : order.getStartDate()).append("\n");
            contentBuilder.append("结束日期：").append(contractData.get("endDate") != null ? contractData.get("endDate") : order.getEndDate()).append("\n");
            contentBuilder.append("月租金：￥").append(order.getMonthlyPrice()).append("\n");
            contentBuilder.append("押金：￥").append(order.getDeposit()).append("\n");
            contentBuilder.append("服务费：￥").append(order.getServiceFee()).append("\n");
            contentBuilder.append("支付方式：").append(contractData.get("paymentMethod") != null ? contractData.get("paymentMethod") : "月付").append("\n");
            contentBuilder.append("其他条款：").append(contractData.get("otherTerms") != null ? contractData.get("otherTerms") : "无").append("\n");

            contract.setContent(contentBuilder.toString());
            
            // 设置初始状态为待签署
            contract.setStatus("pending");
            contract.setTenantSigned(false);
            contract.setOwnerSigned(false);

            // 创建合同
            Contract createdContract = contractService.createContract(contract);
            return new Result(true, StatusCode.SUCCESS, "创建合同成功", createdContract);
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, StatusCode.ERROR, "创建合同失败：" + e.getMessage());
        }
    }

    /**
     * 分页获取合同列表
     */
    @GetMapping
    public Result getContractList(@RequestParam Map<String, Object> params) {
        try {
            PageInfo<ContractDTO> pageInfo = contractService.getContractDTOList(params);
            Map<String, Object> result = new HashMap<>();
            result.put("list", pageInfo.getList());
            result.put("total", pageInfo.getTotal());
            result.put("pages", pageInfo.getPages());
            result.put("pageNum", pageInfo.getPageNum());
            result.put("pageSize", pageInfo.getPageSize());
            return new Result(true, StatusCode.SUCCESS, "获取合同列表成功", result);
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, StatusCode.ERROR, "获取合同列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据订单号获取合同详情
     */
    @GetMapping("/order/{orderNo}")
    public Result getContractByOrderNo(@PathVariable String orderNo) {
        try {
            // 先获取订单
            Order order = orderService.getOrderByOrderNo(orderNo);
            if (order == null) {
                return new Result(false, StatusCode.ERROR, "订单不存在");
            }

            // 根据订单ID获取合同详情
            ContractDTO contractDTO = contractService.getContractDTOByOrderId(order.getId());
            if (contractDTO != null) {
                return new Result(true, StatusCode.SUCCESS, "获取合同成功", contractDTO);
            } else {
                return new Result(false, StatusCode.ERROR, "合同不存在");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, StatusCode.ERROR, "获取合同失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据合同编号获取合同详情
     */
    @GetMapping("/{contractNo}")
    public Result getContractByContractNo(@PathVariable String contractNo) {
        try {
            ContractDTO contractDTO = contractService.getContractDTOByContractNo(contractNo);
            if (contractDTO != null) {
                return new Result(true, StatusCode.SUCCESS, "获取合同成功", contractDTO);
            } else {
                return new Result(false, StatusCode.ERROR, "合同不存在");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, StatusCode.ERROR, "获取合同失败: " + e.getMessage());
        }
    }

    /**
     * 签署合同
     */
    @PutMapping("/{contractNo}/sign")
    public Result signContract(@PathVariable String contractNo, @RequestBody Map<String, Object> requestBody) {
        try {
            Contract contract = contractService.getContractByContractNo(contractNo);
            if (contract == null) {
                return new Result(false, StatusCode.ERROR, "合同不存在");
            }

            // 判断是房东还是租客签署
            boolean isOwnerSigning = requestBody.get("ownerSignTime") != null;
            boolean result;

            if (isOwnerSigning) {
                // 房东签署
                Integer ownerId = contract.getOwnerId();
                // 保存签名信息
                String ownerSignature = (String) requestBody.get("ownerSignature");
                result = contractService.signContractByOwner(contract.getId(), ownerId, ownerSignature);
            } else {
                // 租客签署
                Integer tenantId = contract.getTenantId();
                // 保存签名信息
                String tenantSignature = (String) requestBody.get("tenantSignature");
                result = contractService.signContractByTenant(contract.getId(), tenantId, tenantSignature);
            }

            if (result) {
                // 获取更新后的合同信息
                ContractDTO contractDTO = contractService.getContractDTOById(contract.getId());
                return new Result(true, StatusCode.SUCCESS, "签署合同成功", contractDTO);
            } else {
                return new Result(false, StatusCode.ERROR, "签署合同失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, StatusCode.ERROR, "签署合同失败: " + e.getMessage());
        }
    }

    /**
     * 更新合同状态
     */
    @PutMapping("/{contractNo}")
    public Result updateContractStatus(@PathVariable String contractNo, @RequestBody Map<String, Object> requestBody) {
        try {
            String status = (String) requestBody.get("status");
            if (status == null) {
                return new Result(false, StatusCode.ERROR, "状态不能为空");
            }

            Contract contract = contractService.getContractByContractNo(contractNo);
            if (contract == null) {
                return new Result(false, StatusCode.ERROR, "合同不存在");
            }

            boolean result = contractService.updateContractStatus(contract.getId(), status);
            if (result) {
                contract = contractService.getContractById(contract.getId());
                return new Result(true, StatusCode.SUCCESS, "更新合同状态成功", contract);
            } else {
                return new Result(false, StatusCode.ERROR, "更新合同状态失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, StatusCode.ERROR, "更新合同状态失败");
        }
    }

    /**
     * 取消合同
     */
    @PutMapping("/{contractNo}/cancel")
    public Result cancelContract(@PathVariable String contractNo) {
        try {
            Contract contract = contractService.getContractByContractNo(contractNo);
            if (contract == null) {
                return new Result(false, StatusCode.ERROR, "合同不存在");
            }

            boolean result = contractService.cancelContract(contract.getId());
            if (result) {
                return new Result(true, StatusCode.SUCCESS, "取消合同成功");
            } else {
                return new Result(false, StatusCode.ERROR, "取消合同失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new Result(false, StatusCode.ERROR, "取消合同失败");
        }
    }
} 