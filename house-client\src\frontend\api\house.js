import request from '../../utils/request'

const api_name = 'house'

/**
 * 获取房源详情
 * @param {Number} houseId 房源ID
 */
export function getHouseDetail(houseId) {
  return request({
    url: `/houses/${houseId}`,
    method: 'get'
  })
}

/**
 * 增加房源浏览量
 * @param {Number} houseId 房源ID
 */
export function increaseViewCount(houseId) {
  return request({
    url: `/${api_name}/increaseviewcount/${houseId}`,
    method: 'put'
  })
}

/**
 * 获取热门房源
 * @param {Number} limit 获取数量
 */
export function getHotHouses(limit = 4) {
  return request({
    url: `/${api_name}/gethothouses`,
    method: 'get',
    params: { limit }
  })
}

/**
 * 获取推荐房源
 * @param {Number} limit 获取数量
 */
export function getRecommendHouses(limit = 4) {
  return request({
    url: `/${api_name}/getrecommendhouses`,
    method: 'get',
    params: { limit }
  })
}

/**
 * 根据条件搜索房源
 * @param {Object} params 搜索条件
 */
export function searchHouses(params) {
  return request({
    url: `/${api_name}/gethouselistbycondition`,
    method: 'post',
    data: params
  })
}

/**
 * 获取用户发布的房源
 * @param {Number} userId 用户ID
 */
export function getUserHouses(userId) {
  return request({
    url: `/${api_name}/gethouselistbyuserid`,
    method: 'get',
    params: { userId }
  })
}

/**
 * 获取用户信息
 * @param {Number} userId 用户ID
 */
export function getUserInfo(userId) {
  return request({
    url: `/user/getuserinfo/${userId}`,
    method: 'get'
  })
}

export default {
  getHouseDetail,
  increaseViewCount,
  getHotHouses,
  getRecommendHouses,
  searchHouses,
  getUserHouses,
  getUserInfo
} 