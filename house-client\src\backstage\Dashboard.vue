<template>
  <div class="dashboard-container">
    <!-- 顶部统计卡片 -->
    <div class="stat-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="card-icon card-icon-cyan">
              <i class="el-icon-house"></i>
            </div>
            <div class="card-content">
              <div class="card-title">房源总数</div>
              <div class="card-value">{{ stats.houseCount || 0 }}</div>
              <div class="card-footer">
                <span class="trend">{{ stats.newHouseCount || 0 }} 新增</span>
                <span class="period">本月</span>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="card-icon card-icon-green">
              <i class="el-icon-s-order"></i>
            </div>
            <div class="card-content">
              <div class="card-title">订单总数</div>
              <div class="card-value">{{ stats.orderCount || 0 }}</div>
              <div class="card-footer">
                <span class="trend">{{ stats.newOrderCount || 0 }} 新增</span>
                <span class="period">本月</span>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="card-icon card-icon-orange">
              <i class="el-icon-user"></i>
            </div>
            <div class="card-content">
              <div class="card-title">用户总数</div>
              <div class="card-value">{{ stats.userCount || 0 }}</div>
              <div class="card-footer">
                <span class="trend">{{ stats.newUserCount || 0 }} 新增</span>
                <span class="period">本月</span>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover" class="stat-card">
            <div class="card-icon card-icon-blue">
              <i class="el-icon-wallet"></i>
            </div>
            <div class="card-content">
              <div class="card-title">收入总额</div>
              <div class="card-value">¥{{ formatNumber(stats.totalIncome || 0) }}</div>
              <div class="card-footer">
                <span class="trend">¥{{ formatNumber(stats.monthIncome || 0) }}</span>
                <span class="period">本月</span>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="chart-container">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <div slot="header" class="clearfix">
              <span>订单状态分布</span>
            </div>
            <div class="chart" id="order-status-chart"></div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover" class="chart-card">
            <div slot="header" class="clearfix">
              <span>近6个月收入统计</span>
            </div>
            <div class="chart" id="income-chart"></div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 待处理事项 -->
    <el-card shadow="hover" class="todo-card">
      <div slot="header" class="clearfix">
        <span>待处理事项</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="fetchTodoList">
          <i class="el-icon-refresh"></i> 刷新
        </el-button>
      </div>
      <el-table
        v-loading="loading"
        :data="todoList"
        style="width: 100%"
        :empty-text="isOwner ? '暂无待处理的租房申请' : '暂无待处理事项'"
      >
        <el-table-column prop="type" label="类型" width="120">
          <template slot-scope="scope">
            <el-tag :type="getTypeTag(scope.row.type)">{{ getTypeText(scope.row.type) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="标题" min-width="200"></el-table-column>
        <el-table-column prop="content" label="内容" min-width="300">
          <template slot-scope="scope">
            <el-tooltip :content="scope.row.content" placement="top" :disabled="scope.row.content.length < 30">
              <span>{{ truncateText(scope.row.content, 30) }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160">
          <template slot-scope="scope">{{ formatDateTime(scope.row.createTime) }}</template>
        </el-table-column>
        <el-table-column label="操作" width="120">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleTodoAction(scope.row)"
            >处理</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 最新消息 -->
    <el-card shadow="hover" class="message-card">
      <div slot="header" class="clearfix">
        <span>最新消息</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="fetchMessages">
          <i class="el-icon-refresh"></i> 刷新
        </el-button>
      </div>
      <div v-if="messages.length === 0" class="empty-message">
        暂无新消息
      </div>
      <div v-else class="message-list">
        <div
          v-for="(message, index) in messages"
          :key="message.id"
          class="message-item"
          :class="{'unread': !message.read}"
        >
          <div class="message-icon">
            <i :class="getMessageIcon(message.type)"></i>
          </div>
          <div class="message-content">
            <div class="message-title">{{ message.title }}</div>
            <div class="message-text">{{ message.content }}</div>
            <div class="message-time">{{ formatDateTime(message.createTime) }}</div>
          </div>
          <div class="message-action">
            <el-button type="text" @click="handleMessageView(message)">查看</el-button>
            <el-button type="text" v-if="!message.read" @click="handleMessageRead(message)">标为已读</el-button>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getBackstageUser, isAdmin, isOwner, isTenant } from '../utils/auth'
import * as echarts from 'echarts'
import moment from 'moment'

export default {
  name: 'Dashboard',
  data() {
    return {
      loading: false,
      stats: {
        houseCount: 0,
        newHouseCount: 0,
        orderCount: 0,
        newOrderCount: 0,
        userCount: 0,
        newUserCount: 0,
        totalIncome: 0,
        monthIncome: 0
      },
      orderStatusData: [],
      incomeData: [],
      todoList: [],
      messages: [],
      orderStatusChart: null,
      incomeChart: null,
      isOwner: false,
      isTenant: false,
      isAdmin: false,
      userId: null
    }
  },
  created() {
    this.isOwner = isOwner()
    this.isTenant = isTenant()
    this.isAdmin = isAdmin()
    
    const userInfo = getBackstageUser()
    if (userInfo && userInfo.userInfo) {
      this.userId = userInfo.userInfo.id
    }
    
    this.fetchDashboardData()
    this.fetchTodoList()
    this.fetchMessages()
  },
  mounted() {
    this.$nextTick(() => {
      // 初始化图表
      this.initCharts()
    })
  },
  methods: {
    // 获取仪表盘数据
    async fetchDashboardData() {
      try {
        // 构建请求参数
        const params = {}
        
        if (this.isOwner) {
          params.ownerId = this.userId
        } else if (this.isTenant) {
          params.tenantId = this.userId
        }
        
        const res = await this.$http.get('/dashboard/stats', { params })
        
        if (res.data && res.data.flag) {
          this.stats = res.data.data.stats || {}
          this.orderStatusData = res.data.data.orderStatusData || []
          this.incomeData = res.data.data.incomeData || []
          
          // 更新图表数据
          this.updateCharts()
        }
      } catch (error) {
        console.error('获取仪表盘数据失败:', error)
      }
    },
    
    // 获取待办事项
    async fetchTodoList() {
      this.loading = true
      try {
        // 构建请求参数
        const params = {
          limit: 5
        }
        
        if (this.isOwner) {
          params.ownerId = this.userId
          params.type = 'application' // 房东主要处理租房申请
        } else if (this.isTenant) {
          params.tenantId = this.userId
          params.types = ['payment', 'contract'] // 租客主要处理支付和合同
        }
        
        const res = await this.$http.get('/dashboard/todos', { params })
        
        if (res.data && res.data.flag) {
          this.todoList = res.data.data || []
        }
      } catch (error) {
        console.error('获取待办事项失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // 获取最新消息
    async fetchMessages() {
      try {
        const res = await this.$http.get('/notifications', {
          params: {
            userId: this.userId,
            limit: 5,
            read: false
          }
        })
        
        if (res.data && res.data.flag) {
          this.messages = res.data.data || []
        }
      } catch (error) {
        console.error('获取最新消息失败:', error)
      }
    },
    
    // 初始化图表
    initCharts() {
      // 初始化订单状态分布图表
      this.orderStatusChart = echarts.init(document.getElementById('order-status-chart'))
      
      // 初始化收入统计图表
      this.incomeChart = echarts.init(document.getElementById('income-chart'))
      
      // 更新图表数据
      this.updateCharts()
      
      // 监听窗口大小变化，调整图表大小
      window.addEventListener('resize', () => {
        this.orderStatusChart.resize()
        this.incomeChart.resize()
      })
    },
    
    // 更新图表数据
    updateCharts() {
      // 更新订单状态分布图表
      if (this.orderStatusChart) {
        const orderStatusOption = {
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b} : {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            left: 'left',
            data: this.orderStatusData.map(item => item.name)
          },
          series: [
            {
              name: '订单状态',
              type: 'pie',
              radius: '55%',
              center: ['50%', '60%'],
              data: this.orderStatusData,
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
              }
            }
          ]
        }
        
        this.orderStatusChart.setOption(orderStatusOption)
      }
      
      // 更新收入统计图表
      if (this.incomeChart) {
        // 处理数据
        const months = []
        const values = []
        
        this.incomeData.forEach(item => {
          months.push(item.month)
          values.push(item.income)
        })
        
        const incomeOption = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: months
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '收入',
              type: 'bar',
              data: values,
              itemStyle: {
                color: '#409EFF'
              }
            }
          ]
        }
        
        this.incomeChart.setOption(incomeOption)
      }
    },
    
    // 处理待办事项
    handleTodoAction(todo) {
      // 根据类型跳转到不同页面
      if (todo.type === 'application') {
        // 租房申请，跳转到申请管理页面
        this.$router.push('/backstage/orders/applications')
      } else if (todo.type === 'payment') {
        // 支付提醒，跳转到支付页面
        if (todo.orderNo) {
          this.$router.push(`/frontend/orderpay/${todo.orderNo}`)
        }
      } else if (todo.type === 'contract') {
        // 合同签署，跳转到合同管理页面
        this.$router.push('/backstage/contracts')
      } else {
        // 其他类型，跳转到订单管理页面
        this.$router.push('/backstage/orders')
      }
    },
    
    // 查看消息
    handleMessageView(message) {
      // 标记为已读
      this.handleMessageRead(message)
      
      // 根据消息类型跳转到不同页面
      if (message.link) {
        // 如果有链接，直接跳转
        this.$router.push(message.link)
      } else {
        // 否则跳转到消息中心
        this.$router.push('/backstage/messages')
      }
    },
    
    // 标记消息为已读
    async handleMessageRead(message) {
      try {
        const res = await this.$http.put(`/notifications/${message.id}/read`, {
          read: true
        })
        
        if (res.data && res.data.flag) {
          // 更新本地消息状态
          const index = this.messages.findIndex(item => item.id === message.id)
          if (index !== -1) {
            this.messages[index].read = true
            this.$set(this.messages, index, { ...this.messages[index] })
          }
        }
      } catch (error) {
        console.error('标记消息已读失败:', error)
      }
    },
    
    // 获取待办事项类型标签
    getTypeTag(type) {
      const typeMap = {
        'application': 'primary',
        'payment': 'success',
        'contract': 'warning',
        'order': 'info'
      }
      return typeMap[type] || 'info'
    },
    
    // 获取待办事项类型文本
    getTypeText(type) {
      const typeMap = {
        'application': '租房申请',
        'payment': '支付提醒',
        'contract': '合同签署',
        'order': '订单处理'
      }
      return typeMap[type] || type
    },
    
    // 获取消息图标
    getMessageIcon(type) {
      const iconMap = {
        'system': 'el-icon-s-platform',
        'order': 'el-icon-s-order',
        'payment': 'el-icon-wallet',
        'contract': 'el-icon-document',
        'application': 'el-icon-house'
      }
      return iconMap[type] || 'el-icon-message'
    },
    
    // 格式化数字
    formatNumber(num) {
      return num.toLocaleString('zh-CN')
    },
    
    // 格式化日期时间
    formatDateTime(dateStr) {
      return moment(dateStr).format('YYYY-MM-DD HH:mm')
    },
    
    // 文本截断
    truncateText(text, length) {
      if (!text) return ''
      return text.length > length ? text.substring(0, length) + '...' : text
    }
  }
}
</script>

<style scoped>
.dashboard-container {
  padding: 20px;
}

.stat-cards {
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  height: 120px;
  padding: 15px;
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.card-icon i {
  font-size: 30px;
  color: #fff;
}

.card-icon-cyan {
  background-color: #00C4C4;
}

.card-icon-green {
  background-color: #4CAF50;
}

.card-icon-orange {
  background-color: #FF9800;
}

.card-icon-blue {
  background-color: #1976D2;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 10px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
}

.card-footer {
  font-size: 12px;
}

.trend {
  color: #67C23A;
}

.period {
  color: #909399;
  margin-left: 10px;
}

.chart-container {
  margin-bottom: 20px;
}

.chart-card {
  margin-bottom: 20px;
}

.chart {
  height: 300px;
}

.todo-card,
.message-card {
  margin-bottom: 20px;
}

.empty-message {
  text-align: center;
  padding: 30px;
  color: #909399;
  font-size: 14px;
}

.message-list {
  max-height: 400px;
  overflow-y: auto;
}

.message-item {
  display: flex;
  padding: 15px;
  border-bottom: 1px solid #EBEEF5;
}

.message-item.unread {
  background-color: #F5F7FA;
}

.message-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #F2F6FC;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.message-icon i {
  font-size: 20px;
  color: #409EFF;
}

.message-content {
  flex: 1;
}

.message-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.message-text {
  color: #606266;
  margin-bottom: 5px;
}

.message-time {
  font-size: 12px;
  color: #909399;
}

.message-action {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
</style> 