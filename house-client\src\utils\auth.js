/**
 * 用户身份验证相关方法
 */

// 用户信息本地存储key
const FRONTEND_USER_KEY = 'house_frontend_user_info';
const BACKSTAGE_USER_KEY = 'house_backstage_user_info';

/**
 * 保存前台用户信息到本地存储
 * @param {Object} userInfo 用户信息对象，包含token和用户基本信息
 */
export function saveFrontendUser(userInfo) {
  // 确保token存在
  if (!userInfo || !userInfo.token) {
    console.error('保存前台用户信息失败: token不存在');
    return;
  }
  
  // 记录token存储时间
  userInfo.tokenTimestamp = Date.now();
  
  localStorage.setItem(FRONTEND_USER_KEY, JSON.stringify(userInfo));
  console.log('前台用户信息已保存到本地存储');
}

/**
 * 保存后台用户信息到本地存储
 * @param {Object} userInfo 用户信息对象，包含token和用户基本信息
 */
export function saveBackstageUser(userInfo) {
  // 确保token存在
  if (!userInfo || !userInfo.token) {
    console.error('保存后台用户信息失败: token不存在');
    return;
  }
  
  // 记录token存储时间
  userInfo.tokenTimestamp = Date.now();
  
  localStorage.setItem(BACKSTAGE_USER_KEY, JSON.stringify(userInfo));
  console.log('后台用户信息已保存到本地存储');
}

/**
 * 设置用户信息（兼容两种调用方式）
 * @param {Object|String} data 用户信息对象或系统角色
 * @param {String} isFrontend 是否为前台用户
 */
export function setUser(data, isFrontend = true) {
  if (typeof data === 'object') {
    // 新版方式：直接传入响应对象
    if (isFrontend) {
      saveFrontendUser(data);
    } else {
      saveBackstageUser(data);
    }
  } else if (arguments.length >= 4) {
    // 旧版方式：传入四个独立参数 (systemRole, name, id, token)
    const [systemRole, name, id, token] = arguments;
    
    // 确保token存在
    if (!token) {
      console.error('设置用户信息失败: token不存在');
      return;
    }
    
    const userInfo = {
      systemRole,
      token,
      tokenTimestamp: Date.now(), // 记录token存储时间
      userInfo: {
        id,
        name,
        // 根据systemRole设置type值
        type: systemRole === 'admin' ? 1 : 2
      }
    };
    
    // 第五个参数表示是否为前台用户
    const isF = arguments.length === 5 ? arguments[4] : true;
    if (isF) {
      saveFrontendUser(userInfo);
    } else {
      saveBackstageUser(userInfo);
    }
  } else {
    console.error('setUser参数错误');
  }
}

/**
 * 从本地存储获取前台用户信息
 * @returns {Object} 用户信息对象
 */
export function getFrontendUser() {
  try {
    const userStr = localStorage.getItem(FRONTEND_USER_KEY);
    if (userStr) {
      return JSON.parse(userStr);
    }
    return {};
  } catch (error) {
    console.error('解析前台用户信息失败', error);
    return {};
  }
}

/**
 * 从本地存储获取后台用户信息
 * @returns {Object} 用户信息对象
 */
export function getBackstageUser() {
  try {
    const userStr = localStorage.getItem(BACKSTAGE_USER_KEY);
    if (userStr) {
      return JSON.parse(userStr);
    }
    return {};
  } catch (error) {
    console.error('解析后台用户信息失败', error);
    return {};
  }
}

/**
 * 根据参数获取对应的用户信息
 * @param {Boolean} isFrontend 是否获取前台用户信息
 * @returns {Object} 用户信息对象
 */
export function getUser(isFrontend = true) {
  return isFrontend ? getFrontendUser() : getBackstageUser();
}

/**
 * 移除前台本地存储的用户信息（用于注销）
 */
export function removeFrontendUser() {
  localStorage.removeItem(FRONTEND_USER_KEY);
  console.log('前台用户信息已清除');
}

/**
 * 移除后台本地存储的用户信息（用于注销）
 */
export function removeBackstageUser() {
  localStorage.removeItem(BACKSTAGE_USER_KEY);
  console.log('后台用户信息已清除');
}

/**
 * 根据参数移除对应的用户信息
 * @param {Boolean} isBackstage 是否移除后台用户信息，默认为false（移除前台用户信息）
 */
export function removeUser(isBackstage = false) {
  if (isBackstage) {
    removeBackstageUser();
  } else {
    removeFrontendUser();
  }
}

/**
 * 检查用户是否已登录
 * @param {Boolean} isFrontend 是否检查前台用户
 * @returns {Boolean} 是否已登录
 */
export function isLoggedIn(isFrontend = true) {
  const userInfo = getUser(isFrontend);
  return userInfo && userInfo.token ? true : false;
}

/**
 * 获取用户令牌
 * @param {Boolean} isFrontend 是否获取前台用户令牌
 * @returns {String} 用户令牌
 */
export function getToken(isFrontend = true) {
  const userInfo = getUser(isFrontend);
  return userInfo.token || '';
}

/**
 * 检查用户是否为管理员
 * @param {Boolean} isFrontend 是否检查前台用户
 * @returns {Boolean} 是否为管理员
 */
export function isAdmin(isFrontend = true) {
  const userInfo = getUser(isFrontend);
  return userInfo && userInfo.systemRole === 'admin';
}

/**
 * 检查用户是否为房东
 * @param {Boolean} isFrontend 是否检查前台用户
 * @returns {Boolean} 是否为房东
 */
export function isOwner(isFrontend = true) {
  const userInfo = getUser(isFrontend);
  return userInfo && (userInfo.systemRole === 'owner' || (userInfo.userInfo && userInfo.userInfo.type === 3));
}

/**
 * 刷新token（更新token存储时间）
 * @param {Boolean} isFrontend 是否刷新前台用户token
 */
export function refreshToken(isFrontend = true) {
  const userInfo = getUser(isFrontend);
  if (userInfo && userInfo.token) {
    userInfo.tokenTimestamp = Date.now();
    if (isFrontend) {
      localStorage.setItem(FRONTEND_USER_KEY, JSON.stringify(userInfo));
    } else {
      localStorage.setItem(BACKSTAGE_USER_KEY, JSON.stringify(userInfo));
    }
  }
}