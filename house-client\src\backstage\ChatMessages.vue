<template>
  <div class="chat-messages-container">
    <div class="page-header">
      <h2>房源咨询消息</h2>
      <div class="header-actions">
        <el-button type="primary" icon="el-icon-refresh" @click="fetchMessages" :loading="loading">刷新</el-button>
      </div>
    </div>

    <div class="message-container">
      <div class="message-sidebar">
        <div class="search-box">
          <el-input
            placeholder="搜索用户或房源"
            v-model="searchKeyword"
            prefix-icon="el-icon-search"
            clearable
            @clear="handleSearchClear"
            @input="handleSearch">
          </el-input>
        </div>
        <div class="contact-list">
          <div 
            v-for="contact in filteredContacts" 
            :key="contact.id"
            :class="['contact-item', { 'active': currentContact && currentContact.id === contact.id }]"
            @click="selectContact(contact)">
            <div class="avatar">
              <div class="contact-avatar" :style="{ backgroundImage: `url(${getImageUrl(contact.avatar)})` }"></div>
              <span class="unread-badge" v-if="contact.unreadCount > 0">{{ contact.unreadCount }}</span>
            </div>
            <div class="contact-info">
              <div class="contact-name">{{ contact.userName }}</div>
              <div class="house-info">{{ contact.houseAddress }}</div>
              <div class="last-message">{{ contact.lastMessage }}</div>
            </div>
            <div class="contact-time">{{ formatTime(contact.lastTime) }}</div>
          </div>
          <div class="no-contacts" v-if="filteredContacts.length === 0">
            <el-empty description="暂无咨询消息"></el-empty>
          </div>
        </div>
      </div>

      <div class="message-content">
        <div v-if="currentContact" class="chat-container">
          <div class="chat-header">
            <div class="chat-title">
              <div class="user-name">{{ currentContact.userName }}</div>
              <div class="house-address">咨询房源: {{ currentContact.houseAddress }}</div>
            </div>
            <div class="chat-actions">
              <el-button size="mini" type="primary" @click="viewHouseDetail">查看房源</el-button>
              <el-button size="mini" icon="el-icon-delete" @click="clearHistory">清空记录</el-button>
            </div>
          </div>
          <div class="chat-messages" ref="messageList">
            <div v-for="(message, index) in currentMessages" :key="index" :class="['message-item', { 'self': message.fromUserId == landlordId }]">
              <div class="message-avatar">
                <div class="message-avatar-img" :style="{ backgroundImage: `url(${getMessageAvatar(message)})` }"></div>
              </div>
              <div class="message-bubble">
                <div class="message-time">{{ formatTime(message.timestamp || message.createTime) }}</div>
                <div class="message-content">{{ message.content }}</div>
              </div>
            </div>
            <div class="no-messages" v-if="currentMessages.length === 0">
              <el-empty description="暂无消息记录"></el-empty>
            </div>
          </div>
          <div class="chat-input">
            <el-input
              type="textarea"
              :rows="3"
              placeholder="请输入回复消息"
              v-model="messageText"
              @keyup.enter.native="sendMessage">
            </el-input>
            <div class="input-actions">
              <el-button type="primary" @click="sendMessage" :disabled="!messageText.trim()">发送</el-button>
            </div>
          </div>
        </div>
        <div v-else class="no-chat-selected">
          <el-empty description="请选择咨询消息开始回复"></el-empty>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getBackstageUser } from '../utils/auth'
import { getImageUrl } from '../utils/imageUtils'
import chatApi from './api/chat'

export default {
  name: 'ChatMessages',
  data() {
    return {
      contacts: [],
      currentContact: null,
      currentMessages: [],
      messageText: '',
      searchKeyword: '',
      defaultAvatar: require('../assets/houselogo.png'),
      landlordAvatar: require('../assets/houselogo.png'),
      landlordId: null,
      loading: false,
      onNewMessageHandler: null, // 用于存储WebSocket消息监听器
      socket: null // 用于存储WebSocket实例
    }
  },
  computed: {
    filteredContacts() {
      if (!this.searchKeyword) {
        return this.contacts
      }
      return this.contacts.filter(contact => 
        contact.userName.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
        contact.houseAddress.toLowerCase().includes(this.searchKeyword.toLowerCase())
      )
    },
    processedLandlordAvatar() {
      return this.landlordAvatar ? this.getImageUrl(this.landlordAvatar) : this.defaultAvatar;
    }
  },
  created() {
    const userInfo = getBackstageUser()
    if (userInfo && userInfo.userInfo) {
      this.landlordId = userInfo.userInfo.id
      this.landlordAvatar = userInfo.userInfo.avatar || this.defaultAvatar
      this.fetchMessages()
      
      // 添加WebSocket消息监听
      this.setupWebSocketListener()
      
      // 直接初始化WebSocket连接
      this.initWebSocket(userInfo.userInfo.id, userInfo.token)
    } else {
      this.$message.error('未获取到用户信息，请重新登录')
    }
  },
  beforeDestroy() {
    // 移除WebSocket消息监听
    this.removeWebSocketListener()
    
    // 关闭WebSocket连接
    this.closeWebSocket()
  },
  methods: {
    // 初始化WebSocket连接
    initWebSocket(userId, token) {
      if (!userId || !token) {
        console.error('无法初始化WebSocket：用户ID或token为空')
        return
      }
      
      try {
        // 检查是否已经有连接，如果有则关闭
        if (this.socket && this.socket.readyState !== WebSocket.CLOSED) {
          // 如果已经连接，不重复连接
          if (this.socket.readyState === WebSocket.OPEN) {
            console.log('ChatMessages: WebSocket已经连接，无需重新连接')
            return
          }
          // 关闭现有连接
          this.closeWebSocket()
        }
        
        // 使用直接的WebSocket连接URL
        const encodedToken = encodeURIComponent(token)
        const wsUrl = `ws://localhost:9002/ws/chat/${userId}?houseId=0&landlordId=${userId}&token=${encodedToken}`
        
        console.log('ChatMessages组件初始化WebSocket连接:', wsUrl)
        
        // 创建新连接
        this.socket = new WebSocket(wsUrl)
        
        // 连接成功回调
        this.socket.onopen = () => {
          console.log('ChatMessages: WebSocket连接已建立')
        }
        
        // 接收消息回调
        this.socket.onmessage = (event) => {
          console.log('ChatMessages: 直接收到WebSocket消息:', event.data)
          try {
            const message = JSON.parse(event.data)
            this.onNewMessage(message)
          } catch (error) {
            console.error('解析WebSocket消息失败:', error)
          }
        }
        
        // 连接关闭回调
        this.socket.onclose = () => {
          console.log('ChatMessages: WebSocket连接已关闭')
        }
        
        // 连接错误回调
        this.socket.onerror = (error) => {
          console.error('ChatMessages: WebSocket连接错误:', error)
        }
      } catch (error) {
        console.error('初始化WebSocket失败:', error)
      }
    },
    
    // 关闭WebSocket连接
    closeWebSocket() {
      if (this.socket) {
        try {
          this.socket.close()
        } catch (e) {
          console.error("关闭WebSocket连接时出错:", e)
        }
        this.socket = null
      }
    },
    
    // 设置WebSocket消息监听
    setupWebSocketListener() {
      // 监听全局WebSocket消息事件
      this.onNewMessageHandler = this.onNewMessage.bind(this);
      this.$root.$on('backstage-new-message', this.onNewMessageHandler);
      
      // 直接监听已存在的WebSocket连接
      if (this.$root.$data.backstageWebSocket) {
        console.log('找到现有的后台WebSocket连接，添加消息监听');
        
        // 添加直接消息监听
        this.$root.$data.backstageWebSocket.addEventListener('message', (event) => {
          try {
            const message = JSON.parse(event.data);
            console.log('ChatMessages组件直接收到WebSocket消息:', message);
            this.onNewMessage(message);
          } catch (error) {
            console.error('解析WebSocket消息失败:', error);
          }
        });
      } else {
        console.warn('未找到后台WebSocket连接，只能依赖全局事件');
      }
    },
    
    // 移除WebSocket消息监听
    removeWebSocketListener() {
      if (this.onNewMessageHandler) {
        this.$root.$off('backstage-new-message', this.onNewMessageHandler)
      }
    },
    
    // 处理新的WebSocket消息
    onNewMessage(message) {
      console.log('ChatMessages收到新消息:', message)
      
      // 只处理聊天类型的消息
      if (message.type === 'chat') {
        // 获取发送者和接收者ID
        const fromId = message.fromId
        const toId = message.toId
        
        // 如果当前用户是接收者，刷新消息列表
        if (toId == this.landlordId) {
          console.log('收到发给房东的新消息，刷新消息列表')
          
          // 直接重新获取所有消息，确保头像等信息正确显示
          this.fetchMessages()
          
          // 如果当前正在查看该联系人的消息，滚动到底部
          if (this.currentContact && 
              ((this.currentContact.userId == fromId) || 
               (this.currentContact.id === `${message.houseId}-${fromId}`))) {
            this.$nextTick(() => {
              this.scrollToBottom()
            })
          }
        }
      }
    },
    getImageUrl(url) {
      if (!url) return this.defaultAvatar;
      
      // 如果是完整URL（以http开头），直接返回
      if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
      }
      
      // 处理以/api/images/开头的路径 - 直接拼接主机名
      if (url.startsWith('/api/images/')) {
        return `http://localhost:9002${url}`;
      }
      
      // 如果是相对路径，拼接后端地址
      if (url.startsWith('/images/')) {
        // 修改为访问后端的/api/images路径
        return `http://localhost:9002/api/images${url.substring(8)}`;
      }
      
      // 如果只有文件名，拼接完整路径
      if (!url.includes('/')) {
        return `http://localhost:9002/api/images/${url}`;
      }
      
      // 其他情况，尝试作为相对路径处理
      return `http://localhost:9002${url}`;
    },
    formatTime(timestamp) {
      if (!timestamp) return ''
      
      const now = new Date()
      const date = new Date(timestamp)
      
      // 如果是今天
      if (now.toDateString() === date.toDateString()) {
        return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
      }
      
      // 如果是昨天
      const yesterday = new Date(now)
      yesterday.setDate(yesterday.getDate() - 1)
      if (yesterday.toDateString() === date.toDateString()) {
        return `昨天 ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
      }
      
      // 如果是今年
      if (now.getFullYear() === date.getFullYear()) {
        return `${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
      }
      
      // 其他情况
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    },
    fetchMessages() {
      if (!this.landlordId) {
        this.$message.error('未获取到房东ID')
        return
      }
      
      this.loading = true
      console.log('开始获取消息列表，房东ID:', this.landlordId)
      
      chatApi.getAllChatMessages(this.landlordId)
        .then(response => {
          if (response.data && response.data.code === 20000) {
            console.log('成功获取消息列表:', response.data.data)
            // 处理联系人数据
            const messagesData = response.data.data || []
            
            // 按房源和用户分组，每个房源-用户对作为一个联系人
            const contactsMap = new Map()
            
            messagesData.forEach(msg => {
              // 处理头像URL
              if (msg.fromUserAvatar) {
                msg.fromUserAvatar = this.getImageUrl(msg.fromUserAvatar)
              }
              if (msg.toUserAvatar) {
                msg.toUserAvatar = this.getImageUrl(msg.toUserAvatar)
              }
              
              const key = `${msg.houseId}-${msg.fromUserId === this.landlordId ? msg.toUserId : msg.fromUserId}`
              
              if (!contactsMap.has(key)) {
                contactsMap.set(key, {
                  id: key,
                  userId: msg.fromUserId === this.landlordId ? msg.toUserId : msg.fromUserId,
                  userName: msg.fromUserId === this.landlordId ? msg.toUserName : msg.fromUserName,
                  avatar: msg.fromUserId === this.landlordId ? 
                    (msg.toUserAvatar || this.defaultAvatar) : 
                    (msg.fromUserAvatar || this.defaultAvatar),
                  houseId: msg.houseId,
                  houseAddress: msg.houseAddress,
                  lastMessage: msg.content,
                  lastTime: msg.createTime,
                  unreadCount: msg.fromUserId !== this.landlordId && msg.readStatus === 0 ? 1 : 0,
                  messages: [msg]
                })
              } else {
                const contact = contactsMap.get(key)
                
                // 更新最后消息和时间
                if (new Date(msg.createTime) > new Date(contact.lastTime)) {
                  contact.lastMessage = msg.content
                  contact.lastTime = msg.createTime
                }
                
                // 更新未读计数
                if (msg.fromUserId !== this.landlordId && msg.readStatus === 0) {
                  contact.unreadCount++
                }
                
                // 添加消息到列表
                contact.messages.push(msg)
              }
            })
            
            // 将Map转为数组并按最后消息时间排序
            this.contacts = Array.from(contactsMap.values()).sort((a, b) => 
              new Date(b.lastTime) - new Date(a.lastTime)
            )
            
            // 如果当前有选中的联系人，更新其消息列表
            if (this.currentContact) {
              const updatedContact = this.contacts.find(c => c.id === this.currentContact.id)
              if (updatedContact) {
                this.currentContact = updatedContact
                this.currentMessages = [...updatedContact.messages].sort((a, b) => 
                  new Date(a.createTime) - new Date(b.createTime)
                )
                
                // 标记消息为已读
                if (updatedContact.unreadCount > 0) {
                  this.markMessagesAsRead()
                }
                
                // 滚动到底部
                this.$nextTick(() => {
                  this.scrollToBottom()
                })
              }
            } else if (this.contacts.length > 0) {
              // 如果没有未读消息但有联系人，选择第一个
              this.selectContact(this.contacts[0])
            }
          } else {
            this.$message.error(response.data.message || '获取消息列表失败')
          }
          this.loading = false
        })
        .catch(error => {
          console.error('获取消息列表失败:', error)
          this.$message.error('获取消息列表失败')
          this.loading = false
        })
    },
    selectContact(contact) {
      this.currentContact = contact
      
      // 如果联系人对象中已有消息，直接使用
      if (contact.messages && contact.messages.length > 0) {
        this.currentMessages = [...contact.messages].sort((a, b) => 
          new Date(a.createTime) - new Date(b.createTime)
        )
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      } else {
        // 否则从服务器获取聊天记录
        this.fetchChatHistory(contact)
      }
      
      // 标记消息为已读
      if (contact.unreadCount > 0) {
        chatApi.markMessagesAsRead(this.landlordId, contact.userId)
          .then(response => {
            if (response.data && response.data.code === 20000) {
              // 更新联系人列表中的未读数
              const index = this.contacts.findIndex(c => c.id === contact.id)
              if (index !== -1) {
                this.contacts[index].unreadCount = 0
              }
            }
          })
          .catch(error => {
            console.error('标记消息为已读失败:', error)
          })
      }
    },
    fetchChatHistory(contact) {
      this.loading = true
      chatApi.getChatHistory(contact.houseId, contact.userId, this.landlordId)
        .then(response => {
          if (response.data && response.data.code === 20000) {
            this.currentMessages = response.data.data || []
            this.$nextTick(() => {
              this.scrollToBottom()
            })
          } else {
            this.$message.error(response.data.message || '获取聊天记录失败')
          }
          this.loading = false
        })
        .catch(error => {
          console.error('获取聊天记录失败:', error)
          this.$message.error('获取聊天记录失败')
          this.loading = false
        })
    },
    sendMessage() {
      if (!this.messageText.trim() || !this.currentContact) {
        return
      }
      
      const message = {
        houseId: this.currentContact.houseId,
        fromUserId: this.landlordId,
        toUserId: this.currentContact.userId,
        content: this.messageText.trim(),
        messageType: 'text',
        readStatus: 0
      }
      
      // 先添加到本地消息列表
      const localMessage = {
        ...message,
        timestamp: Date.now()
      }
      this.currentMessages.push(localMessage)
      this.$nextTick(() => {
        this.scrollToBottom()
      })
      
      // 清空输入框
      const messageText = this.messageText
      this.messageText = ''
      
      // 发送消息到服务器
      chatApi.sendMessage(message)
        .then(response => {
          if (response.data && response.data.code === 20000) {
            // 更新联系人列表
            const index = this.contacts.findIndex(c => c.id === this.currentContact.id)
            if (index !== -1) {
              this.contacts[index].lastMessage = messageText
              this.contacts[index].lastTime = new Date().getTime()
              
              // 将当前联系人移到顶部
              const contact = this.contacts.splice(index, 1)[0]
              this.contacts.unshift(contact)
            }
            
            // 如果用户在线，确保通过WebSocket发送消息
            // 注意：这部分已经在后端的ChatMessageController中处理
            // 后端会在保存消息后通过WebSocket发送给接收者
          } else {
            this.$message.error(response.data.message || '消息发送失败')
          }
        })
        .catch(error => {
          console.error('消息发送失败:', error)
          this.$message.error('消息发送失败')
        })
    },
    scrollToBottom() {
      if (this.$refs.messageList) {
        this.$refs.messageList.scrollTop = this.$refs.messageList.scrollHeight
      }
    },
    handleSearch() {
      // 搜索联系人，已在计算属性中实现
    },
    handleSearchClear() {
      this.searchKeyword = ''
    },
    clearHistory() {
      if (!this.currentContact) return
      
      this.$confirm('确认清空与该用户关于此房源的聊天记录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        chatApi.deleteChatHistory(this.currentContact.houseId, this.currentContact.userId, this.landlordId)
          .then(response => {
            if (response.data && response.data.code === 20000) {
              this.currentMessages = []
              this.$message.success('聊天记录已清空')
              
              // 更新联系人列表
              const index = this.contacts.findIndex(c => c.id === this.currentContact.id)
              if (index !== -1) {
                this.contacts[index].lastMessage = '无消息'
                // 从列表中移除该联系人
                this.contacts.splice(index, 1)
                this.currentContact = null
              }
            } else {
              this.$message.error(response.data.message || '清空聊天记录失败')
            }
          })
          .catch(error => {
            console.error('清空聊天记录失败:', error)
            this.$message.error('清空聊天记录失败')
          })
      }).catch(() => {})
    },
    viewHouseDetail() {
      if (!this.currentContact) return
      
      // 在新窗口打开房源详情页
      const routeUrl = this.$router.resolve({
        path: `/frontend/housedetail/${this.currentContact.houseId}`
      })
      window.open(routeUrl.href, '_blank')
    },
    // 标记当前联系人的所有未读消息为已读
    markMessagesAsRead() {
      if (this.currentContact) {
        chatApi.markMessagesAsRead(this.landlordId, this.currentContact.userId)
          .then(response => {
            if (response.data && response.data.code === 20000) {
              // 更新联系人列表中的未读数
              const index = this.contacts.findIndex(c => c.id === this.currentContact.id)
              if (index !== -1) {
                this.contacts[index].unreadCount = 0
              }
            }
          })
          .catch(error => {
            console.error('标记消息为已读失败:', error)
          })
      }
    },
    getMessageAvatar(message) {
      if (message.fromUserId === this.landlordId) {
        return this.processedLandlordAvatar;
      } else {
        return this.getImageUrl(message.fromUserAvatar);
      }
    }
  }
}
</script>

<style scoped>
.chat-messages-container {
  padding: 20px;
  height: calc(100vh - 140px);
  display: flex;
  flex-direction: column;
}

.page-header {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.message-container {
  display: flex;
  height: 100%;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.message-sidebar {
  width: 300px;
  border-right: 1px solid #ebeef5;
  display: flex;
  flex-direction: column;
}

.search-box {
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
}

.contact-list {
  flex: 1;
  overflow-y: auto;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  padding: 12px 15px;
  cursor: pointer;
  border-bottom: 1px solid #f5f5f5;
  position: relative;
}

.contact-item:hover {
  background-color: #f5f7fa;
}

.contact-item.active {
  background-color: #ecf5ff;
}

.avatar {
  position: relative;
  margin-right: 12px;
  flex-shrink: 0;
}

.contact-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.unread-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #f56c6c;
  color: #fff;
  border-radius: 50%;
  min-width: 18px;
  height: 18px;
  line-height: 18px;
  text-align: center;
  font-size: 12px;
  padding: 0 5px;
}

.contact-info {
  flex: 1;
  overflow: hidden;
}

.contact-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.house-info {
  color: #409EFF;
  font-size: 13px;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.last-message {
  color: #999;
  font-size: 13px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.contact-time {
  color: #999;
  font-size: 12px;
  min-width: 40px;
  text-align: right;
  margin-left: 10px;
}

.message-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.chat-header {
  padding: 15px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-title {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 5px;
}

.house-address {
  color: #409EFF;
  font-size: 14px;
}

.chat-actions {
  display: flex;
  gap: 10px;
}

.chat-messages {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
}

.message-item {
  display: flex;
  margin-bottom: 15px;
}

.message-item.self {
  flex-direction: row-reverse;
}

.message-avatar {
  margin: 0 10px;
  flex-shrink: 0;
}

.message-avatar-img {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.message-bubble {
  max-width: 70%;
}

.message-item.self .message-bubble {
  text-align: right;
}

.message-time {
  color: #999;
  font-size: 12px;
  margin-bottom: 5px;
}

.message-content {
  background-color: #f5f5f5;
  padding: 10px 15px;
  border-radius: 4px;
  word-break: break-word;
}

.message-item.self .message-content {
  background-color: #ecf5ff;
  color: #409EFF;
}

.chat-input {
  padding: 15px;
  border-top: 1px solid #ebeef5;
}

.input-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
}

.no-chat-selected, .no-messages, .no-contacts {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
</style> 