<template>
  <div class="review-reply-container">
    <el-card shadow="hover">
      <div slot="header" class="clearfix">
        <span>租客评价管理</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          @click="fetchReviews"
        >
          <i class="el-icon-refresh"></i> 刷新
        </el-button>
      </div>
      
      <!-- 筛选栏 -->
      <div class="filter-bar">
        <el-form :inline="true" :model="filterForm">
          <el-form-item label="房源">
            <el-select v-model="filterForm.houseId" placeholder="选择房源" clearable>
              <el-option
                v-for="house in houses"
                :key="house.houseId"
                :label="house.address"
                :value="house.houseId"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="评分">
            <el-select v-model="filterForm.rating" placeholder="评分" clearable>
              <el-option label="5分" value="5"></el-option>
              <el-option label="4分" value="4"></el-option>
              <el-option label="3分" value="3"></el-option>
              <el-option label="2分" value="2"></el-option>
              <el-option label="1分" value="1"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="回复状态">
            <el-select v-model="filterForm.replyStatus" placeholder="回复状态" clearable>
              <el-option label="已回复" value="replied"></el-option>
              <el-option label="未回复" value="unreplied"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleFilter">筛选</el-button>
            <el-button @click="resetFilter">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      
      <!-- 评价列表 -->
      <el-table
        v-loading="loading"
        :data="reviews"
        border
        style="width: 100%"
        :empty-text="loading ? '加载中...' : '暂无评价'"
      >
        <el-table-column type="expand">
          <template slot-scope="props">
            <div class="expanded-review">
              <div class="review-images" v-if="props.row.images">
                <el-image
                  v-for="(image, index) in props.row.images.split(',')"
                  :key="index"
                  :src="image"
                  :preview-src-list="props.row.images.split(',')"
                  fit="cover"
                  class="review-image"
                ></el-image>
              </div>
              <div class="review-reply-form" v-if="!props.row.reply">
                <el-input
                  type="textarea"
                  :rows="3"
                  placeholder="请输入回复内容..."
                  v-model="replyContent[props.row.id]"
                ></el-input>
                <div class="reply-actions">
                  <el-button type="primary" size="small" @click="submitReply(props.row)">提交回复</el-button>
                </div>
              </div>
              <div class="review-reply-content" v-else>
                <div class="reply-header">
                  <span class="reply-title">我的回复</span>
                  <span class="reply-time">{{ formatDateTime(props.row.replyTime) }}</span>
                </div>
                <div class="reply-text">{{ props.row.reply }}</div>
                <div class="reply-actions">
                  <el-button type="text" size="small" @click="editReply(props.row)">修改回复</el-button>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="houseTitle" label="房源信息" min-width="180">
          <template slot-scope="scope">
            <div class="house-info">
              <el-image
                style="width: 60px; height: 60px"
                :src="getHouseImage(scope.row.house)"
                :preview-src-list="[getHouseImage(scope.row.house)]"
                fit="cover"
              ></el-image>
              <div class="house-details">
                <div class="house-title">{{ scope.row.house ? scope.row.house.address : '房源信息获取失败' }}</div>
                <div class="house-address">{{ scope.row.house ? scope.row.house.detail : '' }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="评分" width="120">
          <template slot-scope="scope">
            <el-rate
              :value="scope.row.rating"
              disabled
              text-color="#ff9900"
              :colors="['#FF9900', '#FF9900', '#FF9900']"
            ></el-rate>
          </template>
        </el-table-column>
        <el-table-column prop="content" label="评价内容" min-width="250">
          <template slot-scope="scope">
            <div class="review-content-preview">
              <div class="review-author">
                {{ scope.row.anonymous ? '匿名用户' : scope.row.userName }} 
                <span class="review-time">{{ formatDate(scope.row.createTime) }}</span>
              </div>
              <div class="review-text">{{ scope.row.content }}</div>
              <div class="review-tags" v-if="scope.row.tags">
                <el-tag size="mini" v-for="(tag, index) in scope.row.tags.split(',')" :key="index" type="info">{{ tag }}</el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="reply" label="回复状态" width="100">
          <template slot-scope="scope">
            <el-tag type="success" v-if="scope.row.reply">已回复</el-tag>
            <el-tag type="info" v-else>未回复</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template slot-scope="scope">
            <el-button
              type="primary"
              size="mini"
              @click="showReplyDialog(scope.row)"
              v-if="!scope.row.reply"
            >回复</el-button>
            <el-button
              type="success"
              size="mini"
              @click="showReplyDialog(scope.row)"
              v-else
            >修改回复</el-button>
            <el-button
              type="danger"
              size="mini"
              @click="handleDeleteReply(scope.row)"
              v-if="scope.row.reply"
            >删除回复</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
      </div>
    </el-card>
    
    <!-- 回复对话框 -->
    <el-dialog :title="currentReview && currentReview.reply ? '修改回复' : '回复评价'" :visible.sync="replyDialogVisible" width="50%">
      <div v-if="currentReview" class="reply-dialog-content">
        <div class="review-detail">
          <div class="review-author">
            {{ currentReview.anonymous ? '匿名用户' : currentReview.userName }} 
            <span class="review-time">{{ formatDate(currentReview.createTime) }}</span>
          </div>
          <div class="review-rating">
            <el-rate
              :value="currentReview.rating"
              disabled
              text-color="#ff9900"
              :colors="['#FF9900', '#FF9900', '#FF9900']"
            ></el-rate>
          </div>
          <div class="review-text">{{ currentReview.content }}</div>
          <div class="review-images" v-if="currentReview.images">
            <el-image
              v-for="(image, index) in currentReview.images.split(',')"
              :key="index"
              :src="image"
              :preview-src-list="currentReview.images.split(',')"
              fit="cover"
              class="review-image"
            ></el-image>
          </div>
          <div class="review-tags" v-if="currentReview.tags">
            <el-tag size="mini" v-for="(tag, index) in currentReview.tags.split(',')" :key="index" type="info">{{ tag }}</el-tag>
          </div>
        </div>
        
        <div class="reply-form">
          <el-form :model="replyForm" :rules="replyRules" ref="replyForm" label-width="80px">
            <el-form-item label="回复内容" prop="content">
              <el-input
                type="textarea"
                :rows="4"
                placeholder="请输入回复内容..."
                v-model="replyForm.content"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="replyDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitDialogReply">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getBackstageUser, isAdmin, isOwner } from '../utils/auth'
import { getImageUrl } from '../utils/imageUtils'
import moment from 'moment'
import reviewApi from '../frontend/api/review'

export default {
  name: 'ReviewReply',
  data() {
    return {
      loading: false,
      houses: [],
      reviews: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      filterForm: {
        houseId: '',
        rating: '',
        replyStatus: ''
      },
      replyContent: {}, // 存储每个评价的回复内容
      replyDialogVisible: false,
      currentReview: null,
      replyForm: {
        content: ''
      },
      replyRules: {
        content: [
          { required: true, message: '请输入回复内容', trigger: 'blur' },
          { min: 5, message: '回复内容不能少于5个字符', trigger: 'blur' }
        ]
      },
      userId: null
    }
  },
  created() {
    const userInfo = getBackstageUser()
    if (userInfo && userInfo.userInfo) {
      this.userId = userInfo.userInfo.id
    }
    
    this.fetchHouses()
    this.fetchReviews()
  },
  methods: {
    // 获取我的房源列表
    async fetchHouses() {
      try {
        const params = {}
        
        if (isOwner(false)) {
          params.ownerId = this.userId
        }
        
        const res = await this.$http.get(`/house/getownerhouselist?ownerId=${this.userId}`)
        
        if (res.data && res.data.flag) {
          this.houses = res.data.data || []
        }
      } catch (error) {
        console.error('获取房源列表失败:', error)
        this.$message.error('获取房源列表失败')
      }
    },
    
    // 获取评价列表
    async fetchReviews() {
      this.loading = true
      try {
        // 如果是房东，使用专门的房东评价接口
        let res;
        if (isOwner(false)) {
          res = await this.$http.get(`/reviews/owner/${this.userId}`)
        } else {
          const params = {
            page: this.currentPage,
            limit: this.pageSize,
            ...this.filterForm
          }

          // 处理回复状态筛选
          if (params.replyStatus === 'replied') {
            params.hasReply = true
            delete params.replyStatus
          } else if (params.replyStatus === 'unreplied') {
            params.hasReply = false
            delete params.replyStatus
          }

          res = await reviewApi.getReviews(params)
        }

        if (res.data && res.data.flag) {
          // 房东接口返回的是数组，管理员接口返回的是分页对象
          if (isOwner(false)) {
            let allReviews = res.data.data || []

            // 客户端筛选
            if (this.filterForm.houseId) {
              allReviews = allReviews.filter(review => review.houseId == this.filterForm.houseId)
            }
            if (this.filterForm.rating) {
              allReviews = allReviews.filter(review => Math.floor(review.rating) == this.filterForm.rating)
            }
            if (this.filterForm.replyStatus === 'replied') {
              allReviews = allReviews.filter(review => review.reply && review.reply.trim() !== '')
            } else if (this.filterForm.replyStatus === 'unreplied') {
              allReviews = allReviews.filter(review => !review.reply || review.reply.trim() === '')
            }

            this.total = allReviews.length

            // 客户端分页
            const start = (this.currentPage - 1) * this.pageSize
            const end = start + this.pageSize
            this.reviews = allReviews.slice(start, end)
          } else {
            this.reviews = res.data.data.list || []
            this.total = res.data.data.total || 0
          }
          
          // 获取房源信息
          await this.fetchHouseInfo()
          
          // 初始化回复内容
          this.reviews.forEach(review => {
            if (!review.reply) {
              this.$set(this.replyContent, review.id, '')
            }
          })
        } else {
          this.$message.error('获取评价列表失败')
        }
      } catch (error) {
        console.error('获取评价列表失败:', error)
        this.$message.error('获取评价列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 获取房源信息
    async fetchHouseInfo() {
      try {
        if (!this.reviews || this.reviews.length === 0) {
          return
        }
        
        for (let i = 0; i < this.reviews.length; i++) {
          const review = this.reviews[i]
          if (!review.houseId) continue
          
          try {
            const houseRes = await this.$http.get(`/house/gethousedetail?houseId=${review.houseId}`)
            if (houseRes.data && houseRes.data.flag) {
              this.$set(this.reviews[i], 'house', houseRes.data.data)
            }
          } catch (error) {
            console.error(`获取房源信息失败:`, error)
          }
        }
      } catch (error) {
        console.error('获取房源信息失败:', error)
      }
    },
    
    // 处理筛选
    handleFilter() {
      this.currentPage = 1
      this.fetchReviews()
    },
    
    // 重置筛选
    resetFilter() {
      this.filterForm = {
        houseId: '',
        rating: '',
        replyStatus: ''
      }
      this.handleFilter()
    },
    
    // 显示回复对话框
    showReplyDialog(review) {
      this.currentReview = { ...review }
      this.replyForm.content = review.reply || ''
      this.replyDialogVisible = true
    },
    
    // 编辑回复
    editReply(review) {
      this.showReplyDialog(review)
    },
    
    // 提交回复（表格内）
    async submitReply(review) {
      const content = this.replyContent[review.id]
      if (!content || content.trim() === '') {
        this.$message.warning('请输入回复内容')
        return
      }
      
      try {
        const res = await reviewApi.replyReview(review.id, content)
        
        if (res.data && res.data.flag) {
          this.$message.success('回复成功')
          this.fetchReviews()
        } else {
          this.$message.error(res.data.message || '回复失败')
        }
      } catch (error) {
        console.error('回复评价失败:', error)
        this.$message.error('回复评价失败')
      }
    },
    
    // 提交回复（对话框）
    submitDialogReply() {
      this.$refs.replyForm.validate(async (valid) => {
        if (valid) {
          try {
            const res = await reviewApi.replyReview(this.currentReview.id, this.replyForm.content)
            
            if (res.data && res.data.flag) {
              this.$message.success('回复成功')
              this.replyDialogVisible = false
              this.fetchReviews()
            } else {
              this.$message.error(res.data.message || '回复失败')
            }
          } catch (error) {
            console.error('回复评价失败:', error)
            this.$message.error('回复评价失败')
          }
        } else {
          return false
        }
      })
    },
    
    // 删除回复
    async handleDeleteReply(review) {
      try {
        await this.$confirm('确定要删除此回复吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const res = await reviewApi.deleteReply(review.id)
        
        if (res.data && res.data.flag) {
          this.$message.success('删除回复成功')
          this.fetchReviews()
        } else {
          this.$message.error(res.data.message || '删除回复失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除回复失败:', error)
          this.$message.error('删除回复失败')
        }
      }
    },
    
    // 获取房屋图片
    getHouseImage(house) {
      if (!house) return require('../assets/showcase.jpg')
      
      if (house.imageUrl) {
        return getImageUrl(house.imageUrl)
      }
      
      return getImageUrl('/img/showcase.jpg')
    },
    
    // 格式化日期
    formatDate(dateStr) {
      return moment(dateStr).format('YYYY-MM-DD')
    },
    
    // 格式化日期时间
    formatDateTime(dateStr) {
      return moment(dateStr).format('YYYY-MM-DD HH:mm')
    },
    
    // 处理页大小变化
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchReviews()
    },
    
    // 处理页码变化
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchReviews()
    }
  }
}
</script>

<style scoped>
.review-reply-container {
  padding: 20px;
}

.filter-bar {
  margin-bottom: 20px;
}

.house-info {
  display: flex;
  align-items: center;
}

.house-details {
  margin-left: 10px;
  overflow: hidden;
}

.house-title {
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.house-address {
  color: #909399;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.review-content-preview {
  max-height: 150px;
  overflow: hidden;
}

.review-author {
  font-weight: bold;
  margin-bottom: 5px;
}

.review-time {
  font-weight: normal;
  color: #909399;
  font-size: 12px;
  margin-left: 5px;
}

.review-text {
  line-height: 1.6;
  margin-bottom: 10px;
}

.review-tags {
  margin-bottom: 10px;
}

.review-tags .el-tag {
  margin-right: 5px;
  margin-bottom: 5px;
}

.expanded-review {
  padding: 20px;
}

.review-images {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 15px;
}

.review-image {
  width: 100px;
  height: 100px;
  margin-right: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
  overflow: hidden;
}

.review-reply-form {
  margin-top: 15px;
}

.reply-actions {
  margin-top: 10px;
  text-align: right;
}

.review-reply-content {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-top: 15px;
}

.reply-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.reply-title {
  font-weight: bold;
}

.reply-time {
  font-size: 12px;
  color: #909399;
}

.reply-text {
  line-height: 1.6;
  margin-bottom: 10px;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.reply-dialog-content {
  padding: 0 20px;
}

.review-detail {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #EBEEF5;
}

.review-rating {
  margin: 10px 0;
}
</style> 