package com.house.pojo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 支付记录实体类
 */
@Data
public class Payment {

    /**
     * 支付ID
     */
    private Integer id;

    /**
     * 支付单号
     */
    private String paymentNo;

    /**
     * 订单ID
     */
    private Integer orderId;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 支付金额
     */
    private BigDecimal amount;

    /**
     * 支付类型：
     * deposit - 押金
     * rent - 租金
     * service - 服务费
     * other - 其他
     */
    private String type;

    /**
     * 支付方式：
     * alipay - 支付宝
     * wechat - 微信
     * bank - 银行卡
     * other - 其他
     */
    private String method;

    /**
     * 支付状态：
     * pending - 待支付
     * success - 支付成功
     * failed - 支付失败
     * refunded - 已退款
     */
    private String status;

    /**
     * 交易流水号
     */
    private String transactionId;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
} 