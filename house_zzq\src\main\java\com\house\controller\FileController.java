package com.house.controller;

import com.house.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.UUID;

/**
 * 文件上传控制器
 */
@RestController
@CrossOrigin(origins = "*", maxAge = 3600)
@Slf4j
public class FileController {
    
    // 图片保存路径
    @Value("${file.upload-dir}")
    private String uploadDir;
    
    // 图片访问URL前缀
    @Value("${file.access-path}")
    private String accessPath;
    
    /**
     * 上传图片
     *
     * @param file 上传的文件
     * @return 上传结果
     */
    @PostMapping("/file/upload")
    public Result<String> uploadFile(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return Result.error("上传的文件不能为空");
        }
        
        try {
            // 确保目录存在
            File uploadDirFile = new File(uploadDir);
            if (!uploadDirFile.exists()) {
                uploadDirFile.mkdirs();
            }
            
            // 生成新文件名
            String originalFilename = file.getOriginalFilename();
            String extension = originalFilename.substring(originalFilename.lastIndexOf("."));
            String newFilename = UUID.randomUUID().toString() + extension;
            
            // 保存文件
            Path path = Paths.get(uploadDir, newFilename);
            Files.write(path, file.getBytes());
            
            // 记录文件上传信息
            log.info("文件上传成功: 原始名称=[{}], 新文件名=[{}], 文件大小=[{}KB]", 
                    originalFilename, newFilename, file.getSize() / 1024);
            
            // 返回文件访问URL - 确保返回/api/images/开头的路径
            String fileUrl = "/api/images/" + newFilename;
            return Result.success("文件上传成功", fileUrl);
            
        } catch (IOException e) {
            log.error("文件上传失败: {}", e.getMessage());
            return Result.error("文件上传失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除图片
     *
     * @param filename 文件名
     * @return 删除结果
     */
    @DeleteMapping("/file/delete")
    public Result<Object> deleteFile(@RequestParam("filename") String filename) {
        try {
            // 防止路径遍历攻击
            if (filename.contains("..")) {
                log.warn("检测到可能的路径遍历攻击: {}", filename);
                return Result.error("文件名无效");
            }
            
            // 获取文件名（去掉可能的路径前缀）
            String originalFilename = filename;
            filename = new File(filename).getName();
            
            // 删除文件
            Path path = Paths.get(uploadDir, filename);
            boolean deleted = Files.deleteIfExists(path);
            
            log.info("文件删除{}: 文件路径=[{}]", deleted ? "成功" : "失败(文件不存在)", originalFilename);
            return Result.success("文件删除成功");
            
        } catch (IOException e) {
            log.error("文件删除失败: {}", e.getMessage());
            return Result.error("文件删除失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取图片
     * 
     * @param filename 文件名
     * @return 图片文件
     */
    @GetMapping("/api/images/{filename:.+}")
    public ResponseEntity<Resource> getImage(@PathVariable String filename) {
        try {
            // 防止路径遍历攻击
            if (filename.contains("..")) {
                log.warn("检测到可能的路径遍历攻击: {}", filename);
                return ResponseEntity.badRequest().build();
            }
            
            // 构建文件路径
            Path filePath = Paths.get(uploadDir, filename);
            Resource resource = new FileSystemResource(filePath.toFile());
            
            if (!resource.exists()) {
                log.warn("请求的图片不存在: {}", filename);
                return ResponseEntity.notFound().build();
            }
            
            // 确定文件的MIME类型
            String contentType = Files.probeContentType(filePath);
            if (contentType == null) {
                contentType = "application/octet-stream";
            }
            
            log.info("提供图片访问: 文件=[{}], 类型=[{}]", filename, contentType);
            
            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + resource.getFilename() + "\"")
                    .body(resource);
                    
        } catch (IOException e) {
            log.error("图片访问失败: {}", e.getMessage());
            return ResponseEntity.status(500).build();
        }
    }
} 