<template>
  <div class="login-container no-scroll-page">
   
    <div class="login-content">
      <div class="welcome-section">
        <h1 class="welcome-title">欢迎回来</h1>
        <p class="welcome-subtitle">您的理想居所，从这里开始</p>
        <div class="welcome-features">
          <div class="feature-item">
            <i class="el-icon-house"></i>
            <span>精选房源</span>
          </div>
          <div class="feature-item">
            <i class="el-icon-lock"></i>
            <span>安全保障</span>
          </div>
          <div class="feature-item">
            <i class="el-icon-service"></i>
            <span>贴心服务</span>
          </div>
        </div>
      </div>
      <div class="login-box">
        <h2>用户登录</h2>
        <el-form :model="loginForm" status-icon :rules="rules" ref="loginForm" label-width="80px">
          <el-form-item label="账号" prop="account">
            <el-input v-model="loginForm.account" prefix-icon="el-icon-user" placeholder="请输入账号"></el-input>
          </el-form-item>
          <el-form-item label="密码" prop="password">
            <el-input type="password" v-model="loginForm.password" prefix-icon="el-icon-lock" placeholder="请输入密码" @keyup.enter.native="submitForm('loginForm')"></el-input>
          </el-form-item>
          <el-form-item class="login-actions">
            <el-button type="primary" @click="submitForm('loginForm')" :loading="loading" class="btn-primary login-btn">登录</el-button>
            <el-button @click="resetForm('loginForm')" plain class="btn-text">重置</el-button>
          </el-form-item>
          <div class="form-footer">
            <div class="register-link">
              还没有账号？<router-link to="/frontend/register" class="btn-text">立即注册</router-link>
            </div>
            <div class="home-link">
              <router-link to="/frontend/home" class="btn-text">
                <i class="el-icon-back"></i> 返回首页
              </router-link>
            </div>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { userApi } from './api'
import { setUser, getFrontendUser } from '../utils/auth'

export default {
  name: 'FrontendLogin',
  data() {
    return {
      loginForm: {
        account: '',
        password: ''
      },
      loading: false,
      rules: {
        account: [
          { required: true, message: '请输入账号', trigger: 'blur' },
          { min: 4, max: 16, message: '长度在 4 到 16 个字符', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 18, message: '长度在 6 到 18 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.loading = true
          // 构造登录数据，将account转换为username以匹配后端
          const loginData = {
            username: this.loginForm.account,
            password: this.loginForm.password,
            system: 'frontend'
          }
          userApi.login(loginData)
            .then(response => {
              if (response.data && response.data.flag) {
                // 构造用户信息对象，从data字段中获取token和userInfo
                const loginResult = {
                  flag: response.data.flag,
                  token: response.data.data.token,
                  userInfo: response.data.data.userInfo,
                  roles: response.data.data.roles
                }

                // 保存用户信息
                setUser(loginResult, true) // 第二个参数true表示这是前台用户
                this.$message({
                  message: '登录成功!',
                  type: 'success'
                })

                // 登录成功，初始化WebSocket连接
                this.initWebSocket(loginResult.userInfo.id)

                // 前台登录，无论角色都跳转到前台首页
                this.$router.push('/frontend/home')
              } else {
                this.$message.error(response.data.message || '账号或密码错误，请重试')
              }
            })
            .catch(error => {
              console.error('登录请求失败:', error)
              this.$message.error('登录失败，请稍后再试')
            })
            .finally(() => {
              this.loading = false
            })
        } else {
          console.log('表单验证失败')
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    initWebSocket(userId) {
      // 初始化WebSocket连接，用于消息通知
      if ('WebSocket' in window) {
        const wsUrl = `ws://${window.location.host}/ws/frontend/${userId}`
        
        // 将WebSocket实例存储在全局Vue实例中，方便其他组件访问
        this.$root.$data.frontendWebSocket = new WebSocket(wsUrl)
        
        // 连接成功回调
        this.$root.$data.frontendWebSocket.onopen = () => {
          console.log('前台WebSocket连接已建立')
        }
        
        // 接收消息回调
        this.$root.$data.frontendWebSocket.onmessage = (event) => {
          const message = JSON.parse(event.data)
          console.log('前台收到消息:', message)
          
          // 触发全局事件，通知消息组件更新
          this.$root.$emit('frontend-new-message', message)
          
          // 显示通知
          this.$notify({
            title: '新消息',
            message: message.content,
            type: 'info',
            duration: 5000
          })
        }
        
        // 连接关闭回调
        this.$root.$data.frontendWebSocket.onclose = () => {
          console.log('前台WebSocket连接已关闭')
        }
        
        // 连接错误回调
        this.$root.$data.frontendWebSocket.onerror = (error) => {
          console.error('前台WebSocket发生错误:', error)
        }
      } else {
        console.error('您的浏览器不支持WebSocket')
      }
    }
  }
}
</script>

<style scoped>
/* 防止滚动条 */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
}

.login-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: url('../assets/bg.jpg') no-repeat center center;
  background-size: cover;
  position: relative;
}

.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 0;
}

.login-header {
  position: relative;
  z-index: 1;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 15px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.logo {
  width: 1200px;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 20px;
}

.logo a {
  display: flex;
  align-items: center;
  text-decoration: none;
}

.logo-img {
  height: 40px;
  margin-right: 10px;
}

.logo-text {
  font-size: 22px;
  font-weight: bold;
  color: #409EFF;
}

.login-content {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh - 70px);
  padding: 20px;
  overflow-y: auto;
  overflow-x: hidden;
}

.welcome-section {
  color: #fff;
  width: 400px;
  margin-right: 80px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.welcome-title {
  font-size: 42px;
  margin-bottom: 15px;
  font-weight: 600;
}

.welcome-subtitle {
  font-size: 18px;
  margin-bottom: 30px;
  opacity: 0.9;
}

.welcome-features {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.feature-item {
  display: flex;
  align-items: center;
  font-size: 16px;
}

.feature-item i {
  font-size: 24px;
  margin-right: 15px;
  color: #409EFF;
}

.login-box {
  width: 400px;
  padding: 35px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.login-box:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
}

.login-box h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #409EFF;
  font-weight: 600;
}

.login-actions {
  margin-top: 20px;
}

.login-btn {
  width: 100%;
  margin-bottom: 15px;
}

.form-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  padding: 0 20px;
}

.register-link a, .home-link a {
  text-decoration: none;
  color: #409EFF;
}

.register-link a:hover, .home-link a:hover {
  color: #66b1ff;
}

/* 响应式设计 */
@media screen and (max-width: 992px) {
  .login-content {
    flex-direction: column;
  }
  
  .welcome-section {
    margin-right: 0;
    margin-bottom: 40px;
    text-align: center;
    width: 100%;
    max-width: 400px;
  }
  
  .welcome-features {
    flex-direction: row;
    justify-content: center;
    gap: 30px;
  }
}

@media screen and (max-width: 576px) {
  .login-box {
    width: 100%;
    padding: 25px;
  }
  
  .welcome-features {
    flex-direction: column;
    align-items: center;
  }
}
</style> 