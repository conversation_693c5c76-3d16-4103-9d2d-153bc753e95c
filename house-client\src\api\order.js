import request from '@/utils/request'

// 创建订单
export function createOrder(data) {
  return request({
    url: '/orders',
    method: 'post',
    data
  })
}

// 根据订单号获取订单详情
export function getOrderByOrderNo(orderNo) {
  return request({
    url: `/orders/${orderNo}`,
    method: 'get'
  })
}

// 根据ID获取订单详情
export function getOrderById(id) {
  return request({
    url: `/orders/id/${id}`,
    method: 'get'
  })
}

// 更新订单状态
export function updateOrderStatus(orderNo, status) {
  return request({
    url: `/orders/${orderNo}/status`,
    method: 'put',
    params: { status }
  })
}

// 取消订单
export function cancelOrder(orderNo) {
  return request({
    url: `/orders/${orderNo}/cancel`,
    method: 'put'
  })
}

// 支付押金
export function payDeposit(orderNo, paymentMethod) {
  return request({
    url: `/orders/${orderNo}/pay-deposit`,
    method: 'put',
    params: { paymentMethod }
  })
}

// 支付租金
export function payRent(orderNo, paymentMethod) {
  return request({
    url: `/orders/${orderNo}/pay-rent`,
    method: 'put',
    params: { paymentMethod }
  })
}

// 确认入住
export function confirmCheckIn(orderNo) {
  return request({
    url: `/orders/${orderNo}/check-in`,
    method: 'put'
  })
}

// 完成订单
export function completeOrder(orderNo) {
  return request({
    url: `/orders/${orderNo}/complete`,
    method: 'put'
  })
}

// 获取订单列表
export function getOrderList(params) {
  return request({
    url: '/orders',
    method: 'get',
    params
  })
}

// 获取用户订单统计
export function getOrderStatsByUserId(userId) {
  return request({
    url: `/orders/stats/${userId}`,
    method: 'get'
  })
}

// 获取订单支付记录
export function getPaymentsByOrderId(orderId) {
  return request({
    url: `/payments/order/${orderId}`,
    method: 'get'
  })
}