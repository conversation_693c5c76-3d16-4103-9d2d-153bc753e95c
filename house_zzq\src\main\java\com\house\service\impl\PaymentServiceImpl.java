package com.house.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.house.mapper.PaymentMapper;
import com.house.pojo.Payment;
import com.house.service.NotificationService;
import com.house.service.PaymentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * 支付服务实现类
 */
@Service
public class PaymentServiceImpl implements PaymentService {

    @Autowired
    private PaymentMapper paymentMapper;

    @Autowired
    private NotificationService notificationService;

    /**
     * 生成唯一支付单号
     */
    private String generatePaymentNo() {
        return "PAY" + System.currentTimeMillis() + String.format("%04d", new Random().nextInt(10000));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Payment createPayment(Payment payment) {
        // 生成支付单号
        payment.setPaymentNo(generatePaymentNo());
        
        // 设置初始状态为待支付
        payment.setStatus("pending");
        
        // 设置时间
        Date now = new Date();
        payment.setCreateTime(now);
        payment.setUpdateTime(now);
        
        // 插入支付记录
        paymentMapper.insert(payment);
        
        return payment;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Payment createDepositPayment(Integer orderId, Integer userId, BigDecimal amount, String method) {
        Payment payment = new Payment();
        payment.setOrderId(orderId);
        payment.setUserId(userId);
        payment.setAmount(amount);
        payment.setMethod(method);
        payment.setType("deposit");
        
        return createPayment(payment);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Payment createRentPayment(Integer orderId, Integer userId, BigDecimal amount, String method) {
        Payment payment = new Payment();
        payment.setOrderId(orderId);
        payment.setUserId(userId);
        payment.setAmount(amount);
        payment.setMethod(method);
        payment.setType("rent");

        return createPayment(payment);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Payment createServiceFeePayment(Integer orderId, Integer userId, BigDecimal amount, String method) {
        Payment payment = new Payment();
        payment.setOrderId(orderId);
        payment.setUserId(userId);
        payment.setAmount(amount);
        payment.setMethod(method);
        payment.setType("service");

        return createPayment(payment);
    }

    @Override
    public Payment getPaymentByPaymentNo(String paymentNo) {
        return paymentMapper.selectByPaymentNo(paymentNo);
    }

    @Override
    public Payment getPaymentById(Integer id) {
        return paymentMapper.selectById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePaymentStatus(String paymentNo, String status, String transactionId) {
        Date now = new Date();
        int result = paymentMapper.updateStatus(paymentNo, status, transactionId, now);
        
        if (result > 0) {
            // 获取支付记录
            Payment payment = getPaymentByPaymentNo(paymentNo);
            
            // 发送通知
            if (payment != null) {
                String title;
                String content;
                
                if ("success".equals(status)) {
                    title = "支付成功";
                    content = "您的支付单号 " + paymentNo + " 已成功支付，金额：¥" + payment.getAmount();
                } else if ("failed".equals(status)) {
                    title = "支付失败";
                    content = "您的支付单号 " + paymentNo + " 支付失败，请重新尝试。";
                } else if ("refunded".equals(status)) {
                    title = "退款成功";
                    content = "您的支付单号 " + paymentNo + " 已成功退款，金额：¥" + payment.getAmount();
                } else {
                    title = "支付状态更新";
                    content = "您的支付单号 " + paymentNo + " 状态已更新为：" + status;
                }
                
                notificationService.sendPaymentNotification(
                        String.valueOf(payment.getUserId()),
                        title,
                        content,
                        payment.getPaymentNo()
                );
            }
            
            return true;
        }
        
        return false;
    }

    @Override
    public List<Payment> getPaymentsByOrderId(Integer orderId) {
        return paymentMapper.selectByOrderId(orderId);
    }

    @Override
    public List<Payment> getPaymentsByUserId(Integer userId) {
        return paymentMapper.selectByUserId(userId);
    }

    @Override
    public PageInfo<Payment> getPaymentList(Map<String, Object> params) {
        // 获取分页参数
        int pageNum = params.get("page") != null ? Integer.parseInt(params.get("page").toString()) : 1;
        int pageSize = params.get("limit") != null ? Integer.parseInt(params.get("limit").toString()) : 10;

        // 设置分页
        PageHelper.startPage(pageNum, pageSize);

        // 查询数据
        List<Payment> payments = paymentMapper.selectByCondition(params);

        return new PageInfo<>(payments);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateOrderPaymentStatus(Integer orderId, String status, String transactionId) {
        int result = paymentMapper.updateOrderPaymentStatus(orderId, status, transactionId);
        return result > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean handlePaymentCallback(String paymentNo, String transactionId, String status) {
        // 更新支付状态
        boolean result = updatePaymentStatus(paymentNo, status, transactionId);
        
        if (result) {
            // 获取支付记录
            Payment payment = getPaymentByPaymentNo(paymentNo);
            
            // 处理其他业务逻辑，如更新订单状态等
            // 这里可以根据实际需求进行扩展
            
            return true;
        }
        
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean refundPayment(String paymentNo, String reason) {
        // 获取支付记录
        Payment payment = getPaymentByPaymentNo(paymentNo);
        
        if (payment == null) {
            return false;
        }
        
        // 检查支付状态
        if (!"success".equals(payment.getStatus())) {
            return false;
        }
        
        // 更新支付状态为已退款
        boolean result = updatePaymentStatus(paymentNo, "refunded", payment.getTransactionId());
        
        if (result) {
            // 发送退款通知
            notificationService.sendPaymentNotification(
                    String.valueOf(payment.getUserId()),
                    "退款成功",
                    "您的支付单号 " + paymentNo + " 已成功退款，金额：¥" + payment.getAmount() + "，退款原因：" + reason,
                    payment.getPaymentNo()
            );
            
            return true;
        }
        
        return false;
    }
} 