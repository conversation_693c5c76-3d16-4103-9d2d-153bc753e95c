import axios from 'axios';

/**
 * 上传图片到后端服务器
 * @param {File} file 图片文件
 * @return {Promise} 上传结果，成功返回图片URL
 */
export async function uploadImage(file) {
  if (!file) {
    return Promise.reject(new Error('未选择文件'));
  }
  
  // 检查文件类型
  if (!file.type.startsWith('image/')) {
    return Promise.reject(new Error('请上传图片文件'));
  }
  
  // 检查文件大小，最大10MB
  if (file.size > 10 * 1024 * 1024) {
    return Promise.reject(new Error('图片大小不能超过10MB'));
  }
  
  // 创建FormData对象
  const formData = new FormData();
  formData.append('file', file);
  
  try {
    // 发送请求
    const response = await axios.post(
      'http://localhost:9002/file/upload',
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }
    );
    
    // 返回结果
    if (response.data && response.data.code === 20000) {
      return response.data.data; // 返回图片URL
    } else {
      throw new Error(response.data.message || '上传失败');
    }
  } catch (error) {
    console.error('上传图片失败:', error);
    throw error;
  }
}

/**
 * 删除图片
 * @param {String} imageUrl 图片URL
 * @return {Promise} 删除结果
 */
export async function deleteImage(imageUrl) {
  if (!imageUrl) {
    return Promise.resolve();
  }
  
  // 提取文件名
  const filename = imageUrl.substring(imageUrl.lastIndexOf('/') + 1);
  
  try {
    // 发送请求
    const response = await axios.delete(
      `http://localhost:9002/file/delete?filename=${filename}`
    );
    
    // 返回结果
    if (response.data && response.data.code === 20000) {
      return response.data;
    } else {
      throw new Error(response.data.message || '删除失败');
    }
  } catch (error) {
    console.error('删除图片失败:', error);
    throw error;
  }
} 