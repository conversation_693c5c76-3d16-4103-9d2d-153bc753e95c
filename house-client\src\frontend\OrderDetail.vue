<template>
  <div class="order-detail-page content-scroll">
    <Header />
    <div class="container">
      <div class="order-detail-card">
        <h2>订单详情</h2>
        <el-divider></el-divider>

        <!-- Loading状态 -->
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="8" animated />
        </div>

        <!-- 错误状态 -->
        <div v-else-if="!order" class="error-container">
          <el-empty description="订单信息加载失败">
            <el-button type="primary" @click="fetchOrderDetail">重新加载</el-button>
          </el-empty>
        </div>

        <!-- 订单状态和进度 -->
        <div class="order-progress" v-else>
          <el-steps :active="getStepActive" finish-status="success">
            <el-step title="提交订单" :description="formatDateTime(order.createTime)"></el-step>
            <el-step title="签署合同" :description="order.contractSignTime ? formatDateTime(order.contractSignTime) : '待签署'"></el-step>
            <el-step title="支付押金" :description="order.depositPayTime ? formatDateTime(order.depositPayTime) : '待支付'"></el-step>
            <el-step title="入住" :description="order.checkInTime ? formatDateTime(order.checkInTime) : '待入住'"></el-step>
            <el-step title="完成" :description="order.status === 'completed' ? formatDateTime(order.updateTime) : '进行中'"></el-step>
          </el-steps>
          
          <!-- 订单状态卡片 -->
          <div class="status-card" :class="`status-${order.status}`">
            <div class="status-icon">
              <i :class="getStatusIcon"></i>
            </div>
            <div class="status-info">
              <h3>{{ getStatusText }}</h3>
              <p>{{ getStatusDesc }}</p>
              
              <div class="status-actions" v-if="order.status === ORDER_STATUS.CONTRACT_PENDING">
                <el-button type="primary" @click="goToContract">签署合同</el-button>
                <el-button type="danger" @click="cancelOrder">取消订单</el-button>
              </div>

              <div class="status-actions" v-else-if="order.status === ORDER_STATUS.UNPAID">
                <el-button type="primary" @click="goToPay">立即支付</el-button>
                <el-button type="danger" @click="cancelOrder">取消订单</el-button>
              </div>

              <div class="status-actions" v-else-if="order.status === ORDER_STATUS.PENDING">
                <el-button type="primary" @click="goToContract">查看合同</el-button>
              </div>
              
              <div class="status-actions" v-else-if="order.status === ORDER_STATUS.RENTING">
                <el-button type="primary" @click="goToContract">查看合同</el-button>
                <el-button type="success" @click="payRent">支付租金</el-button>
                <el-button type="success" @click="goToReview" v-if="!order.reviewed">评价房源</el-button>
                <el-button type="info" v-else disabled>已评价</el-button>
              </div>

              <div class="status-actions" v-else-if="order.status === ORDER_STATUS.COMPLETED">
                <el-button type="primary" @click="goToContract">查看合同</el-button>
                <el-button type="success" @click="goToReview" v-if="!order.hasReviewed">评价房源</el-button>
                <el-button type="info" v-else disabled>已评价</el-button>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 订单详细信息 -->
        <div class="order-info" v-if="order">
          <el-row :gutter="20">
            <el-col :span="24">
              <h3>订单信息</h3>
              <el-descriptions :column="2" border>
                <el-descriptions-item label="订单编号">{{ order.orderNo }}</el-descriptions-item>
                <el-descriptions-item label="下单时间">{{ formatDateTime(order.createTime) }}</el-descriptions-item>
                <el-descriptions-item label="订单状态">
                  <el-tag :type="getStatusType">{{ getStatusText }}</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="支付方式">{{ getPaymentMethod }}</el-descriptions-item>
              </el-descriptions>
            </el-col>
          </el-row>
          
          <el-row :gutter="20" class="section">
            <el-col :span="24">
              <h3>房源信息</h3>
              <div class="house-card" v-if="house">
                <div class="house-image">
                  <img :src="house.images ? house.images.split(',')[0] : '/img/showcase.jpg'" alt="房源图片">
                </div>
                <div class="house-details">
                  <h4>{{ house.title }}</h4>
                  <p class="address">{{ house.address }}</p>
                  <p class="specs">{{ house.area }}㎡ | {{ house.bedroom }}室{{ house.living }}厅{{ house.bathroom }}卫 | {{ house.orientation }}</p>
                  <p class="price">月租金: ¥{{ house.price }}</p>
                  
                  <el-button size="small" type="primary" plain @click="viewHouseDetail">查看房源详情</el-button>
                </div>
              </div>
            </el-col>
          </el-row>
          
          <el-row :gutter="20" class="section">
            <el-col :span="24">
              <h3>租赁信息</h3>
              <el-descriptions :column="2" border>
                <el-descriptions-item label="租期">{{ formatDate(order.startDate) }} 至 {{ formatDate(order.endDate) }}</el-descriptions-item>
                <el-descriptions-item label="租赁月数">{{ order.duration }}个月</el-descriptions-item>
                <el-descriptions-item label="月租金">¥{{ order.monthlyPrice }}</el-descriptions-item>
                <el-descriptions-item label="押金">¥{{ order.deposit }}</el-descriptions-item>
                <el-descriptions-item label="服务费">¥{{ order.serviceFee }}</el-descriptions-item>
                <el-descriptions-item label="总金额">
                  <span class="total-amount">¥{{ order.totalPrice }}</span>
                </el-descriptions-item>
              </el-descriptions>
            </el-col>
          </el-row>
          
          <el-row :gutter="20" class="section">
            <el-col :span="24">
              <h3>支付记录</h3>
              <el-table :data="paymentRecords" style="width: 100%" v-loading="paymentsLoading" border>
                <el-table-column prop="paymentNo" label="支付单号" width="180"></el-table-column>
                <el-table-column prop="type" label="类型">
                  <template slot-scope="scope">
                    {{ getPaymentTypeText(scope.row.type) }}
                  </template>
                </el-table-column>
                <el-table-column prop="amount" label="金额">
                  <template slot-scope="scope">
                    ¥{{ scope.row.amount }}
                  </template>
                </el-table-column>
                <el-table-column prop="method" label="支付方式">
                  <template slot-scope="scope">
                    {{ getPaymentMethodText(scope.row.method) }}
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态">
                  <template slot-scope="scope">
                    <el-tag :type="getPaymentStatusType(scope.row.status)">
                      {{ getPaymentStatusText(scope.row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="createTime" label="支付时间" width="180">
                  <template slot-scope="scope">
                    {{ formatDateTime(scope.row.createTime) }}
                  </template>
                </el-table-column>
              </el-table>
              <div class="empty-payment" v-if="paymentRecords.length === 0 && !paymentsLoading">
                <el-empty description="暂无支付记录"></el-empty>
              </div>
            </el-col>
          </el-row>
        </div>
        
        <!-- 加载中 -->
        <div v-else class="loading-skeleton">
          <el-skeleton :rows="10" animated />
        </div>
        
        <!-- 底部按钮 -->
        <div class="order-footer">
          <el-button @click="goBack">返回</el-button>
          <el-button type="primary" @click="goToOrderList">返回订单列表</el-button>
        </div>
      </div>
    </div>
    <Footer />
  </div>
</template>

<script>
import Header from './components/Header'
import Footer from './components/Footer'
import { getOrderByOrderNo, cancelOrder, getPaymentsByOrderId } from '../api/order'
import { getHouseDetail } from './api/house'
import { getFrontendUser } from '../utils/auth'
import { OrderStatusUtils, ORDER_STATUS } from '../constants/orderStatus'
import moment from 'moment'

export default {
  name: 'OrderDetail',
  components: {
    Header,
    Footer
  },
  data() {
    return {
      orderNo: '',
      order: null,
      house: null,
      userInfo: null,
      loading: false,
      paymentsLoading: false,
      paymentRecords: [],
      ORDER_STATUS // 添加状态常量供模板使用
    }
  },
  computed: {
    // 获取当前步骤
    getStepActive() {
      if (!this.order) return 0
      return OrderStatusUtils.getStepIndex(this.order.status)
    },
    
    // 获取状态类型
    getStatusType() {
      if (!this.order) return ''
      return OrderStatusUtils.getStatusType(this.order.status)
    },
    
    // 获取状态文本
    getStatusText() {
      if (!this.order) return ''
      return OrderStatusUtils.getStatusText(this.order.status)
    },
    
    // 获取状态描述
    getStatusDesc() {
      if (!this.order) return ''
      return OrderStatusUtils.getStatusDesc(this.order.status)
    },
    
    // 获取状态图标
    getStatusIcon() {
      if (!this.order) return ''
      return OrderStatusUtils.getStatusIcon(this.order.status)
    },
    
    // 获取支付方式
    getPaymentMethod() {
      if (!this.order || !this.order.depositPayTime) return '未支付'
      
      // 模拟数据，实际项目中应从订单信息中获取
      return '支付宝'
    }
  },
  created() {
    this.orderNo = this.$route.params.orderNo
    if (!this.orderNo) {
      this.$message.error('订单号不存在')
      this.$router.push('/frontend/orderlist')
      return
    }
    
    // 获取用户信息
    this.userInfo = getFrontendUser()
    console.log('获取到的用户信息:', this.userInfo)
    if (!this.userInfo || !this.userInfo.token) {
      this.$message.warning('请先登录')
      this.$router.push('/frontend/login?redirect=/frontend/orderdetail/' + this.orderNo)
      return
    }
    
    // 获取订单详情
    this.fetchOrderDetail()
  },
  methods: {
    // 获取订单详情
    async fetchOrderDetail() {
      this.loading = true
      
      try {
        console.log('正在获取订单信息，订单号:', this.orderNo)
        const res = await getOrderByOrderNo(this.orderNo)
        console.log('订单API响应:', res)

        if (res.data && res.data.flag) {
          this.order = res.data.data
          console.log('获取到订单数据:', this.order)

          // 检查用户权限
          const userId = this.userInfo.userInfo ? this.userInfo.userInfo.id : this.userInfo.id
          console.log('用户ID:', userId, '租客ID:', this.order.tenantId, '房东ID:', this.order.ownerId)
          if (userId !== this.order.tenantId && userId !== this.order.ownerId) {
            this.$message.warning('您无权查看此订单')
            this.$router.push('/frontend/orderlist')
            return
          }

          // 获取房源详情
          await this.fetchHouseDetail(this.order.houseId)

          // 获取支付记录
          this.fetchPaymentRecords()
        } else {
          console.error('API返回失败:', res.data)
          this.$message.error('获取订单信息失败: ' + (res.data ? res.data.message : '未知错误'))
        }
      } catch (error) {
        console.error('获取订单信息失败:', error)
        this.$message.error('获取订单信息失败: ' + (error.response ? error.response.data.message : error.message))
      } finally {
        this.loading = false
      }
    },
    
    // 获取房源详情
    async fetchHouseDetail(houseId) {
      try {
        const res = await getHouseDetail(houseId)
        if (res.data && res.data.flag) {
          this.house = res.data.data
        }
      } catch (error) {
        console.error('获取房源信息失败:', error)
      }
    },
    
    // 获取支付记录
    async fetchPaymentRecords() {
      this.paymentsLoading = true

      // 检查订单是否存在
      if (!this.order) {
        this.paymentsLoading = false
        return
      }

      try {
        // 调用API获取真实的支付记录
        const res = await getPaymentsByOrderId(this.order.id)

        if (res.data && res.data.flag) {
          this.paymentRecords = res.data.data || []
          console.log('获取到支付记录:', this.paymentRecords)
        } else {
          console.error('获取支付记录失败:', res.data)
          this.paymentRecords = []
        }
      } catch (error) {
        console.error('获取支付记录失败:', error)
        this.paymentRecords = []
        this.$message.error('获取支付记录失败')
      } finally {
        this.paymentsLoading = false
      }
    },
    
    // 格式化日期
    formatDate(dateStr) {
      return moment(dateStr).format('YYYY-MM-DD')
    },
    
    // 格式化日期时间
    formatDateTime(dateStr) {
      return moment(dateStr).format('YYYY-MM-DD HH:mm:ss')
    },
    
    // 获取支付类型文本
    getPaymentTypeText(type) {
      const typeMap = {
        'deposit': '押金',
        'rent': '租金',
        'service': '服务费',
        'other': '其他'
      }
      return typeMap[type] || type
    },
    
    // 获取支付方式文本
    getPaymentMethodText(method) {
      const methodMap = {
        'alipay': '支付宝',
        'wechat': '微信',
        'bank': '银行卡',
        'other': '其他'
      }
      return methodMap[method] || method
    },
    
    // 获取支付状态类型
    getPaymentStatusType(status) {
      const statusMap = {
        'pending': 'warning',
        'success': 'success',
        'failed': 'danger',
        'refunded': 'info'
      }
      return statusMap[status] || 'info'
    },
    
    // 获取支付状态文本
    getPaymentStatusText(status) {
      const statusMap = {
        'pending': '待支付',
        'success': '支付成功',
        'failed': '支付失败',
        'refunded': '已退款'
      }
      return statusMap[status] || status
    },
    
    // 前往支付页面
    goToPay() {
      this.$router.push('/frontend/orderpay/' + this.orderNo)
    },
    
    // 查看合同
    goToContract() {
      this.$router.push({
        path: `/frontend/contract/${this.order.orderNo}`
      })
    },
    
    // 去评价页面
    goToReview() {
      if (!this.order) {
        this.$message.error('订单信息不存在')
        return
      }
      this.$router.push({
        path: `/frontend/review/${this.order.id}`
      })
    },

    // 支付租金
    payRent() {
      if (!this.order) {
        this.$message.error('订单信息不存在')
        return
      }
      this.$router.push({
        path: `/frontend/orderpay/${this.order.orderNo}`,
        query: { type: 'rent' }
      })
    },
    
    // 查看房源详情
    viewHouseDetail() {
      if (!this.house) {
        this.$message.error('房源信息不存在')
        return
      }
      this.$router.push('/frontend/housedetail/' + this.house.id)
    },
    
    // 取消订单
    async cancelOrder() {
      try {
        await this.$confirm('确定要取消订单吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const res = await cancelOrder(this.orderNo)
        if (res.data && res.data.flag) {
          this.$message.success('订单已取消')
          // 重新获取订单信息
          this.fetchOrderDetail()
        } else {
          this.$message.error(res.data.message || '取消订单失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('取消订单失败:', error)
          this.$message.error('取消订单失败')
        }
      }
    },
    
    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },
    
    // 返回订单列表
    goToOrderList() {
      this.$router.push('/frontend/orderlist')
    }
  }
}
</script>

<style scoped>
.order-detail-page {
  background-color: #f8f8f8;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.container {
  flex: 1;
  max-width: 1200px;
  margin: 30px auto;
  padding: 0 20px;
}

.order-detail-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 30px;
}

h2 {
  font-size: 24px;
  color: #333;
  margin-bottom: 20px;
}

h3 {
  font-size: 18px;
  color: #333;
  margin: 20px 0 15px;
  border-left: 4px solid #409EFF;
  padding-left: 10px;
}

h4 {
  font-size: 16px;
  margin: 0 0 10px;
}

.order-progress {
  margin-bottom: 30px;
}

.section {
  margin-top: 30px;
}

.status-card {
  margin-top: 20px;
  padding: 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
}

.status-unpaid {
  background-color: #fdf6ec;
  border: 1px solid #faecd8;
}

.status-pending {
  background-color: #ecf5ff;
  border: 1px solid #d9ecff;
}

.status-renting {
  background-color: #f0f9eb;
  border: 1px solid #e1f3d8;
}

.status-completed {
  background-color: #f4f4f5;
  border: 1px solid #e9e9eb;
}

.status-cancelled {
  background-color: #fef0f0;
  border: 1px solid #fde2e2;
}

.status-icon {
  font-size: 40px;
  margin-right: 20px;
}

.status-unpaid .status-icon {
  color: #e6a23c;
}

.status-pending .status-icon {
  color: #409eff;
}

.status-renting .status-icon {
  color: #67c23a;
}

.status-completed .status-icon {
  color: #909399;
}

.status-cancelled .status-icon {
  color: #f56c6c;
}

.status-info {
  flex: 1;
}

.status-info h3 {
  margin: 0 0 10px;
  border-left: none;
  padding-left: 0;
}

.status-info p {
  color: #606266;
  margin-bottom: 15px;
}

.status-actions {
  margin-top: 15px;
}

.house-card {
  display: flex;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 20px;
  margin-top: 15px;
}

.house-image {
  width: 180px;
  height: 120px;
  overflow: hidden;
  border-radius: 6px;
  margin-right: 20px;
}

.house-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.house-details {
  flex: 1;
}

.address {
  color: #909399;
  margin-bottom: 10px;
}

.specs {
  color: #606266;
  margin-bottom: 10px;
}

.price {
  color: #f56c6c;
  font-weight: bold;
  margin-bottom: 15px;
}

.total-amount {
  font-size: 16px;
  font-weight: bold;
  color: #f56c6c;
}

.empty-payment {
  margin: 30px 0;
}

.order-footer {
  margin-top: 40px;
  text-align: center;
}

.loading-skeleton {
  padding: 20px 0;
}
</style> 