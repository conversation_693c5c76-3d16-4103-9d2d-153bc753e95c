<template>
  <div class="application-list-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>订单列表</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          @click="handleRefresh"
        >
          <i class="el-icon-refresh"></i> 刷新
        </el-button>
      </div>

      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-form :inline="true" :model="searchForm" class="demo-form-inline">
          <el-form-item label="申请编号">
            <el-input v-model="searchForm.orderNo" placeholder="申请编号" clearable></el-input>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="状态" clearable>
              <el-option label="待审核" :value="ORDER_STATUS.APPLICATION"></el-option>
              <el-option label="申请已批准" :value="ORDER_STATUS.APPROVED"></el-option>
              <el-option label="待签署合同" :value="ORDER_STATUS.CONTRACT_PENDING"></el-option>
              <el-option label="待支付押金" :value="ORDER_STATUS.UNPAID"></el-option>
              <el-option label="已拒绝" :value="ORDER_STATUS.CANCELLED"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格 -->
      <el-table
        v-loading="loading"
        :data="applicationList"
        border
        style="width: 100%"
      >
        <el-table-column prop="orderNo" label="申请编号" width="180"></el-table-column>
        <el-table-column prop="houseTitle" label="房源信息" min-width="200">
          <template slot-scope="scope">
            <div class="house-info">
              <el-image
                style="width: 60px; height: 60px"
                :src="require('../assets/showcase.jpg')"
                fit="cover"
              ></el-image>
              <div class="house-details">
                <div class="house-title">{{ scope.row.houseAddress || '房源信息' }}</div>
                <div class="house-address">{{ scope.row.houseArea || '' }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="tenantName" label="申请人" width="120"></el-table-column>
        <el-table-column prop="duration" label="租期" width="100">
          <template slot-scope="scope">{{ scope.row.duration }}个月</template>
        </el-table-column>
        <el-table-column prop="createTime" label="申请时间" width="160">
          <template slot-scope="scope">{{ formatDateTime(scope.row.createTime) }}</template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)">{{ getStatusText(scope.row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleView(scope.row)"
              icon="el-icon-view"
            >查看</el-button>
            <el-button
              v-if="scope.row.status === ORDER_STATUS.APPLICATION"
              size="mini"
              type="success"
              @click="handleApprove(scope.row)"
              icon="el-icon-check"
            >通过</el-button>
            <el-button
              v-if="scope.row.status === ORDER_STATUS.APPLICATION"
              size="mini"
              type="danger"
              @click="handleReject(scope.row)"
              icon="el-icon-close"
            >拒绝</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
      </div>
    </el-card>

    <!-- 申请详情对话框 -->
    <el-dialog title="申请详情" :visible.sync="dialogVisible" width="50%">
      <div v-if="currentApplication" class="application-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="申请编号">{{ currentApplication.orderNo }}</el-descriptions-item>
          <el-descriptions-item label="申请状态">
            <el-tag :type="getStatusType(currentApplication.status)">{{ getStatusText(currentApplication.status) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="房源信息">{{ currentApplication.house ? currentApplication.house.title : '房源信息获取失败' }}</el-descriptions-item>
          <el-descriptions-item label="房源地址">{{ currentApplication.house ? currentApplication.house.address : '' }}</el-descriptions-item>
          <el-descriptions-item label="申请人">{{ currentApplication.tenantName }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ currentApplication.tenantPhone }}</el-descriptions-item>
          <el-descriptions-item label="租期">{{ currentApplication.startDate }} 至 {{ currentApplication.endDate }}</el-descriptions-item>
          <el-descriptions-item label="租期月数">{{ currentApplication.duration }}个月</el-descriptions-item>
          <el-descriptions-item label="月租金">¥{{ currentApplication.monthlyPrice }}</el-descriptions-item>
          <el-descriptions-item label="押金">¥{{ currentApplication.deposit }}</el-descriptions-item>
          <el-descriptions-item label="服务费">¥{{ currentApplication.serviceFee }}</el-descriptions-item>
          <el-descriptions-item label="总价">¥{{ currentApplication.totalPrice }}</el-descriptions-item>
          <el-descriptions-item label="申请时间">{{ formatDateTime(currentApplication.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="更新时间">{{ formatDateTime(currentApplication.updateTime) }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ currentApplication.remark || '无' }}</el-descriptions-item>
        </el-descriptions>

        <div class="dialog-footer" slot="footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button v-if="currentApplication.status === ORDER_STATUS.APPLICATION" type="success" @click="handleApprove(currentApplication)">通过申请</el-button>
          <el-button v-if="currentApplication.status === ORDER_STATUS.APPLICATION" type="danger" @click="handleReject(currentApplication)">拒绝申请</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getBackstageUser, isAdmin, isOwner } from '../utils/auth'
import { getImageUrl } from '../utils/imageUtils'
import { OrderStatusUtils, ORDER_STATUS } from '../constants/orderStatus'
import moment from 'moment'

export default {
  name: 'ApplicationList',
  data() {
    return {
      loading: false,
      applicationList: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      searchForm: {
        orderNo: '',
        status: ''
      },
      dialogVisible: false,
      currentApplication: null,
      userInfo: null,
      userId: null,
      ORDER_STATUS // 添加状态常量供模板使用
    }
  },
  created() {
    this.userInfo = getBackstageUser()
    if (this.userInfo && this.userInfo.userInfo) {
      this.userId = this.userInfo.userInfo.id
    }
    this.fetchApplicationList()
  },
  methods: {
    // 获取申请列表
    async fetchApplicationList() {
      this.loading = true
      try {
        // 构建查询参数
        const params = {
          page: this.currentPage,
          limit: this.pageSize
        }

        // 添加搜索条件
        if (this.searchForm.orderNo) {
          params.orderNo = this.searchForm.orderNo
        }
        if (this.searchForm.status) {
          params.status = this.searchForm.status
        }

        // 根据角色添加不同的参数
        if (isAdmin(false)) {
          // 管理员可以查看所有申请
        } else if (isOwner(false)) {
          // 房东只能查看自己的房源的申请
          params.ownerId = this.userId
        } else {
          this.$message.error('您没有权限查看此页面')
          return
        }

        // 调用API获取申请列表
        const res = await this.$http.get('/orders', { params })
        
        if (res.data && res.data.flag) {
          this.applicationList = res.data.data.list || []
          this.total = res.data.data.total || 0
          
          // 获取房源和租客信息
          await this.fetchRelatedInfo()
        } else {
          this.$message.error('获取租房申请列表失败')
        }
      } catch (error) {
        console.error('获取租房申请列表失败:', error)
        this.$message.error('获取租房申请列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 获取关联信息（房源和租客）
    async fetchRelatedInfo() {
      try {
        if (!this.applicationList || this.applicationList.length === 0) {
          return
        }
        
        // 不再预先获取每个房源的详细信息，只设置基本信息
        for (let i = 0; i < this.applicationList.length; i++) {
          const application = this.applicationList[i]
          
          // 设置租客名称和电话（如果订单接口返回了这些信息）
          if (application.tenantName) {
            this.$set(this.applicationList[i], 'tenantName', application.tenantName)
          }
          if (application.tenantPhone) {
            this.$set(this.applicationList[i], 'tenantPhone', application.tenantPhone)
          }
        }
      } catch (error) {
        console.error('获取关联信息失败:', error)
      }
    },
    
    // 处理查询
    handleSearch() {
      this.currentPage = 1
      this.fetchApplicationList()
    },
    
    // 重置查询
    resetSearch() {
      this.searchForm = {
        orderNo: '',
        status: ''
      }
      this.handleSearch()
    },
    
    // 刷新
    handleRefresh() {
      this.fetchApplicationList()
    },
    
    // 查看详情
    async handleView(row) {
      try {
        this.loading = true
        // 在点击查看按钮时才获取房源详情
        if (row.houseId) {
          const houseRes = await this.$http.get(`/houses/${row.houseId}`)
          if (houseRes.data && houseRes.data.flag) {
            this.$set(row, 'house', houseRes.data.data)
          }
        }
        
        this.currentApplication = { ...row }
        this.dialogVisible = true
      } catch (error) {
        console.error('获取房源详情失败:', error)
        this.$message.error('获取房源详情失败')
      } finally {
        this.loading = false
      }
    },
    
    // 通过申请
    async handleApprove(row) {
      try {
        await this.$confirm('确定要通过该租房申请吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'success'
        })
        
        // 调用API，将状态更新为"已批准"
        const res = await this.$http.put(`/orders/${row.orderNo}/approve`, {
          status: ORDER_STATUS.APPROVED,
          remark: '申请已通过，请签署合同'
        })
        
        if (res.data && res.data.flag) {
          this.$message.success('已通过租房申请')
          
          // 生成合同
          this.generateContract(row)
          
          this.dialogVisible = false
          this.fetchApplicationList()
        } else {
          this.$message.error(res.data.message || '操作失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('通过申请失败:', error)
          this.$message.error('通过申请失败')
        }
      }
    },

    // 添加生成合同的方法
    async generateContract(application) {
      try {
        // 计算合同起止日期和租期
        const startDate = application.startDate || new Date().toISOString().split('T')[0]
        const duration = application.duration || 12
        
        // 计算结束日期
        const endDate = new Date(startDate)
        endDate.setMonth(endDate.getMonth() + duration)
        const endDateStr = endDate.toISOString().split('T')[0]
        
        // 获取房源信息
        const houseRes = await this.$http.get(`/houses/${application.houseId}`)
        let address = ""
        if (houseRes.data && houseRes.data.flag) {
          address = houseRes.data.data.address || ""
        }
        
        // 构建合同数据
        const contractData = {
          orderNo: application.orderNo,
          address: address,
          startDate: startDate,
          endDate: endDateStr,
          duration: duration,
          monthlyRent: application.monthlyPrice || 0,
          deposit: application.deposit || 0,
          serviceFee: application.serviceFee || 0,
          totalAmount: application.totalPrice || 0,
          paymentMethod: 'monthly', // 默认月付
          otherTerms: '无'
        }
        
        // 调用API创建合同
        const res = await this.$http.post('/contracts', contractData)
        
        if (res.data && res.data.flag) {
          this.$message.success('合同已生成，请前往合同管理页面查看')
          
          // 更新订单状态为"待签署合同"
          await this.$http.put(`/orders/${application.orderNo}/status`, {}, {
            params: {
              status: ORDER_STATUS.CONTRACT_PENDING
            }
          })
          
          // 发送消息通知租客签署合同
          await this.$http.post('/messages', {
            userId: application.tenantId,
            title: '租房申请已通过，请签署合同',
            content: `您的租房申请(${application.orderNo})已通过审核，请前往"我的订单"页面查看并签署合同。`,
            type: 'contract',
            relatedId: application.orderNo
          })
          
          // 刷新申请列表
          this.fetchApplicationList()
        } else {
          this.$message.error(res.data && res.data.message ? res.data.message : '合同生成失败')
        }
      } catch (error) {
        console.error('生成合同失败:', error)
        this.$message.error('生成合同失败：' + (error.message || '未知错误'))
      }
    },
    
    // 拒绝申请
    async handleReject(row) {
      try {
        const { value } = await this.$prompt('请输入拒绝理由', '拒绝申请', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPlaceholder: '请输入拒绝理由'
        })
        
        // 调用API
        const res = await this.$http.put(`/orders/${row.orderNo}/reject`, {
          status: ORDER_STATUS.CANCELLED,
          remark: value || '申请已被拒绝'
        })
        
        if (res.data && res.data.flag) {
          this.$message.success('已拒绝租房申请')
          this.dialogVisible = false
          this.fetchApplicationList()
        } else {
          this.$message.error(res.data.message || '操作失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('拒绝申请失败:', error)
          this.$message.error('拒绝申请失败')
        }
      }
    },
    
    // 获取房屋图片
    getHouseImage(house) {
      if (!house) return require('../assets/showcase.jpg')
      
      if (house.imageUrl) {
        return getImageUrl(house.imageUrl)
      }
      
      return getImageUrl('/img/showcase.jpg')
    },
    
    // 格式化日期时间
    formatDateTime(dateStr) {
      return moment(dateStr).format('YYYY-MM-DD HH:mm')
    },
    
    // 获取状态类型
    getStatusType(status) {
      return OrderStatusUtils.getStatusType(status)
    },

    // 获取状态文本
    getStatusText(status) {
      const text = OrderStatusUtils.getStatusText(status)
      // 后台显示特殊处理：cancelled状态显示为"已拒绝"
      return status === ORDER_STATUS.CANCELLED ? '已拒绝' : text
    },
    
    // 处理页大小变化
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchApplicationList()
    },
    
    // 处理页码变化
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchApplicationList()
    }
  }
}
</script>

<style scoped>
.application-list-container {
  padding: 20px;
}

.search-bar {
  margin-bottom: 20px;
}

.house-info {
  display: flex;
  align-items: center;
}

.house-details {
  margin-left: 10px;
  overflow: hidden;
}

.house-title {
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.house-address {
  color: #909399;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.application-detail {
  padding: 20px 0;
}

.dialog-footer {
  margin-top: 20px;
  text-align: right;
}
</style> 