package com.house.service;

import com.house.pojo.ChatMessage;

import java.util.List;

/**
 * 聊天消息Service接口
 */
public interface ChatMessageService {
    /**
     * 保存聊天消息
     * @param chatMessage 聊天消息
     * @return 是否成功
     */
    boolean saveChatMessage(ChatMessage chatMessage);
    
    /**
     * 根据房源ID和用户ID获取聊天记录
     * @param houseId 房源ID
     * @param userId 用户ID
     * @param landlordId 房东ID
     * @return 聊天记录列表
     */
    List<ChatMessage> getChatHistory(Long houseId, Long userId, Long landlordId);
    
    /**
     * 将消息标记为已读
     * @param toUserId 接收者ID
     * @param fromUserId 发送者ID
     * @return 是否成功
     */
    boolean markMessagesAsRead(Long toUserId, Long fromUserId);
    
    /**
     * 获取用户未读消息数量
     * @param userId 用户ID
     * @return 未读消息数量
     */
    int getUnreadMessageCount(Long userId);
    
    /**
     * 获取房东的所有聊天消息
     * @param landlordId 房东ID
     * @return 聊天消息列表
     */
    List<ChatMessage> getLandlordMessages(Long landlordId);
    
    /**
     * 删除聊天记录
     * @param houseId 房源ID
     * @param userId 用户ID
     * @param landlordId 房东ID
     * @return 是否成功
     */
    boolean deleteChatHistory(Long houseId, Long userId, Long landlordId);
} 