<template>
  <el-header class="header">
    <div class="header-left">
      <i class="el-icon-s-fold toggle-button" @click="$emit('toggle-sidebar')"></i>
      <breadcrumb />
    </div>
    <div class="header-right">
      <el-dropdown trigger="click" @command="handleCommand">
        <span class="user-profile">
          <img :src="userAvatar" class="user-avatar" />
          <span>{{ userName }}</span>
          <i class="el-icon-caret-bottom"></i>
        </span>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="profile">个人信息</el-dropdown-item>
          <el-dropdown-item command="logout">退出登录</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </el-header>
</template>

<script>
import { getBackstageUser, removeBackstageUser } from '@/utils/auth'
import { getImageUrl } from '@/utils/imageUtils'
import Breadcrumb from './Breadcrumb'
import EventBus from '@/utils/eventBus'

export default {
  name: 'HeaderComponent',
  components: {
    Breadcrumb
  },
  data() {
    return {
      userName: '',
      userAvatar: ''
    }
  },
  created() {
    this.getUserInfo()
    
    // 监听头像更新事件
    EventBus.$on('avatar-updated', this.updateAvatar)
  },
  beforeDestroy() {
    // 组件销毁前移除事件监听，避免内存泄漏
    EventBus.$off('avatar-updated', this.updateAvatar)
  },
  methods: {
    getUserInfo() {
      const userData = getBackstageUser()
      if (userData && userData.userInfo) {
        // 获取用户名，优先使用name，如果没有则使用username
        this.userName = userData.userInfo.name || userData.userInfo.username || '用户'
        
        // 处理头像路径
        if (userData.userInfo.avatar) {
          this.userAvatar = getImageUrl(userData.userInfo.avatar)
        } else {
          // 如果没有头像，使用默认头像
          this.userAvatar = require('@/assets/showcase.jpg')
        }
      }
    },
    // 更新头像方法
    updateAvatar(avatarPath) {
      console.log('Header组件接收到头像更新事件:', avatarPath)
      this.userAvatar = getImageUrl(avatarPath)
    },
    handleCommand(command) {
      if (command === 'profile') {
        this.$router.push('/backstage/profile')
      } else if (command === 'logout') {
        this.logout()
      }
    },
    logout() {
      // 确认提示
      this.$confirm('确定要退出登录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 清除用户信息
        removeBackstageUser()
        // 跳转到后台登录页
        this.$router.push('/backstage/login')
        // 提示
        this.$message.success('已退出登录')
      }).catch(() => {
        // 取消退出，不做操作
      })
    }
  }
}
</script>

<style scoped>
.header {
  background-color: #fff;
  color: #333;
  line-height: 60px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  padding: 0 20px;
  height: 60px;
  position: relative;
  z-index: 9;
  width: 100%;
  box-sizing: border-box;
}

.header-left {
  display: flex;
  align-items: center;
}

.toggle-button {
  font-size: 20px;
  cursor: pointer;
  margin-right: 20px;
  color: #5a5e66;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-profile {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0 8px;
  height: 50px;
  transition: all 0.3s;
  border-radius: 4px;
}

.user-profile:hover {
  background: rgba(0, 0, 0, 0.025);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 8px;
  object-fit: cover;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.1);
}
</style> 