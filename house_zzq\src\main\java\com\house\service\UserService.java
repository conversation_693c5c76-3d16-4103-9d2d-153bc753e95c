package com.house.service;

import java.util.List;


import com.house.dao.UserDao;
import com.house.dao.UserListDao;
import com.house.dto.UserExecution;
import com.house.exception.UserOperationException;
import com.house.pojo.User;
import com.house.pojo.UserList;
import com.house.vo.PasswordVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Slf4j
@Service
public class UserService {

	@Autowired
	private UserListDao userListDao;

	@Autowired
	private UserDao userDao;
	
	/**
	 * 获取UserDao
	 * @return UserDao
	 */
	public UserDao getUserDao() {
		return userDao;
	}
	
	/**
	 * 获取UserListDao
	 * @return UserListDao
	 */
	public UserListDao getUserListDao() {
		return userListDao;
	}

	/**
	 * 根据用户名查询用户
	 * @param username 用户名
	 * @return 用户对象，不存在则返回null
	 */
	public User findByUsername(String username) {
		log.info("【用户服务】根据用户名查询用户: {}", username);
		return userDao.selectByUsername(username);
	}
	
	/**
	 * 根据User表的ID查询用户详细信息
	 * @param userId User表的ID（对应UserList表的userId字段）
	 * @return 用户详细信息对象，不存在则返回null
	 */
	public UserList findUserInfoById(Integer userId) {
		log.info("【用户服务】根据User表ID查询用户详细信息: {}", userId);
		// 第二个参数userId对应UserList表的userid字段，用于关联User表的id
		return userListDao.selectUserInfoByCondition(null, userId, null);
	}

	public User login(String username, String password) {
		log.info("【登录服务】正在验证用户: {}", username);
		
		// 对密码进行脱敏处理后记录
		String maskedPassword = password.replaceAll(".", "*");
		log.info("【登录服务】用户: {}, 密码: {}", username, maskedPassword);
		
		User user = userDao.selectByUsernameAndPassword(username, password);
		
		if (user == null) {
			log.warn("【登录服务】用户验证失败，用户名或密码错误: {}", username);
			return null;
		}
		
		log.info("【登录服务】用户验证成功: {}, 用户ID: {}", username, user.getId());
		return user;
	}
	
	public List<UserList> findUserListByCondition(String name, Integer id) {

		return userListDao.selectUserListByCondition(name,id);
	}

	public UserList findUserInfoByCondition(String name, Integer userId,Integer id) {

		return userListDao.selectUserInfoByCondition(name,userId,id);
	}

	public UserExecution updatePassword(PasswordVO passwordVO){
		if(passwordVO == null){
			return new UserExecution(false,"修改的密码信息为空");
		}
		UserList userList = userListDao.selectUserInfoByCondition(null,null,passwordVO.getUserId());
		Integer userId = userList.getUserId();
		User user = userDao.selectUserById(userId);
		if(!passwordVO.getOldPassword().equals(user.getPassword())){
			return new UserExecution(false,"旧密码错误");
		}
		user.setPassword(passwordVO.getNewPassword());
		try{
			int effectedNum = userDao.updateUser(user);
			if(effectedNum < 1){
				return new UserExecution(false,"修改密码失败");
			}
		}catch (Exception e){
			throw new UserOperationException(e.toString());
		}
		return new UserExecution(true);
	}

	@Transactional
	public UserExecution addUserListAndUserAccount(UserList userList) {
		if(userList == null){
			return new UserExecution(false,"添加用户信息为空");
		}
		User user = new User();
		user.setUsername(userList.getPhone());
		user.setPassword(userList.getPhone());
		user.setType(userList.getType());
		try{
			int effectedNum = userDao.insertUser(user);
			if(effectedNum < 1){
				return new UserExecution(false,"添加用户帐号失败");
			}
		}catch (Exception e){
			throw new UserOperationException(e.toString());
		}
		int accountId = user.getId();
		userList.setUserId(accountId);
		try{
			int effectedNum = userListDao.insertUserList(userList);
			if(effectedNum < 1){
				return new UserExecution(false,"添加用户信息失败");
			}
		}catch (Exception e){
			throw new UserOperationException(e.toString());
		}
		return new UserExecution(true);
	}


	public UserExecution updateUserList(UserList userList) {
        if(userList == null){
            return new UserExecution(false,"更新用户信息为空");
        }

        try{
            int effectedNum = userListDao.updateUserList(userList);
            if(effectedNum < 1){
                return new UserExecution(false,"更新用户信息失败");
            }
        }catch (Exception e){
            throw new UserOperationException(e.toString());
        }
        return new UserExecution(true);
	}

    @Transactional
    public UserExecution deleteUser(Integer userListId) {
        if(userListId == null){
            return new UserExecution(false,"删除用户信息Id为空");
        }
        UserList userList = userListDao.findUserListById(userListId);
        Integer userAccountId = userList.getUserId();
        try{
            int effectedNum = userDao.deleteUser(userAccountId);
            if(effectedNum < 1){
                return new UserExecution(false,"删除用户帐号失败");
            }
        }catch (Exception e){
            throw new UserOperationException(e.toString());
        }

        try{
            int effectedNum = userListDao.deleteUserList(userListId);
            if(effectedNum < 1){
                return new UserExecution(false,"删除用户信息失败");
            }
        }catch (Exception e){
            throw new UserOperationException(e.toString());
        }
        return new UserExecution(true);
    }



}
