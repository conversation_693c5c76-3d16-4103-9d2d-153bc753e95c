<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.house.mapper.ContractMapper">

    <resultMap id="BaseResultMap" type="com.house.pojo.Contract">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="contract_no" property="contractNo" jdbcType="VARCHAR"/>
        <result column="order_id" property="orderId" jdbcType="INTEGER"/>
        <result column="content" property="content" jdbcType="LONGVARCHAR"/>
        <result column="tenant_id" property="tenantId" jdbcType="INTEGER"/>
        <result column="owner_id" property="ownerId" jdbcType="INTEGER"/>
        <result column="tenant_signed" property="tenantSigned" jdbcType="BOOLEAN"/>
        <result column="tenant_sign_time" property="tenantSignTime" jdbcType="TIMESTAMP"/>
        <result column="tenant_signature" property="tenantSignature" jdbcType="VARCHAR"/>
        <result column="owner_signed" property="ownerSigned" jdbcType="BOOLEAN"/>
        <result column="owner_sign_time" property="ownerSignTime" jdbcType="TIMESTAMP"/>
        <result column="owner_signature" property="ownerSignature" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, contract_no, order_id, content, tenant_id, owner_id, tenant_signed, tenant_sign_time, tenant_signature, 
        owner_signed, owner_sign_time, owner_signature, status, create_time, update_time
    </sql>

    <insert id="insert" parameterType="com.house.pojo.Contract" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tb_contract(contract_no, order_id, content, tenant_id, owner_id, tenant_signed, owner_signed, 
            tenant_signature, owner_signature, status, create_time, update_time)
        VALUES(#{contractNo}, #{orderId}, #{content}, #{tenantId}, #{ownerId}, #{tenantSigned}, #{ownerSigned}, 
            #{tenantSignature}, #{ownerSignature}, #{status}, #{createTime}, #{updateTime})
    </insert>
    
    <select id="selectById" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/> 
        FROM tb_contract 
        WHERE id = #{id}
    </select>
    
    <select id="selectByContractNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_contract
        WHERE contract_no = #{contractNo}
    </select>
    
    <select id="selectByOrderId" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_contract
        WHERE order_id = #{orderId}
    </select>
    
    <update id="updateById" parameterType="com.house.pojo.Contract">
        UPDATE tb_contract 
        SET content = #{content},
            tenant_signed = #{tenantSigned},
            tenant_sign_time = #{tenantSignTime},
            tenant_signature = #{tenantSignature},
            owner_signed = #{ownerSigned},
            owner_sign_time = #{ownerSignTime},
            owner_signature = #{ownerSignature},
            status = #{status},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>
    
    <update id="updateStatus">
        UPDATE tb_contract
        SET status = #{status},
            update_time = NOW()
        WHERE id = #{id}
    </update>
    
    <update id="signByTenant">
        UPDATE tb_contract
        SET tenant_signed = 1,
            tenant_sign_time = #{time},
            update_time = NOW()
        WHERE id = #{id} AND tenant_id = #{tenantId}
    </update>
    
    <update id="signByOwner">
        UPDATE tb_contract
        SET owner_signed = 1,
            owner_sign_time = #{time},
            update_time = NOW()
        WHERE id = #{id} AND owner_id = #{ownerId}
    </update>
    
    <select id="selectList" parameterType="java.util.Map" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM tb_contract
        <where>
            <if test="contractNo != null and contractNo != ''">
                AND contract_no = #{contractNo}
            </if>
            <if test="orderId != null">
                AND order_id = #{orderId}
            </if>
            <if test="tenantId != null">
                AND tenant_id = #{tenantId}
            </if>
            <if test="ownerId != null">
                AND owner_id = #{ownerId}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="tenantSigned != null">
                AND tenant_signed = #{tenantSigned}
            </if>
            <if test="ownerSigned != null">
                AND owner_signed = #{ownerSigned}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
    
    <select id="selectCount" parameterType="java.util.Map" resultType="int">
        SELECT COUNT(*)
        FROM tb_contract
        <where>
            <if test="contractNo != null and contractNo != ''">
                AND contract_no = #{contractNo}
            </if>
            <if test="orderId != null">
                AND order_id = #{orderId}
            </if>
            <if test="tenantId != null">
                AND tenant_id = #{tenantId}
            </if>
            <if test="ownerId != null">
                AND owner_id = #{ownerId}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="tenantSigned != null">
                AND tenant_signed = #{tenantSigned}
            </if>
            <if test="ownerSigned != null">
                AND owner_signed = #{ownerSigned}
            </if>
        </where>
    </select>

</mapper> 