package com.house.config;

import com.house.interceptor.JwtInterceptor;
import com.house.interceptor.LoggingInterceptor;
import com.house.interceptor.RequestResponseLoggingInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.io.File;

/**
 * Web MVC配置
 */
@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired
    private LoggingInterceptor loggingInterceptor;
    
    @Autowired
    private RequestResponseLoggingInterceptor requestResponseLoggingInterceptor;
    
    @Autowired
    private JwtInterceptor jwtInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册日志拦截器，拦截所有请求
        registry.addInterceptor(loggingInterceptor)
                .addPathPatterns("/**")
                .order(1);
                
        // 注册请求响应日志拦截器，拦截所有请求
        registry.addInterceptor(requestResponseLoggingInterceptor)
                .addPathPatterns("/**")
                .order(2);
                
        // 注册JWT拦截器，拦截所有请求
        // 但明确排除不需要验证token的路径
        registry.addInterceptor(jwtInterceptor)
                .addPathPatterns("/**") // 拦截所有请求
                .excludePathPatterns(   // 排除不需要验证的路径
                    "/user/login",              // 登录
                    "/user/register",           // 注册
                    "/api/images/**",           // 图片资源
                    "/static/**",               // 静态资源
                    "/favicon.ico",             // 网站图标
                    "/error",                   // 错误页面
                    "/house/gethothouses",      // 获取热门房源（首页公开接口）
                    "/house/getrecommendhouses", // 获取推荐房源（首页公开接口）
                    "/house/getallhouselist"    // 获取所有房源列表（公开接口）
                )
                .order(3);  // 确保JWT拦截器在最后执行
        
        System.out.println("拦截器配置完成，JWT拦截器已注册");
    }

    @Override
    public void configurePathMatch(PathMatchConfigurer configurer) {
        // 启用后缀模式匹配，确保控制器能处理请求
        configurer.setUseTrailingSlashMatch(true);
    }
    
    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 添加外部图片资源映射
        String uploadDir = "file:" + System.getProperty("user.dir") + File.separator + "upload" + File.separator;
        registry.addResourceHandler("/api/images/**")
                .addResourceLocations(uploadDir);
                
        // 添加静态资源映射
        registry.addResourceHandler("/static/**")
                .addResourceLocations("classpath:/static/");
        
        System.out.println("资源处理器配置完成，静态资源映射已注册");
    }
} 