<template>
  <div class="profile">
    <div class="page-header">
      <h2>个人信息</h2>
    </div>

    <el-card class="profile-form" shadow="hover">
      <div slot="header">
        <span>编辑个人资料</span>
      </div>
      
      <el-form :model="userForm" :rules="rules" ref="userForm" label-width="100px" size="medium">
        <!-- 头像显示和上传 -->
        <div class="avatar-wrapper">
          <div class="avatar-container">
            <img :src="avatarUrl" alt="用户头像" class="user-avatar"/>
          </div>
          <el-upload
            class="avatar-uploader"
            action="http://localhost:9002/file/upload"
            :headers="uploadHeaders"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload">
            <el-button size="small" type="primary" icon="el-icon-camera" class="upload-btn">更换头像</el-button>
          </el-upload>
        </div>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="姓名" prop="name">
              <el-input v-model="userForm.name" prefix-icon="el-icon-user"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="身份证号" prop="idCard">
              <el-input v-model="userForm.idCard" prefix-icon="el-icon-document"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="userForm.phone" prefix-icon="el-icon-mobile-phone"></el-input>
        </el-form-item>
        
        <el-form-item label="性别" prop="gender">
          <el-radio-group v-model="userForm.gender">
            <el-radio label="男">男</el-radio>
            <el-radio label="女">女</el-radio>
          </el-radio-group>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" icon="el-icon-check" @click="submitForm('userForm')">保存修改</el-button>
          <el-button icon="el-icon-refresh-left" @click="resetForm('userForm')">重置</el-button>
          <el-button type="warning" icon="el-icon-lock" @click="showEditPasswordDialog">修改密码</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 修改密码对话框 -->
    <el-dialog title="修改密码" :visible.sync="passwordDialogVisible" width="30%">
      <el-form :model="passwordForm" :rules="passwordRules" ref="passwordForm" label-width="100px">
        <el-form-item label="原密码" prop="oldPassword">
          <el-input type="password" v-model="passwordForm.oldPassword" show-password></el-input>
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input type="password" v-model="passwordForm.newPassword" show-password></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input type="password" v-model="passwordForm.confirmPassword" show-password></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="passwordDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitPasswordForm('passwordForm')">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getBackstageUser, saveBackstageUser } from '../utils/auth'
import { getImageUrl } from '../utils/imageUtils'
import userApi from '../api/user'
import EventBus from '../utils/eventBus'

export default {
  name: 'Profile',
  data() {
    // 验证密码是否一致
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    
    // 获取用户token
    const user = getBackstageUser();
    const token = user && user.token ? user.token : '';
    
    return {
      defaultAvatar: require('@/assets/showcase.jpg'),
      userInfo: {},
      userForm: {
        id: '',
        name: '',
        idCard: '',
        phone: '',
        gender: '男',
        avatar: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        idCard: [
          { required: true, message: '请输入身份证号', trigger: 'blur' },
          { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入正确的身份证号', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ]
      },
      passwordDialogVisible: false,
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      passwordRules: {
        oldPassword: [
          { required: true, message: '请输入原密码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能小于6位', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请再次输入密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      },
      uploadHeaders: {
        'Authorization': 'Bearer ' + token
      }
    }
  },
  computed: {
    avatarUrl() {
      // 记录原始头像路径用于调试
      const originalAvatar = this.userInfo.avatar;
      console.log('原始头像路径:', originalAvatar);
      
      if (!originalAvatar) {
        return this.defaultAvatar;
      }
      
      // 使用imageUtils处理头像路径
      const processedAvatar = getImageUrl(originalAvatar);
      console.log('处理后的头像路径:', processedAvatar);
      
      return processedAvatar;
    }
  },
  created() {
    this.getUserInfo()
  },
  methods: {
    getUserInfo() {
      const user = getBackstageUser()
      if (user && user.userInfo && user.userInfo.id) {
        userApi.getUserInfoByCondition({id: user.userInfo.id}).then(res => {
          if (res.data && res.data.flag) {
            this.userInfo = res.data.data
            // 填充表单
            this.userForm = {
              id: this.userInfo.id,
              name: this.userInfo.name || '',
              idCard: this.userInfo.idCard || '',
              phone: this.userInfo.phone || '',
              gender: this.userInfo.gender || '男',
              avatar: this.userInfo.avatar || ''
            }
          } else {
            this.$message.error('获取用户信息失败')
          }
        }).catch(() => {
          this.$message.error('获取用户信息失败')
        })
      }
    },
    handleAvatarSuccess(res) {
      if (res.flag) {
        // 获取上传成功后的文件路径
        const avatarUrl = res.data || '';
        console.log('上传成功，获取到的头像路径：', avatarUrl);
        
        // 确保头像路径格式正确 - 如果返回的不是以/api/images/开头的路径，进行转换
        let formattedAvatarUrl = avatarUrl;
        if (avatarUrl && !avatarUrl.startsWith('/api/images/') && !avatarUrl.startsWith('http')) {
          // 如果是文件名，则添加前缀
          if (!avatarUrl.includes('/')) {
            formattedAvatarUrl = `/api/images/${avatarUrl}`;
          } 
          // 如果是/images/开头，则替换为/api/images/
          else if (avatarUrl.startsWith('/images/')) {
            formattedAvatarUrl = `/api/images/${avatarUrl.substring(8)}`;
          }
        }
        
        console.log('格式化后的头像路径：', formattedAvatarUrl);
        
        // 将头像路径保存到用户信息中
        this.userInfo.avatar = formattedAvatarUrl;
        this.userForm.avatar = formattedAvatarUrl;
        
        // 更新用户头像
        userApi.updateUser({
          id: this.userInfo.id,
          avatar: formattedAvatarUrl
        }).then(response => {
          if (response.data && response.data.flag) {
            this.$message.success('头像更新成功');
            
            // 更新本地存储的用户信息
            const userData = getBackstageUser();
            if (userData && userData.userInfo) {
              userData.userInfo.avatar = formattedAvatarUrl;
              saveBackstageUser(userData);
            }
            
            // 触发全局头像更新事件
            EventBus.$emit('avatar-updated', formattedAvatarUrl);
            
            // 刷新用户信息
            this.getUserInfo();
          } else {
            this.$message.error('头像更新失败');
          }
        }).catch(() => {
          this.$message.error('头像更新失败');
        });
      } else {
        this.$message.error('头像上传失败');
      }
    },
    beforeAvatarUpload(file) {
      const isImage = file.type.startsWith('image/')
      const isLt20M = file.size / 1024 / 1024 < 20

      if (!isImage) {
        this.$message.error('头像只能是图片格式!')
      }
      if (!isLt20M) {
        this.$message.error('头像大小不能超过 20MB!')
      }
      return isImage && isLt20M
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // 构造提交的数据
          const updateData = {
            id: this.userInfo.id,
            name: this.userForm.name,
            idCard: this.userForm.idCard,
            phone: this.userForm.phone,
            gender: this.userForm.gender,
            avatar: this.userForm.avatar
          };
          
          userApi.updateUser(updateData).then(res => {
            if (res.data && res.data.flag) {
              this.$message.success('个人信息更新成功');
              this.getUserInfo();
            } else {
              this.$message.error(res.data && res.data.message ? res.data.message : '个人信息更新失败');
            }
          }).catch(() => {
            this.$message.error('个人信息更新失败');
          });
        } else {
          this.$message.error('请正确填写表单信息');
          return false;
        }
      });
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
      this.getUserInfo()
    },
    showEditPasswordDialog() {
      this.passwordDialogVisible = true
      this.passwordForm = {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
    },
    submitPasswordForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          userApi.editPassword({
            id: this.userInfo.id,
            oldPassword: this.passwordForm.oldPassword,
            newPassword: this.passwordForm.newPassword
          }).then(res => {
            if (res.data.flag) {
              this.$message.success('密码修改成功，请重新登录')
              this.passwordDialogVisible = false
              // 退出登录
              setTimeout(() => {
                this.$store.dispatch('logout')
                this.$router.push('/backstage/login')
              }, 1500)
            } else {
              this.$message.error(res.data.message || '密码修改失败')
            }
          }).catch(() => {
            this.$message.error('密码修改失败')
          })
        } else {
          this.$message.error('请正确填写表单信息')
          return false
        }
      })
    }
  }
}
</script>

<style scoped>
.profile {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.profile-form {
  margin-bottom: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.avatar-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30px;
  padding-top: 20px;
}

.user-avatar {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border: 3px solid #f0f0f0;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.avatar-container {
  width: 120px; /* Adjust as needed */
  height: 120px; /* Adjust as needed */
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f0f0f0; /* Fallback background */
}

.avatar-uploader {
  text-align: center;
}

.upload-btn {
  margin-top: 10px;
}
</style> 