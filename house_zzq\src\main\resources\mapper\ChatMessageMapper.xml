<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.house.dao.ChatMessageDao">
    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, house_id, from_user_id, to_user_id, content, message_type, read_status, create_time, update_time
    </sql>
    
    <!-- 保存聊天消息 -->
    <insert id="saveChatMessage" parameterType="com.house.pojo.ChatMessage" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO chat_message (
            house_id, from_user_id, to_user_id, content, message_type, read_status
        ) VALUES (
            #{houseId}, #{fromUserId}, #{toUserId}, #{content}, #{messageType}, #{readStatus}
        )
    </insert>
    
    <!-- 根据房源ID和用户ID获取聊天记录 -->
    <select id="getChatHistory" resultType="com.house.pojo.ChatMessage">
        SELECT 
            cm.*,
            h.address as houseAddress,
            u1.name as fromUserName,
            u1.avatar as fromUserAvatar,
            u2.name as toUserName,
            u2.avatar as toUserAvatar
        FROM 
            chat_message cm
        LEFT JOIN 
            house h ON cm.house_id = h.house_id
        LEFT JOIN 
            userlist u1 ON cm.from_user_id = u1.id
        LEFT JOIN 
            userlist u2 ON cm.to_user_id = u2.id
        WHERE 
            cm.house_id = #{houseId} 
            AND (
                (cm.from_user_id = #{userId} AND cm.to_user_id = #{landlordId})
                OR 
                (cm.from_user_id = #{landlordId} AND cm.to_user_id = #{userId})
            )
        ORDER BY 
            cm.create_time ASC
    </select>
    
    <!-- 将消息标记为已读 -->
    <update id="markMessagesAsRead">
        UPDATE 
            chat_message
        SET 
            read_status = 1,
            update_time = NOW()
        WHERE 
            to_user_id = #{toUserId}
            AND from_user_id = #{fromUserId}
            AND read_status = 0
    </update>
    
    <!-- 获取用户未读消息数量 -->
    <select id="getUnreadMessageCount" resultType="int">
        SELECT 
            COUNT(*)
        FROM 
            chat_message
        WHERE 
            to_user_id = #{userId}
            AND read_status = 0
    </select>
    
    <!-- 获取房东的所有聊天消息 -->
    <select id="getLandlordMessages" resultType="com.house.pojo.ChatMessage">
        SELECT 
            cm.*,
            h.address as houseAddress,
            u1.name as fromUserName,
            u1.avatar as fromUserAvatar,
            u2.name as toUserName,
            u2.avatar as toUserAvatar
        FROM 
            chat_message cm
        LEFT JOIN 
            house h ON cm.house_id = h.house_id
        LEFT JOIN 
            userlist u1 ON cm.from_user_id = u1.id
        LEFT JOIN 
            userlist u2 ON cm.to_user_id = u2.id
        WHERE 
            cm.from_user_id = #{landlordId} OR cm.to_user_id = #{landlordId}
        ORDER BY 
            cm.create_time DESC
    </select>
    
    <!-- 删除聊天记录 -->
    <delete id="deleteChatHistory">
        DELETE FROM 
            chat_message
        WHERE 
            house_id = #{houseId} 
            AND (
                (from_user_id = #{userId} AND to_user_id = #{landlordId})
                OR 
                (from_user_id = #{landlordId} AND to_user_id = #{userId})
            )
    </delete>
</mapper> 