import Vue from 'vue'
import App from './App.vue'
import ElementUI from 'element-ui';
// 使用正确的主题路径
import 'element-theme-chalk/lib/index.css';
// 确保引入图标样式
import 'element-ui/lib/theme-chalk/icon.css';
import './assets/css/global.css';

// 导入自定义Element UI组件
import './components/CustomElementComponents.vue';

import router from './router'
import store from './store'

// 引入封装好的请求库，而不是直接使用axios
import request from './utils/request'

import moment from 'moment'
moment.locale('zh-cn');

Vue.prototype.$moment = moment;

// 使用统一的请求实例
Vue.prototype.$http = request

Vue.config.productionTip = false

// 全局注册Element UI组件库
Vue.use(ElementUI);

// 创建全局数据对象，用于共享WebSocket实例
Vue.prototype.$globalData = {
  frontendWebSocket: null,
  backstageWebSocket: null,
  debug: true, // 开启调试模式
  
  // 记录WebSocket连接状态
  wsStatus: {
    frontend: 'disconnected',
    backstage: 'disconnected'
  },
  
  // 记录WebSocket连接URL
  wsUrl: {
    frontend: '',
    backstage: ''
  }
};

new Vue({
  router,
  store,
  data: {
    // 全局数据，用于共享WebSocket实例
    frontendWebSocket: null,
    backstageWebSocket: null
  },
  render: h => h(App)
}).$mount('#app')

