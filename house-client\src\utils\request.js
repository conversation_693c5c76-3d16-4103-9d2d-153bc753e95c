import axios from 'axios'
import { getFrontendUser, getBackstageUser, removeUser } from '@/utils/auth'
import { Message, Loading } from 'element-ui'
import router from '@/router'
import jwt_decode from 'jwt-decode'

/**
 * 创建通用axios实例
 * 该实例可用于前台和后台请求，根据URL自动选择使用前台还是后台的token
 * 包含了token验证、请求拦截和响应拦截功能
 */
const service = axios.create({
  // 后台接口统一使用9002端口
  baseURL: 'http://localhost:9002', // api的base_url
  timeout: 30000 // 请求超时时间
})

let loading; // 定义loading变量

function startLoading() {
  loading = Loading.service({
    lock: true,
    text: '加载中...',
    background: 'rgba(0, 0, 0, 0.7)'
  })
}

function endLoading() {
  if (loading) {
    loading.close()
  }
}

/**
 * 检查token是否过期
 * @param {string} token JWT token
 * @returns {boolean} 是否过期
 */
function isTokenExpired(token) {
  try {
    const decoded = jwt_decode(token);
    const currentTime = Math.floor(Date.now() / 1000);
    
    // 如果token没有过期时间或者过期时间大于当前时间，则token有效
    return decoded.exp && decoded.exp < currentTime;
  } catch (error) {
    console.error('检查token过期失败:', error);
    return true; // 解析失败视为过期
  }
}

// 请求拦截器 - 在请求发送前自动添加token
service.interceptors.request.use(
  config => {
    // 开始加载动画
    startLoading()
    // 根据当前路径判断是前台还是后台请求（优先级更高）
    const currentPath = router.currentRoute ? router.currentRoute.path : '';
    const isBackstageByPath = currentPath.startsWith('/backstage');

    // 根据请求URL判断是前台还是后台请求（作为备选方案）
    const isBackstageByUrl = config.url && (
      config.url.startsWith('/backstage') ||
      config.url.startsWith('/admin') ||
      config.url.includes('/owner')
    );

    // 优先使用路径判断，如果路径无法判断则使用URL判断
    const isBackstage = isBackstageByPath || isBackstageByUrl;

    // 如果是登录请求，添加system参数（如果还没有设置的话）
    if (config.url && (config.url.includes('/login') || config.url.includes('/user/login'))) {
      // 如果是POST请求，添加到请求体
      if (config.method === 'post') {
        config.data = config.data || {};
        if (typeof config.data === 'string') {
          // 如果数据是字符串（JSON字符串），先解析
          try {
            const data = JSON.parse(config.data);
            // 只有在没有设置system参数时才添加
            if (!data.system) {
              data.system = isBackstage ? 'backstage' : 'frontend';
            }
            config.data = JSON.stringify(data);
          } catch (e) {
            console.error('解析请求数据失败', e);
          }
        } else {
          // 如果是对象，只有在没有设置system参数时才添加
          if (!config.data.system) {
            config.data.system = isBackstage ? 'backstage' : 'frontend';
          }
        }
      } else {
        // 如果是GET请求，添加到查询参数
        config.params = config.params || {};
        if (!config.params.system) {
          config.params.system = isBackstage ? 'backstage' : 'frontend';
        }
      }
    }

    // 获取对应的用户信息和token
    const userInfo = isBackstage ? getBackstageUser() : getFrontendUser();
    const token = userInfo && userInfo.token ? userInfo.token : '';
    
    // 如果有token，检查是否过期
    if (token) {
      // 检查token是否过期
      if (isTokenExpired(token)) {
        // token已过期，清除用户信息
        console.log('Token已过期，清除登录状态');
        removeUser(isBackstage);
        
        // 如果当前不在登录页，则跳转到对应的登录页
        const currentPath = router.currentRoute.path;
        if (!currentPath.includes('/login')) {
          const loginPath = isBackstage ? '/backstage/login' : '/frontend/login';
          Message.error('登录已过期，请重新登录');
          router.replace({
            path: loginPath,
            query: { redirect: currentPath }
          });
        }

        // 结束加载动画
        endLoading();

        // 抛出错误，中断请求
        return Promise.reject(new Error('登录已过期，请重新登录'));
      }
      
      // token有效，设置请求头
      config.headers['Authorization'] = 'Bearer ' + token;
    } else {
      // 如果没有token，并且不是登录或注册请求，则提示用户登录
      const isLoginOrRegister = config.url && (
        config.url.includes('/login') || 
        config.url.includes('/register') ||
        config.url.includes('/user/login') ||
        config.url.includes('/user/register')
      );
      
      // 如果不是登录/注册请求，且不是公开API，则提示用户登录
      const isPublicApi = config.url && (
        config.url.includes('/api/images') ||
        config.url.includes('/house/gethothouses') ||
        config.url.includes('/house/getrecommendhouses') ||
        config.url.includes('/house/getallhouselist')
      );
      
      if (!isLoginOrRegister && !isPublicApi && router.currentRoute && !router.currentRoute.path.includes('/login')) {
        // 如果当前页面不是登录页，提示用户登录
        Message.warning('请先登录后再操作');
        
        // 结束加载动画
        endLoading();

        // 跳转到对应的登录页面
        const loginPath = isBackstage ? '/backstage/login' : '/frontend/login';
        router.replace({
          path: loginPath,
          query: { redirect: router.currentRoute.path }
        });

        // 中断请求
        return Promise.reject(new Error('请先登录'));
      }
    }
    
    return config;
  },
  error => {
    // 结束加载动画
    endLoading();
    console.log(error);
    return Promise.reject(error);
  }
);

// 响应拦截器 - 统一处理HTTP错误和token过期问题
service.interceptors.response.use(
  response => {
    // 结束加载动画
    endLoading();
    return response;
  },
  error => {
    // 结束加载动画
    endLoading();

    if (error.response) {
      switch (error.response.status) {
        // 401: 未登录或token过期
        case 401:
          // 根据当前路径判断是前台还是后台请求（优先级更高）
          const currentPath = router.currentRoute ? router.currentRoute.path : '';
          const isBackstageByPath = currentPath.startsWith('/backstage');

          // 根据请求URL判断是前台还是后台请求（作为备选方案）
          const isBackstageByUrl = error.config.url && (
            error.config.url.startsWith('/backstage') ||
            error.config.url.startsWith('/admin') ||
            error.config.url.includes('/owner')
          );

          // 优先使用路径判断，如果路径无法判断则使用URL判断
          const isBackstage = isBackstageByPath || isBackstageByUrl;

          // 清除对应的用户信息
          removeUser(isBackstage);

          // 显示登录过期提示
          Message.error('暂未登录，请重新登录');

          // 跳转到对应的登录页面
          const loginPath = isBackstage ? '/backstage/login' : '/frontend/login';
          router.replace({
            path: loginPath,
            query: { redirect: router.currentRoute.fullPath }
          });
          break;
          
        // 403: 权限不足
        case 403:
          Message.error('权限不足，无法访问');
          break;
          
        // 404: 请求不存在
        case 404:
          Message.error('请求的资源不存在');
          break;
          
        // 其他错误
        default:
          if (error.response.data && error.response.data.message) {
            Message.error(error.response.data.message);
          } else {
            Message.error('服务器出错，请稍后再试');
          }
      }
      return Promise.reject(error.response);
    } else {
      // 处理断网或请求超时
      if (!window.navigator.onLine) {
        Message.error('网络连接已断开，请检查网络');
      } else {
        Message.error('请求超时，请稍后再试');
      }
      return Promise.reject(error);
    }
  }
);

// 导出配置好的请求实例
export default service;