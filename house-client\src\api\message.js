import request from '@/utils/request'
const api_name = 'message'

export default {
    // 获取联系人列表
    getContactList(userId) {
        return request({
            url: `/${api_name}/contacts/${userId}`,
            method: 'get'
        })
    },
    
    // 获取消息历史记录
    getMessageHistory(userId, contactId) {
        return request({
            url: `/${api_name}/history/${userId}/${contactId}`,
            method: 'get'
        })
    },
    
    // 发送消息
    sendMessage(messageData) {
        return request({
            url: `/${api_name}/send`,
            method: 'post',
            data: messageData
        })
    },
    
    // 标记消息为已读
    markAsRead(userId, senderId) {
        return request({
            url: `/${api_name}/markread/${userId}/${senderId}`,
            method: 'put'
        })
    },
    
    // 清空消息历史
    clearHistory(userId, contactId) {
        return request({
            url: `/${api_name}/clear/${userId}/${contactId}`,
            method: 'delete'
        })
    },
    
    // 获取未读消息数量
    getUnreadCount(userId) {
        return request({
            url: `/${api_name}/unread/${userId}`,
            method: 'get'
        })
    }
} 