package com.house.websocket;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * WebSocket消息传输对象
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MessageDTO {
    /**
     * 消息ID
     */
    private Long id;
    
    /**
     * 消息标题
     */
    private String title;
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * 消息类型
     * info: 普通信息
     * success: 成功信息
     * warning: 警告信息
     * error: 错误信息
     * order: 订单通知
     * contract: 合同通知
     * payment: 支付通知
     */
    private String type;
    
    /**
     * 关联ID
     * 例如订单ID、合同ID等
     */
    private String linkId;
    
    /**
     * 关联类型
     * order: 订单
     * contract: 合同
     * payment: 支付
     * house: 房源
     */
    private String linkType;
    
    /**
     * 时间戳
     */
    private Long time;
    
    /**
     * 转换为JSON字符串
     * @return JSON字符串
     */
    public String toJsonString() {
        return JSON.toJSONString(this);
    }
} 