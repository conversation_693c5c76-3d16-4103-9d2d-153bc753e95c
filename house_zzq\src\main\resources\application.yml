server:
  port: 9002
spring:
  main:
    allow-bean-definition-overriding: true
  datasource:
      type: com.alibaba.druid.pool.DruidDataSource
      druid:
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: ******************************************************************************************************
        username: root
        password: root
        initialize: true
  mvc:
    # 静态资源配置
    static-path-pattern: /static/**
    # 不要将视图控制器路径映射为资源路径
    pathmatch:
      matching-strategy: ant_path_matcher
  # 加载静态资源
  resources:
    static-locations: classpath:/static/
  # 关闭默认错误页
  web:
    resources:
      add-mappings: true
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
      enabled: true

# 文件访问路径配置
file:
  # 图片保存路径
  upload-dir: D:/upload/images
  # 图片访问路径前缀
  access-path: /api/images

# JWT配置
jwt:
  # 过期时间，单位是毫秒，2小时 = 2 * 60分钟 * 60秒 * 1000毫秒 = 7200000毫秒
  ttl: 7200000
  # 是否记住我功能的时长，单位是毫秒，7天
  remember-me-ttl: 604800000

## 该配置节点为独立的节点，有很多同学容易将这个配置放在spring的节点下，导致配置无法被识别
mybatis:
   mapper-locations: classpath:mapper/*.xml #注意：一定要对应mapper映射xml文件的所在路径
   type-aliases-package: com.house.pojo # 注意：对应实体类的路径
   #mybatis.configuration.map-underscore-to-camel-case=true开启驼峰命名识别
   configuration:
     map-underscore-to-camel-case: true
     # 显示SQL语句 - 设为DEBUG级别，显示SQL语句
     log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl

# 日志配置
logging:
  # 全局日志级别设置
  level:
    root: INFO # 全局日志级别设为INFO，显示更多信息
    com.house: DEBUG # 项目自身的日志设为DEBUG，显示详细信息
    com.house.controller: DEBUG # 控制器日志设为DEBUG
    com.house.service: DEBUG # 服务层日志设为DEBUG
    com.house.mapper: DEBUG # 数据访问层日志设为DEBUG
    com.house.interceptor.JwtInterceptor: INFO # JWT拦截器保留INFO级别，显示token信息
    com.house.websocket: INFO # WebSocket日志设为INFO
    org.springframework: WARN # Spring框架日志设为WARN
    org.apache: WARN # Apache组件日志设为WARN
    org.hibernate: WARN # Hibernate日志设为WARN
    org.mybatis: DEBUG # MyBatis日志设为DEBUG，显示SQL语句
    # 显示SQL日志
    org.springframework.jdbc.core: DEBUG
    com.house.dao: DEBUG
    
  # 日志输出格式（简化版，只显示时间、级别和消息）
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} %-5level - %msg%n"





