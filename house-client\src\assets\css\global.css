/* 全局样式 */
body, html {
  margin: 0;
  padding: 0;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Aria<PERSON>, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 修复图标问题 */
.fas, .far, .fab, .fa {
  width: 24px;
  text-align: center;
  margin-right: 5px;
  vertical-align: middle;
  font-size: 18px;
}

/* 元素UI菜单图标 */
.el-menu-item [class^=el-icon-] {
  font-size: 18px;
  width: 24px;
  text-align: center;
  margin-right: 5px;
}

/* 菜单样式 */
.el-submenu__title {
  display: flex;
  align-items: center;
}

/* 卡片样式 */
.el-card {
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1) !important;
}

/* 常用flex布局 */
.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 常用margin/padding */
.mt-10 {
  margin-top: 10px;
}

.mb-10 {
  margin-bottom: 10px;
}

.ml-10 {
  margin-left: 10px;
}

.mr-10 {
  margin-right: 10px;
}

.pt-10 {
  padding-top: 10px;
}

.pb-10 {
  padding-bottom: 10px;
}

.pl-10 {
  padding-left: 10px;
}

.pr-10 {
  padding-right: 10px;
}

/* 内容容器通用样式 */
.page-container {
  width: 100%;
  min-height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 仅用于登录和注册页面 */
.no-scroll-page {
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

/* 为有固定导航栏的页面添加顶部间距 */
.has-fixed-header {
  padding-top: 60px;
}

/* 统一按钮样式 */
.btn-primary {
  background-color: #ff6b6b;
  border-color: #ff6b6b;
  color: #fff;
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 2px 6px rgba(255, 107, 107, 0.4);
}

.btn-primary:hover {
  background-color: #ff8585;
  border-color: #ff8585;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(255, 107, 107, 0.5);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(255, 107, 107, 0.4);
}

.btn-text {
  background: transparent;
  border: none;
  color: #2989d8;
  padding: 10px 15px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.btn-text:hover {
  color: #1e5799;
  text-decoration: underline;
}

/* 为避免滚动条遮挡的右侧按钮样式 */
.right-side-btn {
  margin-right: 20px; /* 增加右侧边距，避免被滚动条遮挡 */
}

/* 搜索按钮样式 */
.search-button {
  background-color: #ff6b6b;
  border-color: #ff6b6b;
  color: white;
  font-weight: 500;
  padding: 12px 25px;
  border-radius: 4px;
  box-shadow: 0 2px 6px rgba(255, 107, 107, 0.4);
  transition: all 0.3s;
}

.search-button:hover {
  background-color: #ff8585;
  border-color: #ff8585;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(255, 107, 107, 0.5);
}

.search-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(255, 107, 107, 0.4);
}