<template>
  <div class="order-list-page content-scroll">
    <Header />
    <div class="container">
      <div class="order-list-card">
        <h2>我的订单</h2>
        <el-divider></el-divider>
        
        <div class="order-tabs">
          <el-tabs v-model="activeTab" @tab-click="handleTabClick">
            <el-tab-pane label="全部订单" name="all"></el-tab-pane>
            <el-tab-pane label="租房申请" name="application">
              <span slot="label">租房申请 <el-badge :value="counters.application" :hidden="!counters.application"></el-badge></span>
            </el-tab-pane>
            <el-tab-pane label="待支付" name="unpaid">
              <span slot="label">待支付 <el-badge :value="counters.unpaid" :hidden="!counters.unpaid"></el-badge></span>
            </el-tab-pane>
            <el-tab-pane label="待入住" name="pending">
              <span slot="label">待入住 <el-badge :value="counters.pending" :hidden="!counters.pending"></el-badge></span>
            </el-tab-pane>
            <el-tab-pane label="租赁中" name="renting">
              <span slot="label">租赁中 <el-badge :value="counters.renting" :hidden="!counters.renting"></el-badge></span>
            </el-tab-pane>
            <el-tab-pane label="已完成" name="completed"></el-tab-pane>
            <el-tab-pane label="已取消" name="cancelled"></el-tab-pane>
          </el-tabs>
        </div>
        
        <div class="order-list" v-loading="loading">
          <el-empty v-if="orders.length === 0" description="暂无订单"></el-empty>
          
          <el-card v-for="order in orders" :key="order.id" class="order-item" shadow="hover">
            <div class="order-header">
              <div class="order-number">
                <span>订单编号：{{ order.orderNo }}</span>
                <el-tag size="small" :type="getStatusType(order.status)">{{ getStatusText(order.status) }}</el-tag>
              </div>
              <div class="order-time">下单时间：{{ formatDateTime(order.createTime) }}</div>
            </div>
            
            <div class="order-content">
              <div class="house-info" v-if="order.house">
                <div class="house-image">
                  <img :src="getHouseImage(order.house)" alt="房屋图片">
                </div>
                <div class="house-details">
                  <h3>{{ order.house.title }}</h3>
                  <p class="address">{{ order.house.address }}</p>
                  <p class="specs">{{ order.house.area }}㎡ | {{ order.house.bedroom }}室{{ order.house.living }}厅{{ order.house.bathroom }}卫</p>
                  <p class="lease-term">租期：{{ formatDate(order.startDate) }} 至 {{ formatDate(order.endDate) }}（{{ order.duration }}个月）</p>
                  <p class="price">月租：<span class="price-value">¥{{ order.monthlyPrice }}</span></p>
                </div>
              </div>
              <div class="order-info">
                <p><span class="label">押金：</span>¥{{ order.deposit }}</p>
                <p><span class="label">服务费：</span>¥{{ order.serviceFee }}</p>
                <p><span class="label">总价：</span><span class="total-price">¥{{ order.totalPrice }}</span></p>
              </div>
            </div>
            
            <div class="order-actions">
              <el-button size="small" @click="viewOrderDetail(order.orderNo)">查看详情</el-button>
              
              <template v-if="order.status === ORDER_STATUS.APPLICATION">
                <el-button size="small" type="success" @click="modifyApplication(order.orderNo)">修改申请</el-button>
                <el-button size="small" type="danger" @click="cancelOrder(order.orderNo)">取消申请</el-button>
              </template>

              <!-- 新增：申请已批准状态 -->
              <template v-if="order.status === ORDER_STATUS.APPROVED">
                <el-button size="small" type="info" disabled>申请已批准</el-button>
                <el-button size="small" type="danger" @click="cancelOrder(order.orderNo)">取消申请</el-button>
              </template>

              <!-- 新增：待签署合同状态 -->
              <template v-if="order.status === ORDER_STATUS.CONTRACT_PENDING">
                <el-button size="small" type="primary" @click="goToContract(order.orderNo)">查看并签署合同</el-button>
                <el-button size="small" type="danger" @click="cancelOrder(order.orderNo)">取消申请</el-button>
              </template>
              
              <template v-if="order.status === ORDER_STATUS.UNPAID">
                <el-button size="small" type="primary" @click="goToPay(order.orderNo)">去支付</el-button>
                <el-button size="small" type="info" @click="goToContract(order.orderNo)">查看合同</el-button>
                <el-button size="small" type="danger" @click="cancelOrder(order.orderNo)">取消订单</el-button>
              </template>

              <template v-if="order.status === ORDER_STATUS.PENDING">
                <el-button size="small" type="success" @click="goToCheckIn(order.orderNo)">确认入住</el-button>
                <el-button size="small" type="primary" @click="goToContract(order.orderNo)">查看合同</el-button>
              </template>

              <template v-if="order.status === ORDER_STATUS.RENTING">
                <el-button size="small" type="primary" @click="goToContract(order.orderNo)">查看合同</el-button>
                <el-button size="small" type="success" @click="payRent(order.orderNo)">支付租金</el-button>
                <el-button size="small" type="warning" @click="reviewHouse(order.houseId, order.id)" v-if="!order.reviewed">评价房源</el-button>
              </template>

              <template v-if="order.status === ORDER_STATUS.COMPLETED && !order.reviewed">
                <el-button size="small" type="warning" @click="reviewHouse(order.houseId, order.orderNo)">评价房源</el-button>
              </template>
            </div>
          </el-card>
          
          <div class="pagination-container" v-if="total > 0">
            <el-pagination
              background
              @current-change="handleCurrentChange"
              :current-page.sync="currentPage"
              :page-size="pageSize"
              layout="prev, pager, next, jumper"
              :total="total">
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
    <Footer />
  </div>
</template>

<script>
import Header from './components/Header'
import Footer from './components/Footer'
import { getOrderList, getOrderStatsByUserId, cancelOrder } from './api/order'
import { getHouseDetail } from './api/house'
import { getUser } from '../utils/auth'
import { getImageUrl } from '../utils/imageUtils'
import { OrderStatusUtils, ORDER_STATUS } from '../constants/orderStatus'
import moment from 'moment'
import http from '../utils/request'
import reviewApi from './api/review'

export default {
  name: 'OrderList',
  components: {
    Header,
    Footer
  },
  data() {
    return {
      activeTab: 'all',
      orders: [],
      loading: false,
      userInfo: null,
      userId: null, // 添加用户ID字段
      currentPage: 1,
      pageSize: 5,
      total: 0,
      counters: {
        application: 0,
        unpaid: 0,
        pending: 0,
        renting: 0,
        completed: 0,
        cancelled: 0
      },
      ORDER_STATUS, // 添加状态常量供模板使用
      $http: http // 添加HTTP服务实例
    }
  },
  created() {
    this.userInfo = getUser();
    // 打印用户信息，方便调试
    console.log('用户信息:', this.userInfo);
    
    if (!this.userInfo || Object.keys(this.userInfo).length === 0) {
      this.$message.warning('请先登录');
      this.$router.push('/frontend/login?redirect=/frontend/orderlist');
      return;
    }
    
    // 正确获取用户ID - 在auth.js中存储结构为userInfo.userInfo.id
    if (this.userInfo.userInfo && this.userInfo.userInfo.id) {
      // 用户ID存在于userInfo.userInfo.id中
      this.userId = this.userInfo.userInfo.id;
    } else {
      console.warn('用户ID不存在，跳转到登录页');
      this.$message.warning('用户信息不完整，请重新登录');
      this.$router.push('/frontend/login?redirect=/frontend/orderlist');
      return;
    }
    
    // 确保用户角色正确设置
    if (!this.userInfo.role) {
      // 从type判断角色
      if (this.userInfo.userInfo && this.userInfo.userInfo.type) {
        switch(this.userInfo.userInfo.type) {
          case 1:
            this.userInfo.role = 'admin';
            break;
          case 2:
            this.userInfo.role = 'tenant';
            break;
          case 3:
            this.userInfo.role = 'owner';
            break;
          default:
            this.userInfo.role = 'user';
        }
      } else {
        // 默认设置为普通用户
        this.userInfo.role = 'user';
      }
    }
    
    // 获取订单统计
    this.fetchOrderStats();
    
    // 获取订单列表
    this.fetchOrderList();
  },
  methods: {
    // 获取订单统计
    async fetchOrderStats() {
      try {
        // 确保用户ID存在
        if (!this.userId) {
          console.warn('获取订单统计: 用户ID不存在');
          return;
        }
        
        const res = await getOrderStatsByUserId(this.userId);
        console.log('订单统计响应:', res);
        
        // 修正：API返回格式为{flag: true, code: 20000, message: "获取订单统计成功", data: {...}}
        if (res.data && res.data.flag) {
          const stats = res.data.data;
          this.counters = {
            application: stats.application || 0,
            unpaid: stats.unpaid || 0,
            pending: stats.pending || 0,
            renting: stats.renting || 0,
            completed: stats.completed || 0,
            cancelled: stats.cancelled || 0
          };
        }
      } catch (error) {
        console.error('获取订单统计失败:', error);
      }
    },
    
    // 获取订单列表
    async fetchOrderList() {
      this.loading = true;
      
      try {
        // 确保用户ID存在
        if (!this.userId) {
          console.warn('获取订单列表: 用户ID不存在');
          this.loading = false;
          return;
        }

        const params = {
          page: this.currentPage,
          limit: this.pageSize
        };
        
        // 如果有选择状态，添加状态过滤
        if (this.activeTab !== 'all') {
          params.status = this.activeTab;
        }
        
        // 添加用户ID
        if (this.userInfo.role === 'tenant') {
          params.tenantId = this.userId;
        } else if (this.userInfo.role === 'owner') {
          params.ownerId = this.userId;
        } else {
          // 如果角色未定义，使用通用userId参数
          params.userId = this.userId;
        }
        
        const res = await getOrderList(params);
        console.log('订单列表响应:', res);
        
        // 修正：API返回格式为{flag: true, code: 20000, message: "获取订单列表成功", data: {...}}
        if (res.data && res.data.flag) {
          this.orders = res.data.data.list || [];
          this.total = res.data.data.total || 0;
          
          // 需要通过新的API请求获取每个订单对应的房屋信息
          await this.fetchHouseInfo();
        } else {
          this.$message.error('获取订单列表失败');
        }
      } catch (error) {
        console.error('获取订单列表失败:', error);
        this.$message.error('获取订单列表失败');
      } finally {
        this.loading = false;
      }
    },
    
    // 获取订单关联的房屋信息
    async fetchHouseInfo() {
      try {
        if (!this.orders || this.orders.length === 0) {
          return;
        }
        
        // 遍历订单，获取房屋信息
        for (let i = 0; i < this.orders.length; i++) {
          const order = this.orders[i];
          if (!order.houseId) continue;
          
          try {
            // 使用导入的getHouseDetail API
            const houseRes = await getHouseDetail(order.houseId);
            console.log(`房屋ID ${order.houseId} 的信息:`, houseRes);
            
            if (houseRes.data && houseRes.data.flag) {
              // 打印房屋数据，查看图片字段
              const houseData = houseRes.data.data;
              console.log(`房屋ID ${order.houseId} 的详细数据:`, houseData);
              console.log(`房屋图片URL:`, houseData.imageUrl);
              
              // 将房屋信息添加到订单对象中
              this.$set(this.orders[i], 'house', houseData);
            } else {
              // 如果获取失败，设置默认房屋信息
              this.$set(this.orders[i], 'house', {
                title: '房源信息获取失败',
                address: '地址未知',
                area: 0,
                bedroom: 0,
                living: 0,
                bathroom: 0,
                images: '/img/showcase.jpg'
              });
            }
          } catch (error) {
            console.error(`获取房屋ID ${order.houseId} 的信息失败:`, error);
            // 设置默认房屋信息
            this.$set(this.orders[i], 'house', {
              title: '房源信息获取失败',
              address: '地址未知',
              area: 0,
              bedroom: 0,
              living: 0,
              bathroom: 0,
              images: '/img/showcase.jpg'
            });
          }
        }
      } catch (error) {
        console.error('获取房屋信息失败:', error);
      }
    },
    
    // 处理标签页点击
    handleTabClick() {
      this.currentPage = 1
      this.fetchOrderList()
    },
    
    // 处理页码变化
    handleCurrentChange() {
      this.fetchOrderList()
    },
    
    // 格式化日期
    formatDate(dateStr) {
      return moment(dateStr).format('YYYY-MM-DD')
    },
    
    // 格式化日期时间
    formatDateTime(dateStr) {
      return moment(dateStr).format('YYYY-MM-DD HH:mm')
    },
    
    // 获取状态类型
    getStatusType(status) {
      return OrderStatusUtils.getStatusType(status)
    },

    // 获取状态文本
    getStatusText(status) {
      return OrderStatusUtils.getStatusText(status)
    },
    
    // 查看订单详情
    viewOrderDetail(orderNo) {
      this.$router.push('/frontend/orderdetail/' + orderNo)
    },
    
    // 去支付
    goToPay(orderNo) {
      this.$router.push('/frontend/payment/' + orderNo)
    },
    
    // 查看合同
    goToContract(orderNo) {
      if (!orderNo) {
        this.$message.error('订单号不能为空');
        return;
      }
      console.log('跳转到合同页面，订单号:', orderNo);
      
      // 记录当前用户信息，确保传递到下一个页面
      if (this.userInfo && this.userInfo.userInfo) {
        localStorage.setItem('currentUser', JSON.stringify(this.userInfo));
      }
      
      // 跳转到合同页面
      this.$router.push('/frontend/contract/' + orderNo);
    },

    // 前往入住确认
    goToCheckIn(orderNo) {
      if (!orderNo) {
        this.$message.error('订单号不能为空');
        return;
      }
      console.log('跳转到入住确认页面，订单号:', orderNo);

      // 跳转到入住确认页面
      this.$router.push('/frontend/checkin/' + orderNo);
    },

    // 支付租金
    payRent(orderNo) {
      // 跳转到支付页面，支付租金
      this.$router.push('/frontend/payment/' + orderNo + '?type=rent')
    },
    
    // 修改租房申请
    modifyApplication(orderNo) {
      // 导航到申请修改页面
      this.$router.push(`/frontend/apply/edit/${orderNo}`)
    },
    
    // 评价房源
    async reviewHouse(houseId, orderId) {
      if (!houseId || !orderId) {
        this.$message.error('房源ID或订单ID不能为空');
        return;
      }

      try {
        // 检查订单是否已评价
        const checkRes = await reviewApi.checkOrderReviewed(orderId);
        if (checkRes.data && checkRes.data.flag && checkRes.data.data) {
          this.$message.warning('该订单已经评价过了');
          return;
        }

        // 跳转到评价页面
        this.$router.push(`/frontend/review/${orderId}`);
      } catch (error) {
        console.error('检查评价状态失败:', error);
        // 即使检查失败也允许跳转到评价页面
        this.$router.push(`/frontend/review/${orderId}`);
      }
    },
    
    // 获取房屋图片
    getHouseImage(house) {
      if (!house) return require('../assets/showcase.jpg');
      
      // 后端House实体使用的是imageUrl字段
      if (house.imageUrl) {
        return getImageUrl(house.imageUrl);
      }
      
      // 向下兼容其他可能的字段
      if (house.images) {
        const imageArray = house.images.split(',');
        if (imageArray.length > 0 && imageArray[0]) {
          return getImageUrl(imageArray[0]);
        }
      }
      
      if (house.image) {
        return getImageUrl(house.image);
      }
      
      // 最后使用默认图片
      console.log('使用默认图片，房屋信息:', house);
      return getImageUrl('/img/showcase.jpg');
    },
    
    // 取消订单
    async cancelOrder(orderNo) {
      try {
        await this.$confirm('确定要取消订单吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        const res = await cancelOrder(orderNo)
        if (res.data && res.data.flag) {
          this.$message.success('订单已取消')
          // 重新获取订单列表和统计
          this.fetchOrderStats()
          this.fetchOrderList()
        } else {
          this.$message.error(res.data.message || '取消订单失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('取消订单失败:', error)
          this.$message.error('取消订单失败')
        }
      }
    }
  }
}
</script>

<style scoped>
.order-list-page {
  background-color: #f8f8f8;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.container {
  flex: 1;
  max-width: 1200px;
  margin: 30px auto;
  padding: 0 20px;
}

.order-list-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 30px;
}

h2 {
  font-size: 24px;
  color: #333;
  margin-bottom: 20px;
}

.order-item {
  margin-bottom: 20px;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 15px;
  margin-bottom: 15px;
}

.order-number {
  font-size: 14px;
  color: #606266;
}

.order-number .el-tag {
  margin-left: 10px;
}

.order-time {
  font-size: 13px;
  color: #909399;
}

.order-content {
  display: flex;
}

.house-info {
  display: flex;
  flex: 3;
  border-right: 1px solid #ebeef5;
  padding-right: 20px;
}

.house-image {
  width: 120px;
  height: 80px;
  overflow: hidden;
  border-radius: 4px;
  margin-right: 15px;
}

.house-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.house-details {
  flex: 1;
}

.house-details h3 {
  margin: 0 0 8px;
  font-size: 16px;
  color: #303133;
}

.house-details p {
  margin: 5px 0;
  font-size: 13px;
  color: #606266;
}

.house-details .address {
  color: #909399;
}

.house-details .price {
  margin-top: 10px;
}

.price-value {
  font-weight: bold;
  color: #f56c6c;
}

.order-info {
  flex: 1;
  padding-left: 20px;
}

.order-info p {
  margin: 8px 0;
  font-size: 14px;
  color: #606266;
}

.label {
  display: inline-block;
  width: 70px;
  color: #909399;
}

.total-price {
  font-weight: bold;
  color: #f56c6c;
  font-size: 16px;
}

.order-actions {
  text-align: right;
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px dashed #ebeef5;
}

.order-actions .el-button {
  margin-left: 10px;
}

.pagination-container {
  margin-top: 30px;
  text-align: center;
}

@media (max-width: 768px) {
  .order-content {
    flex-direction: column;
  }
  
  .house-info {
    border-right: none;
    border-bottom: 1px solid #ebeef5;
    padding-right: 0;
    padding-bottom: 15px;
    margin-bottom: 15px;
  }
  
  .order-info {
    padding-left: 0;
  }
}
</style> 