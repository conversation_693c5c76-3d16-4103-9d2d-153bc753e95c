package com.house.pojo;

import lombok.Data;

import java.util.Date;

/**
 * 合同实体类
 */
@Data
public class Contract {

    /**
     * 合同ID
     */
    private Integer id;

    /**
     * 合同编号
     */
    private String contractNo;

    /**
     * 关联的订单ID
     */
    private Integer orderId;

    /**
     * 合同内容
     */
    private String content;

    /**
     * 租客ID
     */
    private Integer tenantId;

    /**
     * 房东ID
     */
    private Integer ownerId;

    /**
     * 租客是否已签署
     */
    private Boolean tenantSigned;

    /**
     * 租客签署时间
     */
    private Date tenantSignTime;

    /**
     * 房东是否已签署
     */
    private Boolean ownerSigned;

    /**
     * 房东签署时间
     */
    private Date ownerSignTime;
    
    /**
     * 房东签名
     */
    private String ownerSignature;
    
    /**
     * 租客签名
     */
    private String tenantSignature;

    /**
     * 合同状态：
     * pending - 待签署
     * active - 生效中
     * expired - 已过期
     * cancelled - 已取消
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
} 