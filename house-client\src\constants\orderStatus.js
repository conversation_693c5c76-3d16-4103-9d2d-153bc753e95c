/**
 * 订单状态常量定义
 * 与后端保持完全一致
 */

// 订单状态枚举
export const ORDER_STATUS = {
  APPLICATION: 'application',        // 租房申请
  APPROVED: 'approved',             // 申请已批准
  CONTRACT_PENDING: 'contract_pending', // 待签署合同
  UNPAID: 'unpaid',                 // 待支付押金
  PENDING: 'pending',               // 待入住
  RENTING: 'renting',               // 租赁中
  COMPLETED: 'completed',           // 已完成
  CANCELLED: 'cancelled'            // 已取消
}

// 状态显示文本映射
export const ORDER_STATUS_TEXT = {
  [ORDER_STATUS.APPLICATION]: '待审核',
  [ORDER_STATUS.APPROVED]: '申请已批准',
  [ORDER_STATUS.CONTRACT_PENDING]: '待签署合同',
  [ORDER_STATUS.UNPAID]: '待支付押金',
  [ORDER_STATUS.PENDING]: '待入住',
  [ORDER_STATUS.RENTING]: '租赁中',
  [ORDER_STATUS.COMPLETED]: '已完成',
  [ORDER_STATUS.CANCELLED]: '已取消'
}

// 状态类型映射（用于Element UI的tag类型）
export const ORDER_STATUS_TYPE = {
  [ORDER_STATUS.APPLICATION]: 'info',
  [ORDER_STATUS.APPROVED]: 'success',
  [ORDER_STATUS.CONTRACT_PENDING]: 'primary',
  [ORDER_STATUS.UNPAID]: 'warning',
  [ORDER_STATUS.PENDING]: 'primary',
  [ORDER_STATUS.RENTING]: 'success',
  [ORDER_STATUS.COMPLETED]: 'info',
  [ORDER_STATUS.CANCELLED]: 'danger'
}

// 状态描述映射
export const ORDER_STATUS_DESC = {
  [ORDER_STATUS.APPLICATION]: '租房申请已提交，等待房东审核',
  [ORDER_STATUS.APPROVED]: '申请已通过，等待合同生成',
  [ORDER_STATUS.CONTRACT_PENDING]: '合同已生成，请尽快签署',
  [ORDER_STATUS.UNPAID]: '合同已签署，请尽快完成押金支付',
  [ORDER_STATUS.PENDING]: '押金已支付，请准备入住',
  [ORDER_STATUS.RENTING]: '已入住，请按时缴纳租金',
  [ORDER_STATUS.COMPLETED]: '订单已完成，感谢您的使用',
  [ORDER_STATUS.CANCELLED]: '订单已取消'
}

// 状态图标映射
export const ORDER_STATUS_ICON = {
  [ORDER_STATUS.APPLICATION]: 'el-icon-document',
  [ORDER_STATUS.APPROVED]: 'el-icon-check',
  [ORDER_STATUS.CONTRACT_PENDING]: 'el-icon-document-checked',
  [ORDER_STATUS.UNPAID]: 'el-icon-warning',
  [ORDER_STATUS.PENDING]: 'el-icon-house',
  [ORDER_STATUS.RENTING]: 'el-icon-success',
  [ORDER_STATUS.COMPLETED]: 'el-icon-circle-check',
  [ORDER_STATUS.CANCELLED]: 'el-icon-circle-close'
}

// 步骤映射（用于进度条显示）
export const ORDER_STEP_MAPPING = {
  [ORDER_STATUS.APPLICATION]: 0,      // 提交订单
  [ORDER_STATUS.APPROVED]: 0,         // 申请已通过（仍在第一步）
  [ORDER_STATUS.CONTRACT_PENDING]: 1, // 签署合同
  [ORDER_STATUS.UNPAID]: 2,           // 支付押金
  [ORDER_STATUS.PENDING]: 3,          // 入住
  [ORDER_STATUS.RENTING]: 4,          // 租赁中
  [ORDER_STATUS.COMPLETED]: 4         // 完成
}

// 正确的业务流程顺序
export const ORDER_FLOW = [
  ORDER_STATUS.APPLICATION,      // 1. 提交申请
  ORDER_STATUS.APPROVED,         // 2. 申请通过
  ORDER_STATUS.CONTRACT_PENDING, // 3. 签署合同
  ORDER_STATUS.UNPAID,          // 4. 支付押金
  ORDER_STATUS.PENDING,         // 5. 待入住
  ORDER_STATUS.RENTING,         // 6. 租赁中
  ORDER_STATUS.COMPLETED        // 7. 完成
]

// 可操作的状态转换
export const STATUS_TRANSITIONS = {
  [ORDER_STATUS.APPLICATION]: [ORDER_STATUS.APPROVED, ORDER_STATUS.CANCELLED],
  [ORDER_STATUS.APPROVED]: [ORDER_STATUS.CONTRACT_PENDING, ORDER_STATUS.CANCELLED],
  [ORDER_STATUS.CONTRACT_PENDING]: [ORDER_STATUS.UNPAID, ORDER_STATUS.CANCELLED],
  [ORDER_STATUS.UNPAID]: [ORDER_STATUS.PENDING, ORDER_STATUS.CANCELLED],
  [ORDER_STATUS.PENDING]: [ORDER_STATUS.RENTING],
  [ORDER_STATUS.RENTING]: [ORDER_STATUS.COMPLETED],
  [ORDER_STATUS.COMPLETED]: [],
  [ORDER_STATUS.CANCELLED]: []
}

// 工具函数
export const OrderStatusUtils = {
  // 获取状态文本
  getStatusText(status) {
    return ORDER_STATUS_TEXT[status] || status
  },
  
  // 获取状态类型
  getStatusType(status) {
    return ORDER_STATUS_TYPE[status] || 'info'
  },
  
  // 获取状态描述
  getStatusDesc(status) {
    return ORDER_STATUS_DESC[status] || ''
  },
  
  // 获取状态图标
  getStatusIcon(status) {
    return ORDER_STATUS_ICON[status] || 'el-icon-info'
  },
  
  // 获取步骤索引
  getStepIndex(status) {
    return ORDER_STEP_MAPPING[status] || 0
  },
  
  // 检查状态是否可以转换
  canTransitionTo(fromStatus, toStatus) {
    const allowedTransitions = STATUS_TRANSITIONS[fromStatus] || []
    return allowedTransitions.includes(toStatus)
  },
  
  // 获取下一个可能的状态
  getNextStatuses(currentStatus) {
    return STATUS_TRANSITIONS[currentStatus] || []
  }
}
