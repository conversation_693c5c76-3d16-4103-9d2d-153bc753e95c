<template>
  <div class="admin-dashboard">
    <div class="page-header">
      <h2>管理员控制台</h2>
    </div>

    <el-row :gutter="20">
      <!-- 系统概览 -->
      <el-col :span="24">
        <el-card shadow="hover" class="overview-card">
          <div slot="header">
            <span>系统概览</span>
          </div>
          <el-row :gutter="20">
            <el-col :xs="24" :sm="12" :md="8" :lg="6">
              <div class="stat-card">
                <div class="stat-icon">
                  <i class="el-icon-user"></i>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ stats.userCount }}</div>
                  <div class="stat-label">注册用户</div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="6">
              <div class="stat-card">
                <div class="stat-icon house-icon">
                  <i class="el-icon-house"></i>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ stats.houseCount }}</div>
                  <div class="stat-label">房源数量</div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="6">
              <div class="stat-card">
                <div class="stat-icon order-icon">
                  <i class="el-icon-s-order"></i>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ stats.orderCount }}</div>
                  <div class="stat-label">订单数量</div>
                </div>
              </div>
            </el-col>
            <el-col :xs="24" :sm="12" :md="8" :lg="6">
              <div class="stat-card">
                <div class="stat-icon income-icon">
                  <i class="el-icon-money"></i>
                </div>
                <div class="stat-info">
                  <div class="stat-value">{{ formatAmount(stats.totalIncome) }}</div>
                  <div class="stat-label">总收入</div>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 最新用户 -->
      <el-col :span="12">
        <el-card shadow="hover" class="latest-card">
          <div slot="header">
            <span>最新用户</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="goToUserManagement">查看所有</el-button>
          </div>
          <el-table :data="latestUsers" style="width: 100%" :show-header="false" v-loading="loading.users">
            <el-table-column>
              <template slot-scope="scope">
                <div class="user-item">
                  <el-avatar :size="40" :src="scope.row.avatar || defaultAvatar"></el-avatar>
                  <div class="user-info">
                    <div class="user-name">{{ scope.row.username }}</div>
                    <div class="user-date">{{ formatDate(scope.row.createTime) }}</div>
                  </div>
                  <el-tag size="mini" :type="getRoleTagType(scope.row.role)">{{ getRoleName(scope.row.role) }}</el-tag>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      
      <!-- 最新订单 -->
      <el-col :span="12">
        <el-card shadow="hover" class="latest-card">
          <div slot="header">
            <span>最新订单</span>
            <el-button style="float: right; padding: 3px 0" type="text" @click="goToOrderManagement">查看所有</el-button>
          </div>
          <el-table :data="latestOrders" style="width: 100%" :show-header="false" v-loading="loading.orders">
            <el-table-column>
              <template slot-scope="scope">
                <div class="order-item">
                  <div class="order-title">{{ scope.row.houseName }}</div>
                  <div class="order-info">
                    <span class="order-id">订单号: {{ scope.row.orderNo }}</span>
                    <span class="order-date">{{ formatDate(scope.row.createTime) }}</span>
                  </div>
                  <div class="order-bottom">
                    <span class="order-price">¥{{ formatAmount(scope.row.amount) }}</span>
                    <el-tag size="mini" :type="getOrderStatusType(scope.row.status)">{{ getOrderStatusText(scope.row.status) }}</el-tag>
                  </div>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <!-- 系统操作 -->
      <el-col :span="24">
        <el-card shadow="hover">
          <div slot="header">
            <span>快捷操作</span>
          </div>
          <div class="quick-actions">
            <el-button type="primary" icon="el-icon-plus" @click="goToAddUser">添加用户</el-button>
            <el-button type="success" icon="el-icon-house" @click="goToAddHouse">添加房源</el-button>
            <el-button type="warning" icon="el-icon-setting" @click="goToSettings">系统设置</el-button>
            <el-button type="info" icon="el-icon-refresh" @click="refreshData">刷新数据</el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import moment from 'moment'
import { OrderStatusUtils, ORDER_STATUS } from '../constants/orderStatus'

export default {
  name: 'AdminDashboard',
  data() {
    return {
      stats: {
        userCount: 0,
        houseCount: 0,
        orderCount: 0,
        totalIncome: 0
      },
      latestUsers: [],
      latestOrders: [],
      loading: {
        stats: false,
        users: false,
        orders: false
      },
      defaultAvatar: require('@/assets/showcase.jpg')
    }
  },
  created() {
    this.fetchStatistics();
    this.fetchLatestUsers();
    this.fetchLatestOrders();
  },
  methods: {
    // 获取统计数据
    async fetchStatistics() {
      this.loading.stats = true;
      try {
        const response = await this.$http.get('/admin/statistics');
        if (response.data && response.data.flag) {
          this.stats = response.data.data;
        }
      } catch (error) {
        console.error('获取统计数据失败:', error);
      } finally {
        this.loading.stats = false;
      }
    },
    
    // 获取最新用户
    async fetchLatestUsers() {
      this.loading.users = true;
      try {
        const response = await this.$http.get('/admin/users/latest');
        if (response.data && response.data.flag) {
          this.latestUsers = response.data.data || [];
        }
      } catch (error) {
        console.error('获取最新用户失败:', error);
      } finally {
        this.loading.users = false;
      }
    },
    
    // 获取最新订单
    async fetchLatestOrders() {
      this.loading.orders = true;
      try {
        const response = await this.$http.get('/admin/orders/latest');
        if (response.data && response.data.flag) {
          this.latestOrders = response.data.data || [];
        }
      } catch (error) {
        console.error('获取最新订单失败:', error);
      } finally {
        this.loading.orders = false;
      }
    },
    
    // 刷新所有数据
    refreshData() {
      this.fetchStatistics();
      this.fetchLatestUsers();
      this.fetchLatestOrders();
      this.$message.success('数据已刷新');
    },
    
    // 跳转到用户管理
    goToUserManagement() {
      this.$router.push('/backstage/users');
    },
    
    // 跳转到订单管理
    goToOrderManagement() {
      this.$router.push('/backstage/orders');
    },
    
    // 跳转到添加用户
    goToAddUser() {
      // 跳转到用户列表并打开添加用户对话框
      this.$router.push('/backstage/users');
      // 需要在UserList组件中添加一个方法来打开添加对话框
    },
    
    // 跳转到添加房源
    goToAddHouse() {
      this.$router.push('/backstage/house-add');
    },
    
    // 跳转到系统设置
    goToSettings() {
      this.$router.push('/backstage/settings');
    },
    
    // 格式化日期
    formatDate(date) {
      return moment(date).format('YYYY-MM-DD HH:mm');
    },
    
    // 格式化金额
    formatAmount(amount) {
      return parseFloat(amount || 0).toFixed(2);
    },
    
    // 获取角色标签类型
    getRoleTagType(role) {
      const types = {
        admin: 'danger',
        owner: 'warning',
        tenant: 'success'
      };
      return types[role] || 'info';
    },
    
    // 获取角色名称
    getRoleName(role) {
      const names = {
        admin: '管理员',
        owner: '房东',
        tenant: '租客'
      };
      return names[role] || '未知';
    },
    
    // 获取订单状态标签类型
    getOrderStatusType(status) {
      return OrderStatusUtils.getStatusType(status)
    },

    // 获取订单状态文本
    getOrderStatusText(status) {
      return OrderStatusUtils.getStatusText(status)
    }
  }
}
</script>

<style scoped>
.admin-dashboard {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
  margin-bottom: 10px;
}

.stat-icon {
  width: 60px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #409EFF;
  color: white;
  border-radius: 50%;
  margin-right: 15px;
  font-size: 24px;
}

.stat-icon.house-icon {
  background-color: #67C23A;
}

.stat-icon.order-icon {
  background-color: #E6A23C;
}

.stat-icon.income-icon {
  background-color: #F56C6C;
}

.stat-value {
  font-size: 22px;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 5px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.latest-card {
  min-height: 400px;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #EBEEF5;
}

.user-info {
  flex: 1;
  margin-left: 15px;
}

.user-name {
  font-weight: bold;
}

.user-date {
  font-size: 12px;
  color: #909399;
}

.order-item {
  padding: 10px 0;
  border-bottom: 1px solid #EBEEF5;
}

.order-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.order-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
  margin-bottom: 5px;
}

.order-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-price {
  font-weight: bold;
  color: #F56C6C;
}

.quick-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}
</style> 