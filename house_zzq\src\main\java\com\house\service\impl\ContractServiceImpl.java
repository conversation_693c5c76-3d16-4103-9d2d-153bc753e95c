package com.house.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.house.constants.ContractStatus;
import com.house.constants.OrderStatus;
import com.house.dao.UserListDao;
import com.house.dto.ContractDTO;
import com.house.mapper.ContractMapper;
import com.house.mapper.HouseMapper;
import com.house.mapper.OrderMapper;
import com.house.pojo.Contract;
import com.house.pojo.House;
import com.house.pojo.Order;
import com.house.pojo.UserList;
import com.house.service.ContractService;
import com.house.service.NotificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * 合同服务实现类
 */
@Service
public class ContractServiceImpl implements ContractService {

    @Autowired
    private ContractMapper contractMapper;

    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private HouseMapper houseMapper;
    
    @Autowired
    private UserListDao userListDao;

    @Autowired
    private NotificationService notificationService;

    /**
     * 生成唯一合同号
     */
    private String generateContractNo() {
        return "CON" + System.currentTimeMillis() + String.format("%04d", new Random().nextInt(10000));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Contract createContract(Contract contract) {
        // 检查是否已存在合同
        if (contract.getOrderId() != null) {
            Contract existingContract = contractMapper.selectByOrderId(contract.getOrderId());
            if (existingContract != null) {
                return existingContract; // 已存在合同，直接返回
            }
        }

        // 生成合同号
        contract.setContractNo(generateContractNo());
        
        // 设置初始值
        if (contract.getTenantSigned() == null) {
            contract.setTenantSigned(false);
        }
        if (contract.getOwnerSigned() == null) {
            contract.setOwnerSigned(false);
        }
        if (contract.getStatus() == null) {
            contract.setStatus(ContractStatus.PENDING);
        }

        // 设置时间
        Date now = new Date();
        contract.setCreateTime(now);
        contract.setUpdateTime(now);

        // 插入合同
        contractMapper.insert(contract);

        // 发送通知给租客
        notificationService.sendNotification(
                String.valueOf(contract.getTenantId()),
                "合同已生成",
                "您的租房合同已生成，请查看并签署合同。",
                "contract",
                contract.getContractNo(),
                "contract"
        );

        // 发送通知给房东
        notificationService.sendNotification(
                String.valueOf(contract.getOwnerId()),
                "合同已生成",
                "租房合同已生成，请查看并签署合同。",
                "contract",
                contract.getContractNo(),
                "contract"
        );

        return contract;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Contract createContractForOrder(Integer orderId) {
        // 获取订单信息
        Order order = orderMapper.selectById(orderId);
        if (order == null) {
            throw new RuntimeException("订单不存在");
        }

        // 检查是否已存在合同
        Contract existingContract = contractMapper.selectByOrderId(orderId);
        if (existingContract != null) {
            return existingContract; // 已存在合同，直接返回
        }

        // 创建合同对象
        Contract contract = new Contract();
        contract.setOrderId(orderId);
        contract.setTenantId(order.getTenantId());
        contract.setOwnerId(order.getOwnerId());

        // 创建合同内容
        StringBuilder contentBuilder = new StringBuilder();
        contentBuilder.append("房屋租赁合同\n\n");
        contentBuilder.append("甲方（房东）：").append(order.getOwnerId()).append("\n");
        contentBuilder.append("乙方（租客）：").append(order.getTenantId()).append("\n");
        contentBuilder.append("租赁期限：").append(order.getDuration()).append("个月").append("\n");
        contentBuilder.append("开始日期：").append(order.getStartDate()).append("\n");
        contentBuilder.append("结束日期：").append(order.getEndDate()).append("\n");
        contentBuilder.append("月租金：￥").append(order.getMonthlyPrice()).append("\n");
        contentBuilder.append("押金：￥").append(order.getDeposit()).append("\n");
        contentBuilder.append("服务费：￥").append(order.getServiceFee()).append("\n");
        contentBuilder.append("总价：￥").append(order.getTotalPrice()).append("\n");
        contract.setContent(contentBuilder.toString());

        // 生成合同号
        contract.setContractNo(generateContractNo());
        // 设置初始状态
        contract.setStatus(ContractStatus.PENDING);
        contract.setTenantSigned(false);
        contract.setOwnerSigned(false);
        // 设置时间
        Date now = new Date();
        contract.setCreateTime(now);
        contract.setUpdateTime(now);

        // 插入合同
        contractMapper.insert(contract);

        // 发送通知给租客
        notificationService.sendNotification(
                String.valueOf(contract.getTenantId()),
                "合同已生成",
                "您的租房合同已生成，请查看并签署合同。",
                "contract",
                contract.getContractNo(),
                "contract"
        );

        // 发送通知给房东
        notificationService.sendNotification(
                String.valueOf(contract.getOwnerId()),
                "合同已生成",
                "租房合同已生成，请查看并签署合同。",
                "contract",
                contract.getContractNo(),
                "contract"
        );

        return contract;
    }

    @Override
    public Contract getContractByContractNo(String contractNo) {
        return contractMapper.selectByContractNo(contractNo);
    }

    @Override
    public Contract getContractById(Integer id) {
        return contractMapper.selectById(id);
    }

    @Override
    public Contract getContractByOrderId(Integer orderId) {
        return contractMapper.selectByOrderId(orderId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean signContractByTenant(Integer contractId, Integer tenantId, String tenantSignature) {
        Contract contract = contractMapper.selectById(contractId);
        if (contract == null || !contract.getTenantId().equals(tenantId)) {
            return false;
        }

        // 设置租客签署状态和签名
        contract.setTenantSigned(true);
        contract.setTenantSignature(tenantSignature);
        if (contract.getTenantSignTime() == null) {
            contract.setTenantSignTime(new Date());
        }
        
        // 如果双方都已签署，更新合同状态
        if (Boolean.TRUE.equals(contract.getOwnerSigned())) {
            contract.setStatus(ContractStatus.ACTIVE);
            
            // 更新关联订单状态
            Order order = orderMapper.selectById(contract.getOrderId());
            if (order != null) {
                order.setStatus(OrderStatus.UNPAID);
                order.setRemark("合同已签署，请尽快支付押金");
                
                // 更新合同签署时间
                Date signTime = new Date();
                order.setContractSignTime(signTime);
                
                // 更新订单
                orderMapper.update(order);
                
                // 也可以使用专门的方法更新合同签署时间
                orderMapper.updateContractSignTime(order.getOrderNo(), signTime);
                
                // 发送通知
                notificationService.sendNotification(
                        String.valueOf(contract.getTenantId()),
                        "合同已完成签署",
                        "合同已签署完成，请尽快支付押金。",
                        "payment",
                        order.getOrderNo(),
                        "contract"
                );
            }
        } else {
            // 发送通知给房东
            notificationService.sendNotification(
                    String.valueOf(contract.getOwnerId()),
                    "租客已签署合同",
                    "租客已签署合同，请您尽快查看并签署。",
                    "contract",
                    contract.getContractNo(),
                    "contract"
            );
        }

        // 更新合同
        contract.setUpdateTime(new Date());
        return contractMapper.updateById(contract) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean signContractByOwner(Integer contractId, Integer ownerId, String ownerSignature) {
        Contract contract = contractMapper.selectById(contractId);
        if (contract == null || !contract.getOwnerId().equals(ownerId)) {
            return false;
        }

        // 设置房东签署状态和签名
        contract.setOwnerSigned(true);
        contract.setOwnerSignature(ownerSignature);
        if (contract.getOwnerSignTime() == null) {
            contract.setOwnerSignTime(new Date());
        }
        
        // 如果双方都已签署，更新合同状态
        if (Boolean.TRUE.equals(contract.getTenantSigned())) {
            contract.setStatus(ContractStatus.ACTIVE);
            
            // 更新关联订单状态
            Order order = orderMapper.selectById(contract.getOrderId());
            if (order != null) {
                order.setStatus(OrderStatus.UNPAID);
                order.setRemark("合同已签署，请尽快支付押金");
                
                // 更新合同签署时间
                Date signTime = new Date();
                order.setContractSignTime(signTime);
                
                // 更新订单
                orderMapper.update(order);
                
                // 也可以使用专门的方法更新合同签署时间
                orderMapper.updateContractSignTime(order.getOrderNo(), signTime);
                
                // 发送通知
                notificationService.sendNotification(
                        String.valueOf(contract.getTenantId()),
                        "合同已完成签署",
                        "合同已签署完成，请尽快支付押金。",
                        "payment",
                        order.getOrderNo(),
                        "contract"
                );
            }
        } else {
            // 发送通知给租客
            notificationService.sendNotification(
                    String.valueOf(contract.getTenantId()),
                    "房东已签署合同",
                    "房东已签署合同，请您尽快查看并签署。",
                    "contract",
                    contract.getContractNo(),
                    "contract"
            );
        }

        // 更新合同
        contract.setUpdateTime(new Date());
        return contractMapper.updateById(contract) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateContractStatus(Integer contractId, String status) {
        Contract contract = contractMapper.selectById(contractId);
        if (contract == null) {
            throw new RuntimeException("合同不存在");
        }

        int result = contractMapper.updateStatus(contractId, status);
        
        if (result > 0) {
            // 如果状态为active，更新订单的合同签署时间
            if (ContractStatus.ACTIVE.equals(status)) {
                Order order = orderMapper.selectById(contract.getOrderId());
                if (order != null && order.getContractSignTime() == null) {
                    // 更新合同签署时间
                    Date signTime = new Date();
                    order.setContractSignTime(signTime);
                    orderMapper.update(order);
                    
                    // 也可以使用专门的方法更新合同签署时间
                    orderMapper.updateContractSignTime(order.getOrderNo(), signTime);
                }
            }
            
            // 发送通知给租客
            String message = ContractStatus.getStatusDesc(status);
            if (message.isEmpty()) {
                message = "合同状态已更新为" + ContractStatus.getStatusText(status);
            }
            
            // 发送通知给租客
            notificationService.sendNotification(
                    String.valueOf(contract.getTenantId()),
                    "合同状态更新",
                    message,
                    "contract",
                    contract.getContractNo(),
                    "contract"
            );
            
            // 发送通知给房东
            notificationService.sendNotification(
                    String.valueOf(contract.getOwnerId()),
                    "合同状态更新",
                    message,
                    "contract",
                    contract.getContractNo(),
                    "contract"
            );
            
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelContract(Integer contractId) {
        Contract contract = contractMapper.selectById(contractId);
        if (contract == null) {
            throw new RuntimeException("合同不存在");
        }

        // 只有待签署的合同可以取消
        if (!ContractStatus.canCancel(contract.getStatus())) {
            throw new RuntimeException("当前状态的合同不能取消");
        }

        int result = contractMapper.updateStatus(contractId, ContractStatus.CANCELLED);
        
        if (result > 0) {
            // 发送通知给租客
            notificationService.sendNotification(
                    String.valueOf(contract.getTenantId()),
                    "合同已取消",
                    "您的租房合同已取消。",
                    "contract",
                    contract.getContractNo(),
                    "contract"
            );
            
            // 发送通知给房东
            notificationService.sendNotification(
                    String.valueOf(contract.getOwnerId()),
                    "合同已取消",
                    "租房合同已取消。",
                    "contract",
                    contract.getContractNo(),
                    "contract"
            );
            
            return true;
        }
        return false;
    }

    @Override
    public PageInfo<Contract> getContractList(Map<String, Object> params) {
        // 分页参数
        Integer pageNum = 1;
        Integer pageSize = 10;
        
        if (params.get("page") != null) {
            pageNum = Integer.parseInt(params.get("page").toString());
        }
        if (params.get("limit") != null) {
            pageSize = Integer.parseInt(params.get("limit").toString());
        }
        
        // 开始分页
        PageHelper.startPage(pageNum, pageSize);
        
        // 执行查询
        List<Contract> contracts = contractMapper.selectList(params);
        
        // 返回分页结果
        return new PageInfo<>(contracts);
    }

    @Override
    public PageInfo<ContractDTO> getContractDTOList(Map<String, Object> params) {
        // 分页参数
        Integer pageNum = 1;
        Integer pageSize = 10;
        
        if (params.get("page") != null) {
            pageNum = Integer.parseInt(params.get("page").toString());
        }
        if (params.get("limit") != null) {
            pageSize = Integer.parseInt(params.get("limit").toString());
        }
        
        // 开始分页
        PageHelper.startPage(pageNum, pageSize);
        
        // 执行查询
        List<Contract> contracts = contractMapper.selectList(params);
        
        // 转换为DTO
        List<ContractDTO> contractDTOs = new ArrayList<>();
        for (Contract contract : contracts) {
            // 获取订单信息
            Order order = orderMapper.selectById(contract.getOrderId());
            
            // 获取房源信息
            House house = null;
            if (order != null && order.getHouseId() != null) {
                house = houseMapper.getHouseDetail(order.getHouseId());
            }
            
            // 获取租客信息
            UserList tenant = userListDao.findUserListById(contract.getTenantId());
            
            // 获取房东信息
            UserList owner = userListDao.findUserListById(contract.getOwnerId());
            
            // 构建DTO
            ContractDTO dto = ContractDTO.fromEntities(contract, order, house, tenant, owner);
            contractDTOs.add(dto);
        }
        
        // 创建新的PageInfo对象
        PageInfo<Contract> originalPageInfo = new PageInfo<>(contracts);
        PageInfo<ContractDTO> pageInfo = new PageInfo<>(contractDTOs);
        
        // 复制分页信息
        pageInfo.setTotal(originalPageInfo.getTotal());
        pageInfo.setPageNum(originalPageInfo.getPageNum());
        pageInfo.setPageSize(originalPageInfo.getPageSize());
        pageInfo.setPages(originalPageInfo.getPages());
        
        return pageInfo;
    }
    
    @Override
    public ContractDTO getContractDTOById(Integer id) {
        Contract contract = contractMapper.selectById(id);
        if (contract == null) {
            return null;
        }
        
        // 获取订单信息
        Order order = orderMapper.selectById(contract.getOrderId());
        
        // 获取房源信息
        House house = null;
        if (order != null && order.getHouseId() != null) {
            house = houseMapper.getHouseDetail(order.getHouseId());
        }
        
        // 获取租客信息
        UserList tenant = userListDao.findUserListById(contract.getTenantId());
        
        // 获取房东信息
        UserList owner = userListDao.findUserListById(contract.getOwnerId());
        
        // 构建DTO
        return ContractDTO.fromEntities(contract, order, house, tenant, owner);
    }
    
    @Override
    public ContractDTO getContractDTOByContractNo(String contractNo) {
        Contract contract = contractMapper.selectByContractNo(contractNo);
        if (contract == null) {
            return null;
        }
        
        // 获取订单信息
        Order order = orderMapper.selectById(contract.getOrderId());
        
        // 获取房源信息
        House house = null;
        if (order != null && order.getHouseId() != null) {
            house = houseMapper.getHouseDetail(order.getHouseId());
        }
        
        // 获取租客信息
        UserList tenant = userListDao.findUserListById(contract.getTenantId());
        
        // 获取房东信息
        UserList owner = userListDao.findUserListById(contract.getOwnerId());
        
        // 构建DTO
        return ContractDTO.fromEntities(contract, order, house, tenant, owner);
    }
    
    @Override
    public ContractDTO getContractDTOByOrderId(Integer orderId) {
        Contract contract = contractMapper.selectByOrderId(orderId);
        if (contract == null) {
            return null;
        }
        
        // 获取订单信息
        Order order = orderMapper.selectById(orderId);
        
        // 获取房源信息
        House house = null;
        if (order != null && order.getHouseId() != null) {
            house = houseMapper.getHouseDetail(order.getHouseId());
        }
        
        // 获取租客信息
        UserList tenant = userListDao.findUserListById(contract.getTenantId());
        
        // 获取房东信息
        UserList owner = userListDao.findUserListById(contract.getOwnerId());
        
        // 构建DTO
        return ContractDTO.fromEntities(contract, order, house, tenant, owner);
    }
} 