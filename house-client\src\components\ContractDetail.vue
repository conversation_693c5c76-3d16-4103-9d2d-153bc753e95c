<template>
  <div class="contract-detail">
    <div class="contract-header">
      <h2>房屋租赁合同</h2>
      <div class="contract-status">
        <el-tag :type="getContractStatusType(contract.status)">{{ getContractStatusText(contract.status) }}</el-tag>
      </div>
    </div>

    <div class="contract-info">
      <el-descriptions :column="3" border>
        <el-descriptions-item label="合同编号">{{ contract.contractNo }}</el-descriptions-item>
        <el-descriptions-item label="订单编号">{{ contract.orderNo }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDateTime(contract.createTime) }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <div class="contract-content">
      <div class="contract-body">
        <h1 class="contract-title">房屋租赁合同</h1>
        
        <div class="contract-parties">
          <p><strong>出租方（甲方）：</strong> {{ contract.ownerName }}</p>
          <p><strong>承租方（乙方）：</strong> {{ contract.tenantName }}</p>
        </div>
        
        <div class="contract-terms">
          <p>甲、乙双方就下列房屋的租赁达成如下协议：</p>
          
          <h3>第一条 房屋基本情况</h3>
          <p>房屋坐落于 {{ contract.house ? contract.house.address : '' }}（以下简称该房屋）。建筑面积 {{ contract.house ? contract.house.area : '' }} 平方米。</p>
          
          <h3>第二条 租赁期限</h3>
          <p>租赁期共 {{ contract.duration || 12 }} 个月，自 {{ formatDate(contract.startDate) }} 起至 {{ formatDate(contract.endDate) }} 止。</p>
          
          <h3>第三条 租金及支付方式</h3>
          <p>1. 月租金为人民币 {{ contract.monthlyRent || contract.monthlyPrice }} 元整。</p>
          <p>2. 乙方应于每月1日前支付当月租金。</p>
          <p>3. 押金为人民币 {{ contract.deposit }} 元整，乙方应于签订本合同时支付，合同终止时，如乙方无违约行为，甲方应如数退还给乙方。</p>
          
          <h3>第四条 甲方的权利与义务</h3>
          <p>1. 甲方应保证出租房屋的安全性，并保障乙方正常居住的权利。</p>
          <p>2. 甲方应按约定向乙方提供房屋及设施设备。</p>
          <p>3. 甲方有权依照约定收取租金、押金。</p>
          
          <h3>第五条 乙方的权利与义务</h3>
          <p>1. 乙方应按时支付租金。</p>
          <p>2. 乙方应合理使用并爱护房屋及其设施设备。</p>
          <p>3. 乙方不得擅自改变房屋结构及用途。</p>
          
          <h3>第六条 合同解除</h3>
          <p>租赁期满，合同自然终止。如乙方要求继续租赁，应提前30天向甲方提出，重新协商签订租赁合同。</p>
          
          <h3>第七条 违约责任</h3>
          <p>任何一方违反本合同约定，应承担相应违约责任并赔偿由此给对方造成的损失。</p>
          
          <h3>第八条 争议解决方式</h3>
          <p>本合同项下发生的争议，由双方当事人协商解决；协商不成的，提交房屋所在地人民法院诉讼解决。</p>
          
          <h3>第九条 其他约定</h3>
          <p>本合同经甲乙双方签字或盖章后生效。本合同一式两份，甲乙双方各执一份，具有同等法律效力。</p>
        </div>

        <div class="contract-signatures">
          <div class="signature-area">
            <div class="signature-title">甲方（出租方）签章：</div>
            <div class="signature-content">
              <div v-if="contract.ownerSignTime" class="sign-box">
                <div class="signature-name">{{ contract.ownerSignature || contract.ownerName }}</div>
                <div class="sign-time">签署时间：{{ formatDateTime(contract.ownerSignTime) }}</div>
              </div>
              <el-button 
                v-else-if="isOwner && canSign" 
                type="success"
                @click="signContract('owner')">
                我是房东，立即签署
              </el-button>
              <span v-else>未签署</span>
            </div>
          </div>
          <div class="signature-area">
            <div class="signature-title">乙方（承租方）签章：</div>
            <div class="signature-content">
              <div v-if="contract.tenantSignTime" class="sign-box">
                <div class="signature-name">{{ contract.tenantSignature || contract.tenantName }}</div>
                <div class="sign-time">签署时间：{{ formatDateTime(contract.tenantSignTime) }}</div>
              </div>
              <el-button 
                v-else-if="isTenant && canSign" 
                type="success"
                @click="signContract('tenant')">
                我是租客，立即签署
              </el-button>
              <span v-else>未签署</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'

export default {
  name: 'ContractDetail',
  props: {
    contract: {
      type: Object,
      required: true
    },
    isOwner: {
      type: Boolean,
      default: false
    },
    isTenant: {
      type: Boolean,
      default: false
    },
    canSign: {
      type: Boolean,
      default: true
    },
    userInfo: {
      type: Object,
      default: () => ({})
    }
  },
  methods: {
    // 签署合同
    signContract(role) {
      this.$emit('sign', { role, contract: this.contract });
    },
    
    // 格式化日期
    formatDate(dateStr) {
      return dateStr ? moment(dateStr).format('YYYY年MM月DD日') : '';
    },
    
    // 格式化日期时间
    formatDateTime(dateStr) {
      return dateStr ? moment(dateStr).format('YYYY-MM-DD HH:mm:ss') : '未签署';
    },
    
    // 获取合同状态类型
    getContractStatusType(status) {
      const statusMap = {
        'pending': 'warning',
        'unsigned': 'info',
        'signed': 'success',
        'active': 'primary',
        'expired': 'warning',
        'terminated': 'danger',
        'cancelled': 'danger'
      };
      return statusMap[status] || 'info';
    },
    
    // 获取合同状态文本
    getContractStatusText(status) {
      const statusMap = {
        'pending': '待签署',
        'unsigned': '未签署',
        'signed': '已签署',
        'active': '生效中',
        'expired': '已过期',
        'terminated': '已终止',
        'cancelled': '已取消'
      };
      return statusMap[status] || status;
    }
  }
}
</script>

<style scoped>
.contract-detail {
  padding: 20px 0;
}

.contract-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.contract-header h2 {
  margin: 0;
}

.contract-info {
  margin-bottom: 20px;
}

.contract-content {
  border: 1px solid #eee;
  padding: 20px;
  margin-top: 20px;
  background-color: #fafafa;
}

.contract-body {
  padding: 30px;
  border: 1px solid #dcdfe6;
  background: #fff;
  min-height: 600px;
}

.contract-title {
  text-align: center;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 40px;
}

.contract-parties {
  margin-bottom: 30px;
}

.contract-terms h3 {
  font-size: 18px;
  margin: 25px 0 15px;
  color: #333;
}

.contract-terms p {
  line-height: 1.8;
  text-indent: 2em;
  margin-bottom: 10px;
}

.contract-signatures {
  margin-top: 60px;
  display: flex;
  justify-content: space-between;
}

.signature-area {
  width: 45%;
  text-align: center;
}

.signature-title {
  font-weight: bold;
  margin-bottom: 15px;
}

.signature-content {
  border: 1px dashed #ddd;
  padding: 20px;
  min-height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.sign-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.signature-name {
  font-size: 22px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #409EFF;
  font-family: "楷体", KaiTi, serif;
  padding: 10px 0;
}

.sign-time {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}

@media print {
  .contract-detail {
    padding: 0;
  }
  
  .contract-body {
    border: none;
    padding: 0;
  }
  
  button {
    display: none !important;
  }
}
</style> 