package com.house.pojo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单实体类
 */
@Data
public class Order {

    /**
     * 订单ID
     */
    private Integer id;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 房源ID
     */
    private Integer houseId;

    /**
     * 租客ID
     */
    private Integer tenantId;

    /**
     * 房东ID
     */
    private Integer ownerId;

    /**
     * 开始日期
     */
    private Date startDate;

    /**
     * 结束日期
     */
    private Date endDate;

    /**
     * 租期（月）
     */
    private Integer duration;

    /**
     * 月租金
     */
    private BigDecimal monthlyPrice;

    /**
     * 押金
     */
    private BigDecimal deposit;

    /**
     * 服务费
     */
    private BigDecimal serviceFee;

    /**
     * 总价
     */
    private BigDecimal totalPrice;

    /**
     * 订单状态：
     * application - 租房申请
     * approved - 申请已批准
     * contract_pending - 待签署合同
     * unpaid - 待支付
     * pending - 待入住
     * renting - 租赁中
     * completed - 已完成
     * cancelled - 已取消
     */
    private String status;
    
    /**
     * 订单备注
     */
    private String remark;

    /**
     * 押金支付时间
     */
    private Date depositPayTime;

    /**
     * 合同签署时间
     */
    private Date contractSignTime;

    /**
     * 入住时间
     */
    private Date checkInTime;

    /**
     * 是否已评价
     */
    private Boolean reviewed;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 房源地址（非数据库字段，用于前端显示）
     */
    private String houseAddress;
    
    /**
     * 房源所在区域（非数据库字段，用于前端显示）
     */
    private String houseArea;
    
    /**
     * 租客姓名（非数据库字段，用于前端显示）
     */
    private String tenantName;
    
    /**
     * 租客电话（非数据库字段，用于前端显示）
     */
    private String tenantPhone;

    /**
     * 房源信息（非数据库字段，用于前端显示）
     */
    private House house;

    /**
     * 房东信息（非数据库字段，用于前端显示）
     */
    private UserList owner;
} 