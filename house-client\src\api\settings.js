import request from '@/utils/request'
const api_name = 'settings'

export default {
    // 获取基本设置
    getBasicSettings() {
        return request({
            url: `/${api_name}/basic`,
            method: 'get'
        })
    },
    
    // 保存基本设置
    saveBasicSettings(data) {
        return request({
            url: `/${api_name}/basic`,
            method: 'post',
            data
        })
    },
    
    // 获取通知设置
    getNotificationSettings() {
        return request({
            url: `/${api_name}/notification`,
            method: 'get'
        })
    },
    
    // 保存通知设置
    saveNotificationSettings(data) {
        return request({
            url: `/${api_name}/notification`,
            method: 'post',
            data
        })
    },
    
    // 获取安全设置
    getSecuritySettings() {
        return request({
            url: `/${api_name}/security`,
            method: 'get'
        })
    },
    
    // 保存安全设置
    saveSecuritySettings(data) {
        return request({
            url: `/${api_name}/security`,
            method: 'post',
            data
        })
    },
    
    // 获取备份历史
    getBackupHistory() {
        return request({
            url: `/${api_name}/backup/history`,
            method: 'get'
        })
    },
    
    // 创建备份
    createBackup() {
        return request({
            url: `/${api_name}/backup/create`,
            method: 'post'
        })
    },
    
    // 恢复备份
    restoreBackup(backupId) {
        return request({
            url: `/${api_name}/backup/restore/${backupId}`,
            method: 'post'
        })
    },
    
    // 删除备份
    deleteBackup(backupId) {
        return request({
            url: `/${api_name}/backup/delete/${backupId}`,
            method: 'delete'
        })
    }
} 