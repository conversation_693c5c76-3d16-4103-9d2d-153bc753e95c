-- 给 house 表添加 view_count 字段
ALTER TABLE house 
ADD COLUMN view_count INT DEFAULT 0 COMMENT '点击量';

-- 初始化所有房源的点击量为随机值（用于测试，生产环境可以不执行此语句）
UPDATE house 
SET view_count = FLOOR(RAND() * 100);

-- 注意：确保没有同时使用 houselist 表，如果之前有引用可以执行下面的清理操作

-- 1. 查看是否有外键依赖 houselist 表
-- SELECT * FROM information_schema.KEY_COLUMN_USAGE 
-- WHERE REFERENCED_TABLE_NAME = 'houselist';

-- 2. 如果有外键依赖，先删除外键约束
-- ALTER TABLE 相关表 DROP FOREIGN KEY 约束名;

-- 3. 删除 houselist 表（如果确认不再使用）
-- DROP TABLE IF EXISTS houselist; 