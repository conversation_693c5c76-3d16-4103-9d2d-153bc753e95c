package com.house.dto;

import com.house.pojo.Contract;
import com.house.pojo.House;
import com.house.pojo.Order;
import com.house.pojo.UserList;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 合同数据传输对象
 * 包含合同详细信息以及关联的订单、房源、租客和房东信息
 */
@Data
public class ContractDTO {

    /**
     * 合同ID
     */
    private Integer id;

    /**
     * 合同编号
     */
    private String contractNo;

    /**
     * 关联的订单ID
     */
    private Integer orderId;
    
    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 合同内容
     */
    private String content;

    /**
     * 租客ID
     */
    private Integer tenantId;
    
    /**
     * 租客姓名
     */
    private String tenantName;
    
    /**
     * 租客电话
     */
    private String tenantPhone;

    /**
     * 房东ID
     */
    private Integer ownerId;
    
    /**
     * 房东姓名
     */
    private String ownerName;
    
    /**
     * 房东电话
     */
    private String ownerPhone;
    
    /**
     * 房源ID
     */
    private Integer houseId;
    
    /**
     * 房源标题/详情
     */
    private String houseDetail;
    
    /**
     * 房源地址
     */
    private String houseAddress;
    
    /**
     * 房源图片
     */
    private String houseImage;
    
    /**
     * 月租金
     */
    private BigDecimal monthlyPrice;
    
    /**
     * 合同开始日期
     */
    private Date startDate;
    
    /**
     * 合同结束日期
     */
    private Date endDate;
    
    /**
     * 租期（月）
     */
    private Integer duration;

    /**
     * 租客是否已签署
     */
    private Boolean tenantSigned;

    /**
     * 租客签署时间
     */
    private Date tenantSignTime;

    /**
     * 房东是否已签署
     */
    private Boolean ownerSigned;

    /**
     * 房东签署时间
     */
    private Date ownerSignTime;

    /**
     * 合同状态：
     * pending - 待签署
     * active - 生效中
     * expired - 已过期
     * cancelled - 已取消
     */
    private String status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 从Contract、Order、House、UserList构建ContractDTO
     */
    public static ContractDTO fromEntities(Contract contract, Order order, House house, UserList tenant, UserList owner) {
        ContractDTO dto = new ContractDTO();
        
        // 合同信息
        dto.setId(contract.getId());
        dto.setContractNo(contract.getContractNo());
        dto.setOrderId(contract.getOrderId());
        dto.setContent(contract.getContent());
        dto.setTenantId(contract.getTenantId());
        dto.setOwnerId(contract.getOwnerId());
        dto.setTenantSigned(contract.getTenantSigned());
        dto.setTenantSignTime(contract.getTenantSignTime());
        dto.setOwnerSigned(contract.getOwnerSigned());
        dto.setOwnerSignTime(contract.getOwnerSignTime());
        dto.setStatus(contract.getStatus());
        dto.setCreateTime(contract.getCreateTime());
        dto.setUpdateTime(contract.getUpdateTime());
        
        // 订单信息
        if (order != null) {
            dto.setOrderNo(order.getOrderNo());
            dto.setHouseId(order.getHouseId());
            dto.setMonthlyPrice(order.getMonthlyPrice());
            dto.setStartDate(order.getStartDate());
            dto.setEndDate(order.getEndDate());
            dto.setDuration(order.getDuration());
        }
        
        // 房源信息
        if (house != null) {
            dto.setHouseDetail(house.getDetail());
            dto.setHouseAddress(house.getAddress());
            dto.setHouseImage(house.getImageUrl());
        }
        
        // 租客信息
        if (tenant != null) {
            dto.setTenantName(tenant.getName());
            dto.setTenantPhone(tenant.getPhone());
        }
        
        // 房东信息
        if (owner != null) {
            dto.setOwnerName(owner.getName());
            dto.setOwnerPhone(owner.getPhone());
        }
        
        return dto;
    }
} 