<template>
  <div class="house-detail-container page-container has-fixed-header">
    <!-- 使用Header组件 -->
    <frontend-header :active-index="activeIndex"></frontend-header>

    <div class="page-wrapper">
      <!-- 面包屑导航 -->
      <div class="breadcrumb-container">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/frontend/houselist' }">房源列表</el-breadcrumb-item>
          <el-breadcrumb-item>{{ house.address }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>

      <!-- 房源详情 -->
      <div class="detail-wrapper" v-if="house">
        <div class="detail-content">
          <div class="left-section">
            <!-- 房源图片轮播 -->
            <div class="house-images card-shadow">
              <el-carousel :interval="4000" height="400px" indicator-position="outside" arrow="always" type="card">
                <el-carousel-item v-for="(image, index) in houseImages" :key="index">
                  <div class="carousel-image-container">
                    <img :src="image" class="carousel-image" alt="房源图片" />
                  </div>
                </el-carousel-item>
              </el-carousel>
            </div>

            <!-- 房源基本信息 -->
            <div class="basic-info card-shadow">
              <h1 class="house-title">{{ house.address }}</h1>
              <div class="price-status">
                <span class="price">¥{{ house.price }} <small>元/月</small></span>
                <el-tag :type="house.status === '可租' ? 'success' : 'info'" effect="dark" size="medium">{{ house.status }}</el-tag>
              </div>
              <div class="house-tags">
                <el-tag effect="plain" size="small" type="primary">整租</el-tag>
                <el-tag effect="plain" size="small" type="success">精装修</el-tag>
                <el-tag effect="plain" size="small" type="warning">拎包入住</el-tag>
                <el-tag effect="plain" size="small" type="danger">近地铁</el-tag>
              </div>
              <div class="view-count">
                <i class="el-icon-view"></i> 浏览量: {{ house.viewCount || 0 }}
              </div>
            </div>

            <!-- 快捷操作 -->
            <div class="quick-actions card-shadow">
              <el-button type="primary" icon="el-icon-s-home" @click="handleReserve">预约看房</el-button>
              <el-button type="danger" icon="el-icon-star-off">收藏</el-button>
              <el-button type="success" icon="el-icon-share">分享</el-button>
              <el-button type="warning" icon="el-icon-document" @click="applyForRent" v-if="house.status === '可租'">申请租房</el-button>
              <el-button type="info" icon="el-icon-chat-line-round" @click="openChatWithLandlord">咨询房东</el-button>
            </div>

            <!-- 房源详细信息 -->
            <div class="info-section card-shadow">
              <h2 class="section-title"><i class="el-icon-info"></i> 房屋信息</h2>
              <el-divider></el-divider>
              <div class="info-content">
                <div class="info-grid">
                  <div class="info-item">
                    <span class="info-label">房屋类型</span>
                    <span class="info-value">{{ house.detail.split('，')[0] }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">面积</span>
                    <span class="info-value">{{ house.detail.split('，')[1] }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">装修情况</span>
                    <span class="info-value">{{ house.detail.split('，')[2] }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">其他特点</span>
                    <span class="info-value">{{ house.detail.split('，')[3] || '无' }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 房源描述 -->
            <div class="info-section card-shadow">
              <h2 class="section-title"><i class="el-icon-document"></i> 房屋描述</h2>
              <el-divider></el-divider>
              <div class="description">
                <p>这套房子位于{{ house.address }}，{{ house.detail }}。周边配套设施完善，交通便利，是居住的理想选择。</p>
                <h3><i class="el-icon-medal-1"></i> 房源亮点：</h3>
                <ul>
                  <li><i class="el-icon-location"></i> 交通便利：距离公交站/地铁站步行仅需5分钟</li>
                  <li><i class="el-icon-shopping-cart-full"></i> 生活便利：周边有超市、菜市场、餐饮店</li>
                  <li><i class="el-icon-umbrella"></i> 环境优美：小区绿化率高，安静舒适</li>
                  <li><i class="el-icon-lock"></i> 安全可靠：24小时保安值守，门禁系统</li>
                </ul>
                <p class="highlight-text">如果您对这套房源感兴趣，请立即联系我们预约看房！</p>
              </div>
            </div>

            <!-- 周边配套 -->
            <div class="info-section card-shadow">
              <h2 class="section-title"><i class="el-icon-map-location"></i> 周边配套</h2>
              <el-divider></el-divider>
              <div class="surroundings">
                <div class="surrounding-grid">
                  <div class="surrounding-item">
                    <div class="surrounding-icon">
                      <i class="el-icon-school"></i>
                    </div>
                    <div class="surrounding-info">
                      <h4>教育</h4>
                      <p>附近有多所学校，包括幼儿园、小学和中学，为不同年龄段的学生提供教育服务。</p>
                    </div>
                  </div>
                  <div class="surrounding-item">
                    <div class="surrounding-icon">
                      <i class="el-icon-shopping-cart-full"></i>
                    </div>
                    <div class="surrounding-info">
                      <h4>购物</h4>
                      <p>周边有大型超市和商场，日常购物非常方便。</p>
                    </div>
                  </div>
                  <div class="surrounding-item">
                    <div class="surrounding-icon">
                      <i class="el-icon-truck"></i>
                    </div>
                    <div class="surrounding-info">
                      <h4>交通</h4>
                      <p>公交站点和地铁站近在咫尺，方便出行。</p>
                    </div>
                  </div>
                  <div class="surrounding-item">
                    <div class="surrounding-icon">
                      <i class="el-icon-basketball"></i>
                    </div>
                    <div class="surrounding-info">
                      <h4>休闲</h4>
                      <p>附近有公园和健身场所，闲暇时可以放松身心。</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 房客评价 -->
            <div class="info-section card-shadow">
              <h2 class="section-title"><i class="el-icon-chat-line-square"></i> 房客评价</h2>
              <el-divider></el-divider>
              <review-list :house-id="houseId"></review-list>
            </div>
          </div>

          <div class="right-section">
            <!-- 联系信息 -->
            <div class="contact-card">
              <h3>预约看房</h3>
              <div class="contact-form">
                <el-form ref="form" :model="contactForm" label-width="80px" :rules="contactRules">
                  <el-form-item label="姓名" prop="name">
                    <el-input v-model="contactForm.name" placeholder="请输入您的姓名" prefix-icon="el-icon-user"></el-input>
                  </el-form-item>
                  <el-form-item label="手机号" prop="phone">
                    <el-input v-model="contactForm.phone" placeholder="请输入您的手机号" prefix-icon="el-icon-mobile-phone"></el-input>
                  </el-form-item>
                  <el-form-item label="看房时间" prop="date">
                    <el-date-picker v-model="contactForm.date" type="date" placeholder="选择日期" style="width: 100%;" value-format="yyyy-MM-dd"></el-date-picker>
                  </el-form-item>
                  <el-form-item label="留言" prop="message">
                    <el-input type="textarea" v-model="contactForm.message" placeholder="您可以在这里留言，告诉我们您的需求"></el-input>
                  </el-form-item>
                  <el-form-item>
                    <el-button type="primary" @click="submitContactForm" class="submit-button" icon="el-icon-s-claim">立即预约看房</el-button>
                  </el-form-item>
                </el-form>
              </div>
            </div>

            <!-- 猜你喜欢 -->
            <div class="similar-houses">
              <h3><i class="el-icon-star-on"></i> 猜你喜欢</h3>
              <div class="similar-list">
                <div class="similar-item" v-for="item in similarHouses" :key="item.houseId" @click="goToHouseDetail(item.houseId)">
                  <div class="similar-img" :style="{ backgroundImage: `url(${item.imageUrl || require('../assets/showcase.jpg')})` }"></div>
                  <div class="similar-info">
                    <div class="similar-title">{{ item.address }}</div>
                    <div class="similar-price">¥{{ item.price }} <small>元/月</small></div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 二维码分享 -->
            <div class="qr-share">
              <h3><i class="el-icon-share"></i> 分享房源</h3>
              <div class="qr-content">
                <div class="qr-code">
                  <!-- 这里可以替换为真实的二维码图片 -->
                  <img src="../assets/showcase.jpg" alt="分享二维码" class="qr-img">
                </div>
                <p class="qr-tip">扫描二维码分享给朋友</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 使用Footer组件 -->
      <frontend-footer></frontend-footer>
      
      <!-- 咨询房东聊天组件 -->
      <chat-with-landlord 
        v-if="house && house.ownerId"
        :house-id="houseId" 
        :landlord-id="house.ownerId"
        :landlord-avatar="landlordAvatar"
        ref="chatComponent"
      ></chat-with-landlord>
    </div>
  </div>
</template>

<script>
// 导入组件
import FrontendHeader from './components/Header.vue';
import FrontendFooter from './components/Footer.vue';
import ReviewList from './components/ReviewList.vue';
import ChatWithLandlord from './components/ChatWithLandlord.vue';
import houseApi from './api/house';
import { getImageUrl, processHouseImage, processHouseListImages } from '@/utils/imageUtils';
import { getUser } from '@/utils/auth';

export default {
  name: "HouseDetail",
  // 注册组件
  components: {
    FrontendHeader,
    FrontendFooter,
    ReviewList,
    ChatWithLandlord
  },
  data() {
    return {
      activeIndex: '/frontend/houses',
      houseId: null,
      // 房屋数据
      house: {
        houseId: 1,
        address: '加载中...',
        price: 0,
        status: '未知',
        detail: '加载中...',
        viewCount: 0,
        imageUrl: '',
        ownerId: null // 房东ID
      },
      // 房屋图片
      houseImages: [
        require('../assets/showcase.jpg')
      ],
      // 联系表单
      contactForm: {
        name: '',
        phone: '',
        date: '',
        message: ''
      },
      // 表单验证规则
      contactRules: {
        name: [
          { required: true, message: '请输入您的姓名', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入您的手机号', trigger: 'blur' },
          { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        date: [
          { required: true, message: '请选择看房日期', trigger: 'change' }
        ]
      },
      // 类似房源
      similarHouses: [],
      // 房东头像
      landlordAvatar: require('../assets/houselogo.png')
    };
  },
  created() {
    // 从路由参数获取房源ID
    this.houseId = this.$route.params.id;
    console.log('HouseDetail.vue - 页面创建，获取到路由参数ID:', this.houseId);
    
    // 获取房源详细信息
    this.getHouseDetail();
  },
  methods: {
    // 获取房源详细信息
    getHouseDetail() {
      console.log('HouseDetail.vue - 开始加载房源详情，ID:', this.houseId);
      
      // 调用API获取房源详情
      houseApi.getHouseDetail(this.houseId)
        .then(response => {
          if (response.data && response.data.code === 20000 && response.data.data) {
            // 处理房源数据和图片路径
            this.house = processHouseImage(response.data.data);
            
            // 更新页面标题
            document.title = `${this.house.address} - 房源详情`;
            
            // 处理轮播图片
            this.updateHouseImages();
            
            // 增加点击量
            this.increaseViewCount();
            
            // 获取类似房源
            this.getSimilarHouses();
            
            // 获取房东信息
            if (this.house.ownerId) {
              this.getLandlordInfo(this.house.ownerId);
            }
          } else {
            this.$message.error('获取房源详情失败');
            console.error('获取房源详情失败:', response.data);
          }
        })
        .catch(error => {
          this.$message.error('获取房源详情出错');
          console.error('获取房源详情出错:', error);
        });
    },
    
    // 获取房东信息
    getLandlordInfo(userId) {
      // 调用API获取房东信息
      houseApi.getUserInfo(userId)
        .then(response => {
          if (response.data && response.data.code === 20000 && response.data.data) {
            const landlord = response.data.data;
            if (landlord.avatar) {
              this.landlordAvatar = landlord.avatar;
            }
          }
        })
        .catch(error => {
          console.error('获取房东信息失败:', error);
        });
    },
    
    // 处理房源图片，生成轮播图数组
    updateHouseImages() {
      // 如果有图片，则使用该图片，否则使用默认图片
      if (this.house.imageUrl) {
        this.houseImages = [this.house.imageUrl];
      } else {
        this.houseImages = [require('../assets/showcase.jpg')];
      }
      
      // 添加一些默认图片以展示轮播效果（实际应用中应该从后端获取多张图片）
      this.houseImages.push(require('../assets/bg.jpg'));
      this.houseImages.push(require('../assets/showcase.jpg'));
    },
    
    // 增加房源浏览量
    increaseViewCount() {
      houseApi.increaseViewCount(this.houseId)
        .then(() => {
          console.log('增加点击量成功');
        })
        .catch(error => {
          console.error('增加点击量失败:', error);
        });
    },
    
    // 获取类似房源
    getSimilarHouses() {
      // 这里应该根据房源特点获取类似房源，这里简单实现为获取热门房源
      houseApi.getHotHouses(3)
        .then(response => {
          if (response.data && response.data.code === 20000) {
            let houses = response.data.data || [];
            // 过滤掉当前房源
            houses = houses.filter(h => h.houseId != this.houseId);
            this.similarHouses = processHouseListImages(houses);
          }
        })
        .catch(error => {
          console.error('获取类似房源失败:', error);
        });
    },
    
    // 提交预约看房表单
    submitContactForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 在真实应用中，这里应该调用API提交预约信息
          this.$message({
            message: '预约看房请求已提交，我们将尽快与您联系！',
            type: 'success'
          });
          // 重置表单
          this.$refs.form.resetFields();
        } else {
          return false;
        }
      });
    },
    
    // 跳转到其他房源详情页
    goToHouseDetail(houseId) {
      if (houseId === this.houseId) return;
      
      // 使用replace而不是push，以避免浏览器历史堆积
      this.$router.replace({
        name: 'FrontendHouseDetail',
        params: { id: houseId }
      });
      
      // 重新加载页面数据
      this.houseId = houseId;
      this.getHouseDetail();
      
      // 滚动到页面顶部
      window.scrollTo(0, 0);
    },
    
    // 处理预约按钮点击
    handleReserve() {
      // 滚动到预约表单
      const formElement = this.$el.querySelector('.contact-form');
      if (formElement) {
        formElement.scrollIntoView({ behavior: 'smooth' });
      }
    },

    // 处理申请租房按钮点击
    applyForRent() {
      // 检查用户是否登录
      const userData = getUser();
      if (!userData || !userData.token) {
        this.$confirm('您需要登录后才能申请租房，是否立即登录？', '提示', {
          confirmButtonText: '去登录',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 保存当前页面URL，登录后可以跳回来
          localStorage.setItem('redirectUrl', window.location.href);
          this.$router.push('/frontend/login');
        }).catch(() => {});
        return;
      }

      // 跳转到租房申请表单页面
      this.$router.push({
        path: `/frontend/apply/${this.houseId}`,
      });
    },
    
    // 打开与房东的聊天窗口
    openChatWithLandlord() {
      if (this.$refs.chatComponent) {
        this.$refs.chatComponent.toggleChat();
        this.$refs.chatComponent.isExpanded = true;
      }
    }
  }
};
</script>

<style scoped>
.page-wrapper {
  background-color: #f7f8fa;
  min-height: calc(100vh - 60px);
  padding-top: 20px;
  padding-bottom: 40px;
}

/* 面包屑导航 */
.breadcrumb-container {
  max-width: 1200px;
  margin: 0 auto 20px;
  padding: 0 15px;
}

/* 详情页布局 */
.detail-wrapper {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.detail-content {
  display: flex;
  justify-content: space-between;
  gap: 30px;
}

.left-section {
  flex: 1;
}

.right-section {
  width: 360px;
}

/* 通用卡片阴影 */
.card-shadow {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 20px;
  margin-bottom: 25px;
  transition: all 0.3s ease;
}

.card-shadow:hover {
  box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* 轮播图样式 */
.house-images {
  overflow: hidden;
  border-radius: 8px;
  padding: 0;
}

.carousel-image-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 8px;
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.el-carousel__item:hover .carousel-image {
  transform: scale(1.05);
}

/* 房源基本信息 */
.basic-info {
  text-align: left;
}

.house-title {
  font-size: 28px;
  color: #333;
  margin-bottom: 15px;
  font-weight: bold;
}

.price-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.price {
  font-size: 28px;
  color: #ff5a5f;
  font-weight: bold;
}

.price small {
  font-size: 14px;
  font-weight: normal;
}

.house-tags {
  margin-bottom: 15px;
}

.house-tags .el-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

.view-count {
  color: #909399;
  font-size: 14px;
  margin-top: 10px;
}

/* 快捷操作 */
.quick-actions {
  display: flex;
  justify-content: space-around;
  gap: 10px;
}

.quick-actions .el-button {
  flex: 1;
}

/* 房源详细信息 */
.section-title {
  font-size: 20px;
  color: #333;
  font-weight: bold;
  margin-bottom: 0;
}

.section-title i {
  margin-right: 8px;
  color: #409EFF;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.info-item {
  margin-bottom: 10px;
  padding: 15px;
  border-radius: 8px;
  background-color: #f8f9fb;
}

.info-label {
  display: block;
  color: #909399;
  margin-bottom: 5px;
}

.info-value {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

/* 房屋描述 */
.description {
  line-height: 1.8;
}

.description h3 {
  font-size: 18px;
  margin: 20px 0 10px;
  color: #333;
}

.description ul {
  padding-left: 0;
  list-style: none;
}

.description li {
  padding: 8px 0;
  position: relative;
}

.description li i {
  color: #409EFF;
  margin-right: 8px;
}

.highlight-text {
  padding: 15px;
  background-color: #f0f9eb;
  border-left: 4px solid #67c23a;
  color: #67c23a;
  margin-top: 20px;
  border-radius: 4px;
}

/* 周边配套 */
.surrounding-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}

.surrounding-item {
  display: flex;
  align-items: flex-start;
  padding: 15px;
  background-color: #f8f9fb;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.surrounding-item:hover {
  background-color: #edf6ff;
  transform: translateY(-3px);
}

.surrounding-icon {
  width: 50px;
  height: 50px;
  background-color: rgba(64, 158, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.surrounding-icon i {
  font-size: 24px;
  color: #409EFF;
}

.surrounding-info h4 {
  margin: 0 0 8px;
  color: #333;
}

.surrounding-info p {
  color: #606266;
  line-height: 1.6;
  margin: 0;
  font-size: 14px;
}

/* 联系卡片 */
.contact-card {
  background: linear-gradient(to bottom right, #ffffff, #f9fafc);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  padding: 25px;
  margin-bottom: 25px;
  position: relative;
  overflow: hidden;
}

.contact-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to right, #409EFF, #67c23a);
}

.contact-card h3 {
  font-size: 20px;
  margin-bottom: 25px;
  color: #333;
  text-align: center;
}

.submit-button {
  width: 100%;
  font-size: 16px;
  padding: 12px 20px;
}

/* 猜你喜欢 */
.similar-houses {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 25px;
}

.similar-houses h3 {
  font-size: 18px;
  margin-bottom: 20px;
  text-align: center;
  color: #333;
}

.similar-houses h3 i {
  color: #ff5a5f;
  margin-right: 5px;
}

.similar-item {
  display: flex;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
  cursor: pointer;
  transition: all 0.3s ease;
}

.similar-item:hover {
  background-color: #f8f9fb;
  transform: translateX(5px);
  border-radius: 6px;
  padding: 10px;
}

.similar-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.similar-img {
  width: 80px;
  height: 80px;
  background-size: cover;
  background-position: center;
  margin-right: 15px;
  border-radius: 6px;
}

.similar-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.similar-title {
  font-size: 14px;
  color: #303133;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.similar-price {
  color: #ff5a5f;
  font-size: 16px;
  font-weight: bold;
}

/* 二维码分享 */
.qr-share {
  background-color: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  text-align: center;
}

.qr-share h3 {
  font-size: 18px;
  margin-bottom: 20px;
  color: #333;
}

.qr-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qr-code {
  width: 150px;
  height: 150px;
  margin-bottom: 10px;
}

.qr-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.qr-tip {
  color: #909399;
  font-size: 14px;
}

/* 响应式调整 */
@media screen and (max-width: 992px) {
  .detail-content {
    flex-direction: column;
  }

  .left-section {
    margin-right: 0;
    margin-bottom: 20px;
  }

  .right-section {
    width: 100%;
  }
  
  .info-grid, .surrounding-grid {
    grid-template-columns: 1fr;
  }
}

@media screen and (max-width: 768px) {
  .house-title {
    font-size: 24px;
  }
  
  .price {
    font-size: 24px;
  }
}

@media screen and (max-width: 576px) {
  .quick-actions {
    flex-direction: column;
  }
}
</style> 