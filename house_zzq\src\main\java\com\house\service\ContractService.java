package com.house.service;

import com.github.pagehelper.PageInfo;
import com.house.dto.ContractDTO;
import com.house.pojo.Contract;

import java.util.Map;

/**
 * 合同服务接口
 */
public interface ContractService {

    /**
     * 创建合同
     */
    Contract createContract(Contract contract);

    /**
     * 为订单创建合同
     */
    Contract createContractForOrder(Integer orderId);

    /**
     * 根据合同编号获取合同
     */
    Contract getContractByContractNo(String contractNo);

    /**
     * 根据ID获取合同
     */
    Contract getContractById(Integer id);

    /**
     * 根据订单ID获取合同
     */
    Contract getContractByOrderId(Integer orderId);

    /**
     * 租客签署合同
     */
    boolean signContractByTenant(Integer contractId, Integer tenantId, String tenantSignature);

    /**
     * 房东签署合同
     */
    boolean signContractByOwner(Integer contractId, Integer ownerId, String ownerSignature);

    /**
     * 更新合同状态
     */
    boolean updateContractStatus(Integer contractId, String status);

    /**
     * 取消合同
     */
    boolean cancelContract(Integer contractId);

    /**
     * 获取合同列表
     */
    PageInfo<Contract> getContractList(Map<String, Object> params);
    
    /**
     * 获取详细合同信息列表
     */
    PageInfo<ContractDTO> getContractDTOList(Map<String, Object> params);
    
    /**
     * 根据合同ID获取详细合同信息
     */
    ContractDTO getContractDTOById(Integer id);
    
    /**
     * 根据合同编号获取详细合同信息
     */
    ContractDTO getContractDTOByContractNo(String contractNo);
    
    /**
     * 根据订单ID获取详细合同信息
     */
    ContractDTO getContractDTOByOrderId(Integer orderId);
} 