/**
 * 处理图片路径，确保可以正确显示
 * @param {String} imageUrl 图片URL
 * @return {String} 处理后的图片URL
 */
export function getImageUrl(imageUrl) {
  if (!imageUrl) {
    // 如果没有图片，返回默认图片
    return require('../assets/showcase.jpg');
  }
  
  // 如果是完整URL（以http开头），直接返回
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    return imageUrl;
  }
  
  // 处理以/api/images/开头的路径 - 直接拼接主机名
  if (imageUrl.startsWith('/api/images/')) {
    return `http://localhost:9002${imageUrl}`;
  }
  
  // 如果是相对路径，拼接后端地址
  if (imageUrl.startsWith('/images/')) {
    // 修改为访问后端的/api/images路径
    return `http://localhost:9002/api/images${imageUrl.substring(8)}`;
  }
  
  // 如果只有文件名，拼接完整路径
  if (!imageUrl.includes('/')) {
    return `http://localhost:9002/api/images/${imageUrl}`;
  }
  
  // 其他情况，尝试作为相对路径处理
  return `http://localhost:9002${imageUrl}`;
}

/**
 * 为房源数据处理图片路径
 * @param {Object} house 房源数据
 * @return {Object} 处理后的房源数据
 */
export function processHouseImage(house) {
  if (house && house.imageUrl) {
    house.imageUrl = getImageUrl(house.imageUrl);
  }
  return house;
}

/**
 * 为房源列表数据处理图片路径
 * @param {Array} houses 房源列表数据
 * @return {Array} 处理后的房源列表数据
 */
export function processHouseListImages(houses) {
  if (!houses || !Array.isArray(houses)) {
    return houses;
  }
  
  return houses.map(house => processHouseImage(house));
} 