<template>
  <div class="user-list-container">
    <el-card shadow="hover" class="box-card">
      <div slot="header" class="clearfix">
        <span>用户管理</span>
        <el-button 
          style="float: right; padding: 3px 0" 
          type="text"
          @click="refreshUserList">
          <i class="el-icon-refresh"></i> 刷新
        </el-button>
      </div>
      
      <!-- 搜索和过滤 -->
      <div class="filter-container">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="用户名">
            <el-input v-model="searchForm.username" placeholder="用户名" clearable></el-input>
          </el-form-item>
          <el-form-item label="真实姓名">
            <el-input v-model="searchForm.realName" placeholder="真实姓名" clearable></el-input>
          </el-form-item>
          <el-form-item label="角色">
            <el-select v-model="searchForm.role" placeholder="角色" clearable>
              <el-option label="管理员" value="admin"></el-option>
              <el-option label="房东" value="owner"></el-option>
              <el-option label="租客" value="tenant"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
        
        <div class="operation-container">
          <el-button type="primary" icon="el-icon-plus" @click="handleAddUser">添加用户</el-button>
          <el-button type="danger" icon="el-icon-delete" :disabled="selectedUsers.length === 0" @click="handleBatchDelete">批量删除</el-button>
        </div>
      </div>
      
      <!-- 用户表格 -->
      <el-table
        v-loading="loading"
        :data="userList"
        border
        style="width: 100%"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column label="头像" width="80">
          <template slot-scope="scope">
            <el-avatar :size="40" :src="scope.row.avatar || defaultAvatar"></el-avatar>
          </template>
        </el-table-column>
        <el-table-column prop="username" label="用户名" width="120"></el-table-column>
        <el-table-column prop="realName" label="真实姓名" width="120"></el-table-column>
        <el-table-column prop="email" label="邮箱" width="180"></el-table-column>
        <el-table-column prop="phone" label="手机号" width="130"></el-table-column>
        <el-table-column prop="role" label="角色" width="100">
          <template slot-scope="scope">
            <el-tag :type="getRoleTagType(scope.row.role)">
              {{ getRoleName(scope.row.role) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-switch
              v-model="scope.row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleStatusChange(scope.row)"
              :disabled="scope.row.role === 'admin' && isCurrentUser(scope.row.id)">
            </el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="注册时间" width="180">
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="200">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              icon="el-icon-edit"
              @click="handleEditUser(scope.row)">编辑</el-button>
            <el-button
              size="mini"
              type="danger"
              icon="el-icon-delete"
              @click="handleDeleteUser(scope.row)"
              :disabled="scope.row.role === 'admin' && isCurrentUser(scope.row.id)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pagination.currentPage"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total">
        </el-pagination>
      </div>
    </el-card>
    
    <!-- 添加/编辑用户对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="500px" @close="resetUserForm">
      <el-form :model="userForm" :rules="userFormRules" ref="userForm" label-width="100px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" :disabled="editMode"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password" v-if="!editMode">
          <el-input v-model="userForm.password" show-password></el-input>
        </el-form-item>
        <el-form-item label="真实姓名" prop="realName">
          <el-input v-model="userForm.realName"></el-input>
        </el-form-item>
        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="userForm.phone"></el-input>
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" placeholder="请选择角色">
            <el-option label="管理员" value="admin"></el-option>
            <el-option label="房东" value="owner"></el-option>
            <el-option label="租客" value="tenant"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="头像">
          <el-upload
            class="avatar-uploader"
            action="#"
            :http-request="uploadAvatar"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload">
            <img v-if="userForm.avatar" :src="userForm.avatar" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
        </el-form-item>
        <el-form-item label="状态">
          <el-switch
            v-model="userForm.status"
            :active-value="1"
            :inactive-value="0"
            active-text="启用"
            inactive-text="禁用">
          </el-switch>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitUserForm" :loading="submitLoading">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getBackstageUserInfo } from '@/utils/auth'
import moment from 'moment'

export default {
  name: 'UserList',
  data() {
    return {
      loading: false,
      submitLoading: false,
      userList: [],
      selectedUsers: [],
      defaultAvatar: require('@/assets/showcase.jpg'),
      searchForm: {
        username: '',
        realName: '',
        role: ''
      },
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      dialogVisible: false,
      dialogTitle: '添加用户',
      editMode: false,
      userForm: {
        id: null,
        username: '',
        password: '',
        realName: '',
        email: '',
        phone: '',
        role: 'tenant',
        avatar: '',
        status: 1
      },
      userFormRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
        ],
        realName: [
          { required: true, message: '请输入真实姓名', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ],
        role: [
          { required: true, message: '请选择角色', trigger: 'change' }
        ]
      },
      currentUserId: null
    }
  },
  created() {
    // 获取当前用户ID
    const userInfo = getBackstageUserInfo()
    if (userInfo && userInfo.id) {
      this.currentUserId = userInfo.id
    }
    
    this.fetchUserList()
  },
  methods: {
    // 获取用户列表
    async fetchUserList() {
      this.loading = true
      try {
        const params = {
          page: this.pagination.currentPage,
          limit: this.pagination.pageSize,
          ...this.searchForm
        }
        
        const response = await this.$http.get('/admin/users', { params })
        
        if (response.data && response.data.flag) {
          this.userList = response.data.data.list || []
          this.pagination.total = response.data.data.total || 0
        } else {
          this.$message.error('获取用户列表失败')
        }
      } catch (error) {
        console.error('获取用户列表失败:', error)
        this.$message.error('获取用户列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 刷新用户列表
    refreshUserList() {
      this.fetchUserList()
    },
    
    // 处理搜索
    handleSearch() {
      this.pagination.currentPage = 1
      this.fetchUserList()
    },
    
    // 重置搜索
    resetSearch() {
      this.searchForm = {
        username: '',
        realName: '',
        role: ''
      }
      this.handleSearch()
    },
    
    // 处理表格选择变化
    handleSelectionChange(val) {
      this.selectedUsers = val
    },
    
    // 处理添加用户
    handleAddUser() {
      this.dialogTitle = '添加用户'
      this.editMode = false
      this.dialogVisible = true
    },
    
    // 处理编辑用户
    handleEditUser(row) {
      this.dialogTitle = '编辑用户'
      this.editMode = true
      this.userForm = { ...row }
      this.dialogVisible = true
    },
    
    // 处理删除用户
    handleDeleteUser(row) {
      this.$confirm('确认删除该用户?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteUser(row.id)
      }).catch(() => {})
    },
    
    // 删除用户
    async deleteUser(id) {
      try {
        const response = await this.$http.delete(`/admin/users/${id}`)
        
        if (response.data && response.data.flag) {
          this.$message.success('删除用户成功')
          this.fetchUserList()
        } else {
          this.$message.error(response.data.message || '删除用户失败')
        }
      } catch (error) {
        console.error('删除用户失败:', error)
        this.$message.error('删除用户失败')
      }
    },
    
    // 处理批量删除
    handleBatchDelete() {
      if (this.selectedUsers.length === 0) {
        return
      }
      
      // 检查是否包含当前管理员
      const hasCurrentAdmin = this.selectedUsers.some(user => 
        user.role === 'admin' && this.isCurrentUser(user.id)
      )
      
      if (hasCurrentAdmin) {
        this.$message.warning('不能删除当前登录的管理员账号')
        return
      }
      
      this.$confirm(`确认删除选中的 ${this.selectedUsers.length} 个用户?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.batchDeleteUsers()
      }).catch(() => {})
    },
    
    // 批量删除用户
    async batchDeleteUsers() {
      try {
        const ids = this.selectedUsers.map(user => user.id)
        const response = await this.$http.post('/admin/users/batch-delete', { ids })
        
        if (response.data && response.data.flag) {
          this.$message.success('批量删除用户成功')
          this.fetchUserList()
        } else {
          this.$message.error(response.data.message || '批量删除用户失败')
        }
      } catch (error) {
        console.error('批量删除用户失败:', error)
        this.$message.error('批量删除用户失败')
      }
    },
    
    // 处理状态变化
    async handleStatusChange(row) {
      try {
        const response = await this.$http.put(`/admin/users/${row.id}/status`, {
          status: row.status
        })
        
        if (response.data && response.data.flag) {
          this.$message.success(`用户状态已${row.status === 1 ? '启用' : '禁用'}`)
        } else {
          // 恢复原状态
          row.status = row.status === 1 ? 0 : 1
          this.$message.error(response.data.message || '更新用户状态失败')
        }
      } catch (error) {
        // 恢复原状态
        row.status = row.status === 1 ? 0 : 1
        console.error('更新用户状态失败:', error)
        this.$message.error('更新用户状态失败')
      }
    },
    
    // 提交用户表单
    submitUserForm() {
      this.$refs.userForm.validate(async valid => {
        if (valid) {
          this.submitLoading = true
          try {
            if (this.editMode) {
              // 更新用户
              await this.updateUser()
            } else {
              // 添加用户
              await this.addUser()
            }
          } finally {
            this.submitLoading = false
          }
        } else {
          return false
        }
      })
    },
    
    // 添加用户
    async addUser() {
      try {
        const response = await this.$http.post('/admin/users', this.userForm)
        
        if (response.data && response.data.flag) {
          this.$message.success('添加用户成功')
          this.dialogVisible = false
          this.fetchUserList()
        } else {
          this.$message.error(response.data.message || '添加用户失败')
        }
      } catch (error) {
        console.error('添加用户失败:', error)
        this.$message.error('添加用户失败')
      }
    },
    
    // 更新用户
    async updateUser() {
      try {
        const response = await this.$http.put(`/admin/users/${this.userForm.id}`, this.userForm)
        
        if (response.data && response.data.flag) {
          this.$message.success('更新用户成功')
          this.dialogVisible = false
          this.fetchUserList()
        } else {
          this.$message.error(response.data.message || '更新用户失败')
        }
      } catch (error) {
        console.error('更新用户失败:', error)
        this.$message.error('更新用户失败')
      }
    },
    
    // 上传头像
    async uploadAvatar(options) {
      const formData = new FormData()
      formData.append('file', options.file)
      
      try {
        const response = await this.$http.post('/upload/avatar', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        
        if (response.data && response.data.flag) {
          this.userForm.avatar = response.data.data
          options.onSuccess()
        } else {
          options.onError(new Error(response.data.message || '上传失败'))
        }
      } catch (error) {
        console.error('上传头像失败', error)
        options.onError(new Error('上传头像失败'))
      }
    },
    
    // 头像上传成功
    handleAvatarSuccess(res, file) {
      // 已在uploadAvatar中处理
    },
    
    // 头像上传前验证
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt20M = file.size / 1024 / 1024 < 20
      
      if (!isJPG) {
        this.$message.error('头像只能是JPG或PNG格式!')
      }
      if (!isLt20M) {
        this.$message.error('头像大小不能超过20MB!')
      }
      return isJPG && isLt20M
    },
    
    // 重置用户表单
    resetUserForm() {
      this.$refs.userForm && this.$refs.userForm.resetFields()
      this.userForm = {
        id: null,
        username: '',
        password: '',
        realName: '',
        email: '',
        phone: '',
        role: 'tenant',
        avatar: '',
        status: 1
      }
    },
    
    // 处理页大小变化
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.fetchUserList()
    },
    
    // 处理页码变化
    handleCurrentChange(val) {
      this.pagination.currentPage = val
      this.fetchUserList()
    },
    
    // 获取角色名称
    getRoleName(role) {
      const roleMap = {
        'admin': '管理员',
        'owner': '房东',
        'tenant': '租客'
      }
      return roleMap[role] || role
    },
    
    // 获取角色标签类型
    getRoleTagType(role) {
      const typeMap = {
        'admin': 'danger',
        'owner': 'success',
        'tenant': 'info'
      }
      return typeMap[role] || ''
    },
    
    // 格式化日期时间
    formatDateTime(dateTime) {
      return dateTime ? moment(dateTime).format('YYYY-MM-DD HH:mm:ss') : ''
    },
    
    // 是否是当前用户
    isCurrentUser(userId) {
      return userId === this.currentUserId
    }
  }
}
</script>

<style scoped>
.user-list-container {
  padding: 20px;
}

.filter-container {
  margin-bottom: 20px;
}

.operation-container {
  margin-top: 10px;
  display: flex;
  justify-content: flex-start;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.avatar-uploader {
  text-align: center;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  display: inline-block;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.avatar {
  width: 100px;
  height: 100px;
  display: block;
  object-fit: cover;
}
</style> 