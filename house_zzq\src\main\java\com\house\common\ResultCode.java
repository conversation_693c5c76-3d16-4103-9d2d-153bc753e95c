package com.house.common;

/**
 * 返回码常量类
 */
public class ResultCode {
    
    /**
     * 成功
     */
    public static final Integer SUCCESS = 20000;
    
    /**
     * 失败
     */
    public static final Integer ERROR = 20001;
    
    /**
     * 未登录
     */
    public static final Integer UNAUTHORIZED = 20002;
    
    /**
     * 无权限
     */
    public static final Integer FORBIDDEN = 20003;
    
    /**
     * 参数错误
     */
    public static final Integer PARAM_ERROR = 20004;
} 