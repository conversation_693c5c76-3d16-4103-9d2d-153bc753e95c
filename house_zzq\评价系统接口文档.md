# 房屋租赁平台 - 评价系统接口文档

## 📋 概述

本文档详细描述了房屋租赁平台中评价系统的所有API接口，包括评价的增删改查、统计信息、房东回复等功能。

## 🏗️ 数据库表结构

### 主要表
- `tb_review` - 评价主表
- `tb_review_stats` - 评价统计表
- `tb_review_tag_stats` - 评价标签统计表

### Review实体字段
```java
public class Review {
    private Integer id;              // 评价ID
    private Integer orderId;         // 订单ID
    private String orderNo;          // 订单编号
    private Integer houseId;         // 房屋ID
    private Integer ownerId;         // 房东ID
    private Integer userId;          // 评价用户ID
    private String userName;         // 评价用户名
    private String userAvatar;       // 用户头像
    private Double rating;           // 总体评分（1-5分）
    private Double locationRating;   // 位置便利性评分
    private Double facilityRating;   // 房屋设施评分
    private Double serviceRating;    // 房东服务评分
    private Double valueRating;      // 性价比评分
    private Double environmentRating; // 周边环境评分
    private Double cleanlinessRating; // 卫生情况评分
    private Double averageRating;    // 平均评分
    private String content;          // 评价内容
    private String tags;             // 评价标签，逗号分隔
    private String images;           // 评价图片，逗号分隔的URL
    private Boolean anonymous;       // 是否匿名评价
    private String reply;            // 房东回复
    private Date replyTime;          // 回复时间
    private Date createTime;         // 创建时间
    private Date updateTime;         // 更新时间
}
```

## 🔗 API接口列表

### 1. 提交评价

**接口地址：** `POST /reviews`

**请求参数：**
```json
{
    "orderId": 123,
    "orderNo": "ORD20250126001",
    "houseId": 456,
    "ownerId": 789,
    "rating": 4.5,
    "locationRating": 4.0,
    "facilityRating": 5.0,
    "serviceRating": 4.5,
    "valueRating": 4.0,
    "environmentRating": 4.5,
    "cleanlinessRating": 5.0,
    "content": "房子很不错，房东人很好",
    "tags": "交通便利,环境优美,设施齐全",
    "images": "image1.jpg,image2.jpg",
    "anonymous": false
}
```

**响应示例：**
```json
{
    "flag": true,
    "code": 20000,
    "message": "提交评价成功",
    "data": {
        "id": 1,
        "orderId": 123,
        "userId": 101,
        "userName": "张三",
        "rating": 4.5,
        "content": "房子很不错，房东人很好",
        "createTime": "2025-01-26 10:30:00"
    }
}
```

### 2. 获取房屋所有评价

**接口地址：** `GET /reviews/house/{houseId}`

**路径参数：**
- `houseId` - 房屋ID

**响应示例：**
```json
{
    "flag": true,
    "code": 20000,
    "message": "获取评价列表成功",
    "data": [
        {
            "id": 1,
            "orderId": 123,
            "houseId": 456,
            "ownerId": 789,
            "userId": 101,
            "userName": "张三",
            "userAvatar": "avatar1.jpg",
            "rating": 4.5,
            "locationRating": 4.0,
            "facilityRating": 5.0,
            "serviceRating": 4.5,
            "valueRating": 4.0,
            "environmentRating": 4.5,
            "cleanlinessRating": 5.0,
            "averageRating": 4.5,
            "content": "房子很不错，房东人很好",
            "tags": "交通便利,环境优美,设施齐全",
            "images": "image1.jpg,image2.jpg",
            "anonymous": false,
            "reply": "谢谢您的好评！",
            "replyTime": "2025-01-26 11:00:00",
            "createTime": "2025-01-26 10:30:00"
        }
    ]
}
```

### 3. 获取房屋评价统计信息

**接口地址：** `GET /reviews/stats/{houseId}`

**路径参数：**
- `houseId` - 房屋ID

**响应示例：**
```json
{
    "flag": true,
    "code": 20000,
    "message": "获取评价统计成功",
    "data": {
        "houseId": 456,
        "totalReviews": 25,
        "averageRating": 4.3,
        "locationRating": 4.2,
        "facilityRating": 4.5,
        "serviceRating": 4.4,
        "valueRating": 4.1,
        "environmentRating": 4.3,
        "cleanlinessRating": 4.6,
        "tags": [
            {
                "name": "交通便利",
                "count": 15
            },
            {
                "name": "环境优美",
                "count": 12
            },
            {
                "name": "设施齐全",
                "count": 10
            }
        ]
    }
}
```

### 4. 房东回复评价

**接口地址：** `POST /reviews/{reviewId}/reply`

**路径参数：**
- `reviewId` - 评价ID

**请求参数：**
```
reply=谢谢您的评价，我们会继续努力提供更好的服务！
```

**响应示例：**
```json
{
    "flag": true,
    "code": 20000,
    "message": "回复评价成功"
}
```

### 5. 获取房东的所有评价

**接口地址：** `GET /reviews/owner/{ownerId}`

**路径参数：**
- `ownerId` - 房东ID

**响应示例：**
```json
{
    "flag": true,
    "code": 20000,
    "message": "获取房东评价列表成功",
    "data": [
        {
            "id": 1,
            "orderId": 123,
            "orderNo": "ORD20250126001",
            "houseId": 456,
            "ownerId": 789,
            "userId": 101,
            "userName": "张三",
            "userAvatar": "avatar1.jpg",
            "rating": 4.5,
            "locationRating": 4.0,
            "facilityRating": 5.0,
            "serviceRating": 4.5,
            "valueRating": 4.0,
            "environmentRating": 4.5,
            "cleanlinessRating": 5.0,
            "averageRating": 4.5,
            "content": "房子很不错，房东人很好",
            "tags": "交通便利,环境优美,设施齐全",
            "images": "image1.jpg,image2.jpg",
            "anonymous": false,
            "reply": "谢谢您的好评！",
            "replyTime": "2025-01-26 11:00:00",
            "createTime": "2025-01-26 10:30:00"
        },
        {
            "id": 2,
            "orderId": 124,
            "orderNo": "ORD20250126002",
            "houseId": 457,
            "ownerId": 789,
            "userId": 102,
            "userName": "李四",
            "userAvatar": "avatar2.jpg",
            "rating": 4.0,
            "content": "整体还不错，性价比很高",
            "createTime": "2025-01-25 15:20:00"
        }
    ]
}
```

### 6. 检查订单是否已评价

**接口地址：** `GET /reviews/check/{orderId}`

**路径参数：**
- `orderId` - 订单ID

**响应示例：**
```json
{
    "flag": true,
    "code": 20000,
    "message": "查询成功",
    "data": true
}
```

## 🚨 缺失的接口

### ⚠️ 获取用户发布的所有评价

**当前状态：** 缺失此接口

**建议接口：** `GET /reviews/user/{userId}`

**功能说明：** 获取某个用户发布的所有评价，用于用户查看自己的评价历史。

**实现方案：**
1. 在 `ReviewMapper` 中添加 `selectReviewsByUserId` 方法
2. 在 `ReviewService` 中添加 `getReviewsByUserId` 方法
3. 在 `ReviewController` 中添加对应的接口

## 📊 相关接口

### 订单相关
- `PUT /orders/{orderNo}/complete` - 完成订单（可触发评价）
- `GET /orders` - 获取订单列表（包含是否已评价字段）

### 房源相关  
- `GET /house/gethousedetail?houseId={houseId}` - 获取房源详情
- `GET /houses/{houseId}` - 获取房源详情（RESTful风格）

## 🔧 技术实现

### 核心类
- `ReviewController` - 评价控制器
- `ReviewService` / `ReviewServiceImpl` - 评价服务层
- `ReviewMapper` - 评价数据访问层
- `Review` - 评价实体类

### 特性
- ✅ 多维度评分（6个维度）
- ✅ 标签评价系统
- ✅ 匿名评价支持
- ✅ 房东回复功能
- ✅ 评价统计信息
- ✅ 图片评价支持
- ✅ 事务处理
- ✅ 房东评价列表（已实现）
- ❌ 用户评价历史（需要补充）

## 📝 注意事项

1. **权限控制：** 当前接口缺少权限验证，建议添加JWT认证
2. **数据验证：** 需要添加评分范围验证（1-5分）
3. **重复评价：** 已有检查机制防止重复评价
4. **统计更新：** 评价提交时会自动更新统计信息
5. **标签处理：** 支持逗号分隔的多标签
6. **图片上传：** 需要配合文件上传接口使用

## 🔄 状态码说明

- `20000` - 成功
- `20002` - 参数错误  
- `50000` - 服务器内部错误

---

**最后更新：** 2025-01-26  
**版本：** v1.0
