package com.house.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.filter.CommonsRequestLoggingFilter;

/**
 * 请求日志配置类
 * 用于记录所有HTTP请求的详细信息
 */
@Configuration
public class RequestLoggingConfig {

    @Bean
    public CommonsRequestLoggingFilter requestLoggingFilter() {
        CommonsRequestLoggingFilter filter = new CommonsRequestLoggingFilter();
        filter.setIncludeQueryString(true);         // 包含查询参数
        filter.setIncludePayload(true);             // 包含请求体
        filter.setMaxPayloadLength(10000);          // 请求体最大长度
        filter.setIncludeHeaders(true);             // 包含请求头
        filter.setIncludeClientInfo(true);          // 包含客户端信息
        filter.setAfterMessagePrefix("请求数据: ");  // 日志前缀
        return filter;
    }
} 