{"name": "client", "version": "0.1.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "client", "version": "0.1.0", "dependencies": {"axios": "^0.18.0", "echarts": "^5.6.0", "element-theme-chalk": "^2.6.3", "element-ui": "^2.6.3", "html2canvas": "^1.4.1", "js-cookie": "^2.2.0", "jspdf": "^2.5.1", "jwt-decode": "^2.2.0", "moment": "^2.24.0", "vue": "^2.6.6", "vue-router": "^3.0.1", "vuex": "^3.1.0"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.5.0", "@vue/cli-service": "^3.5.0", "vue-template-compiler": "^2.5.21"}}, "node_modules/@babel/code-frame": {"version": "7.0.0", "resolved": "http://registry.npm.taobao.org/@babel/code-frame/download/@babel/code-frame-7.0.0.tgz", "integrity": "sha1-BuKrGb21NThVWaq7W6WXKUgoAPg=", "dev": true, "dependencies": {"@babel/highlight": "^7.0.0"}}, "node_modules/@babel/core": {"version": "7.4.0", "resolved": "http://registry.npm.taobao.org/@babel/core/download/@babel/core-7.4.0.tgz", "integrity": "sha1-JI/Wh0t9dVAQv+YfVXRh1PRG2ek=", "dev": true, "dependencies": {"@babel/code-frame": "^7.0.0", "@babel/generator": "^7.4.0", "@babel/helpers": "^7.4.0", "@babel/parser": "^7.4.0", "@babel/template": "^7.4.0", "@babel/traverse": "^7.4.0", "@babel/types": "^7.4.0", "convert-source-map": "^1.1.0", "debug": "^4.1.0", "json5": "^2.1.0", "lodash": "^4.17.11", "resolve": "^1.3.2", "semver": "^5.4.1", "source-map": "^0.5.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/generator": {"version": "7.4.0", "resolved": "http://registry.npm.taobao.org/@babel/generator/download/@babel/generator-7.4.0.tgz", "integrity": "sha1-wjDnlYmuenKf1GMbne1NwiBBgZY=", "dev": true, "dependencies": {"@babel/types": "^7.4.0", "jsesc": "^2.5.1", "lodash": "^4.17.11", "source-map": "^0.5.0", "trim-right": "^1.0.1"}}, "node_modules/@babel/helper-annotate-as-pure": {"version": "7.0.0", "resolved": "http://registry.npm.taobao.org/@babel/helper-annotate-as-pure/download/@babel/helper-annotate-as-pure-7.0.0.tgz", "integrity": "sha1-Mj053QtQ4Qx8Bsp9djjmhk2MXDI=", "dev": true, "dependencies": {"@babel/types": "^7.0.0"}}, "node_modules/@babel/helper-builder-binary-assignment-operator-visitor": {"version": "7.1.0", "resolved": "http://registry.npm.taobao.org/@babel/helper-builder-binary-assignment-operator-visitor/download/@babel/helper-builder-binary-assignment-operator-visitor-7.1.0.tgz", "integrity": "sha1-a2lijf5Ah3mODE7Zjj1Kay+9L18=", "dev": true, "dependencies": {"@babel/helper-explode-assignable-expression": "^7.1.0", "@babel/types": "^7.0.0"}}, "node_modules/@babel/helper-call-delegate": {"version": "7.4.0", "resolved": "http://registry.npm.taobao.org/@babel/helper-call-delegate/download/@babel/helper-call-delegate-7.4.0.tgz", "integrity": "sha1-8wjqvg1E9FEheFOu303qX2/jKU8=", "dev": true, "dependencies": {"@babel/helper-hoist-variables": "^7.4.0", "@babel/traverse": "^7.4.0", "@babel/types": "^7.4.0"}}, "node_modules/@babel/helper-create-class-features-plugin": {"version": "7.4.0", "resolved": "http://registry.npm.taobao.org/@babel/helper-create-class-features-plugin/download/@babel/helper-create-class-features-plugin-7.4.0.tgz", "integrity": "sha1-MP0JDgWdAhmVwXYqW3Z5j6C1HYI=", "dev": true, "dependencies": {"@babel/helper-function-name": "^7.1.0", "@babel/helper-member-expression-to-functions": "^7.0.0", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-replace-supers": "^7.4.0", "@babel/helper-split-export-declaration": "^7.4.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-define-map": {"version": "7.4.0", "resolved": "http://registry.npm.taobao.org/@babel/helper-define-map/download/@babel/helper-define-map-7.4.0.tgz", "integrity": "sha1-y/2MGy8ScI4mLCb2AM0W7Wo7xsk=", "dev": true, "dependencies": {"@babel/helper-function-name": "^7.1.0", "@babel/types": "^7.4.0", "lodash": "^4.17.11"}}, "node_modules/@babel/helper-explode-assignable-expression": {"version": "7.1.0", "resolved": "http://registry.npm.taobao.org/@babel/helper-explode-assignable-expression/download/@babel/helper-explode-assignable-expression-7.1.0.tgz", "integrity": "sha1-U3+hP28WdN90WwwA7I/k6ZaByPY=", "dev": true, "dependencies": {"@babel/traverse": "^7.1.0", "@babel/types": "^7.0.0"}}, "node_modules/@babel/helper-function-name": {"version": "7.1.0", "resolved": "http://registry.npm.taobao.org/@babel/helper-function-name/download/@babel/helper-function-name-7.1.0.tgz", "integrity": "sha1-oM6wFoX3M1XUNgwSR/WCv6/I/1M=", "dev": true, "dependencies": {"@babel/helper-get-function-arity": "^7.0.0", "@babel/template": "^7.1.0", "@babel/types": "^7.0.0"}}, "node_modules/@babel/helper-get-function-arity": {"version": "7.0.0", "resolved": "http://registry.npm.taobao.org/@babel/helper-get-function-arity/download/@babel/helper-get-function-arity-7.0.0.tgz", "integrity": "sha1-g1ctQyDipGVyY3NBE8QoaLZOScM=", "dev": true, "dependencies": {"@babel/types": "^7.0.0"}}, "node_modules/@babel/helper-hoist-variables": {"version": "7.4.0", "resolved": "http://registry.npm.taobao.org/@babel/helper-hoist-variables/download/@babel/helper-hoist-variables-7.4.0.tgz", "integrity": "sha1-JbYhOZriKYaTKXMKYgFbvrCm+9Y=", "dev": true, "dependencies": {"@babel/types": "^7.4.0"}}, "node_modules/@babel/helper-member-expression-to-functions": {"version": "7.0.0", "resolved": "http://registry.npm.taobao.org/@babel/helper-member-expression-to-functions/download/@babel/helper-member-expression-to-functions-7.0.0.tgz", "integrity": "sha1-jNFLCg33/wDwCefXpDaUX0fHoW8=", "dev": true, "dependencies": {"@babel/types": "^7.0.0"}}, "node_modules/@babel/helper-module-imports": {"version": "7.0.0", "resolved": "http://registry.npm.taobao.org/@babel/helper-module-imports/download/@babel/helper-module-imports-7.0.0.tgz", "integrity": "sha1-lggbcRHkhtpNLNlxrRpP4hbMLj0=", "dev": true, "dependencies": {"@babel/types": "^7.0.0"}}, "node_modules/@babel/helper-module-transforms": {"version": "7.2.2", "resolved": "http://registry.npm.taobao.org/@babel/helper-module-transforms/download/@babel/helper-module-transforms-7.2.2.tgz", "integrity": "sha1-qy+OjSMUCfg3DIg9IMM1GQKEuWM=", "dev": true, "dependencies": {"@babel/helper-module-imports": "^7.0.0", "@babel/helper-simple-access": "^7.1.0", "@babel/helper-split-export-declaration": "^7.0.0", "@babel/template": "^7.2.2", "@babel/types": "^7.2.2", "lodash": "^4.17.10"}}, "node_modules/@babel/helper-optimise-call-expression": {"version": "7.0.0", "resolved": "http://registry.npm.taobao.org/@babel/helper-optimise-call-expression/download/@babel/helper-optimise-call-expression-7.0.0.tgz", "integrity": "sha1-opIMVwKwc8Fd5REGIAqoytIEl9U=", "dev": true, "dependencies": {"@babel/types": "^7.0.0"}}, "node_modules/@babel/helper-plugin-utils": {"version": "7.0.0", "resolved": "http://registry.npm.taobao.org/@babel/helper-plugin-utils/download/@babel/helper-plugin-utils-7.0.0.tgz", "integrity": "sha1-u7P77phmHFaQNCN8wDlnupm08lA=", "dev": true}, "node_modules/@babel/helper-regex": {"version": "7.0.0", "resolved": "http://registry.npm.taobao.org/@babel/helper-regex/download/@babel/helper-regex-7.0.0.tgz", "integrity": "sha1-LBcYkjtX+bvmRwX/5WQKxk2b2yc=", "dev": true, "dependencies": {"lodash": "^4.17.10"}}, "node_modules/@babel/helper-remap-async-to-generator": {"version": "7.1.0", "resolved": "http://registry.npm.taobao.org/@babel/helper-remap-async-to-generator/download/@babel/helper-remap-async-to-generator-7.1.0.tgz", "integrity": "sha1-Nh2AghtvONp1vT8HheziCojF/n8=", "dev": true, "dependencies": {"@babel/helper-annotate-as-pure": "^7.0.0", "@babel/helper-wrap-function": "^7.1.0", "@babel/template": "^7.1.0", "@babel/traverse": "^7.1.0", "@babel/types": "^7.0.0"}}, "node_modules/@babel/helper-replace-supers": {"version": "7.4.0", "resolved": "http://registry.npm.taobao.org/@babel/helper-replace-supers/download/@babel/helper-replace-supers-7.4.0.tgz", "integrity": "sha1-T1attq7c1EnS2pOZwtzwVFRjtkw=", "dev": true, "dependencies": {"@babel/helper-member-expression-to-functions": "^7.0.0", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/traverse": "^7.4.0", "@babel/types": "^7.4.0"}}, "node_modules/@babel/helper-simple-access": {"version": "7.1.0", "resolved": "http://registry.npm.taobao.org/@babel/helper-simple-access/download/@babel/helper-simple-access-7.1.0.tgz", "integrity": "sha1-Ze65VMjCRb6qToWdphiPOdceWFw=", "dev": true, "dependencies": {"@babel/template": "^7.1.0", "@babel/types": "^7.0.0"}}, "node_modules/@babel/helper-split-export-declaration": {"version": "7.4.0", "resolved": "http://registry.npm.taobao.org/@babel/helper-split-export-declaration/download/@babel/helper-split-export-declaration-7.4.0.tgz", "integrity": "sha1-Vxv9UnAfSSkg1jt/c1Aw6aPhC1U=", "dev": true, "dependencies": {"@babel/types": "^7.4.0"}}, "node_modules/@babel/helper-wrap-function": {"version": "7.2.0", "resolved": "http://registry.npm.taobao.org/@babel/helper-wrap-function/download/@babel/helper-wrap-function-7.2.0.tgz", "integrity": "sha1-xOABJEV2nigVtVKW6tQ6lYVJ9vo=", "dev": true, "dependencies": {"@babel/helper-function-name": "^7.1.0", "@babel/template": "^7.1.0", "@babel/traverse": "^7.1.0", "@babel/types": "^7.2.0"}}, "node_modules/@babel/helpers": {"version": "7.4.2", "resolved": "http://registry.npm.taobao.org/@babel/helpers/download/@babel/helpers-7.4.2.tgz", "integrity": "sha1-O9+kalUsp371oPhVG+XwhFrpib4=", "dev": true, "dependencies": {"@babel/template": "^7.4.0", "@babel/traverse": "^7.4.0", "@babel/types": "^7.4.0"}}, "node_modules/@babel/highlight": {"version": "7.0.0", "resolved": "http://registry.npm.taobao.org/@babel/highlight/download/@babel/highlight-7.0.0.tgz", "integrity": "sha1-9xDDjI1Fjm3ZogGvtjf8t4HOmeQ=", "dev": true, "dependencies": {"chalk": "^2.0.0", "esutils": "^2.0.2", "js-tokens": "^4.0.0"}}, "node_modules/@babel/parser": {"version": "7.4.2", "resolved": "http://registry.npm.taobao.org/@babel/parser/download/@babel/parser-7.4.2.tgz", "integrity": "sha1-tFIaQAy1qHHqs4kHh7S8EybTjZE=", "dev": true, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/plugin-proposal-async-generator-functions": {"version": "7.2.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-proposal-async-generator-functions/download/@babel/plugin-proposal-async-generator-functions-7.2.0.tgz", "integrity": "sha1-somzBmadzkrSCwJSiJoVdoydQX4=", "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-async-generator-functions instead.", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-remap-async-to-generator": "^7.1.0", "@babel/plugin-syntax-async-generators": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-class-properties": {"version": "7.4.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-proposal-class-properties/download/@babel/plugin-proposal-class-properties-7.4.0.tgz", "integrity": "sha1-1w22Gi8f153pJ+6pH2QRyWTghLg=", "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead.", "dev": true, "dependencies": {"@babel/helper-create-class-features-plugin": "^7.4.0", "@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-decorators": {"version": "7.4.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-proposal-decorators/download/@babel/plugin-proposal-decorators-7.4.0.tgz", "integrity": "sha1-jhv9g++lSl9mIDOvzCuOcB9Ls6k=", "dev": true, "dependencies": {"@babel/helper-create-class-features-plugin": "^7.4.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-decorators": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-json-strings": {"version": "7.2.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-proposal-json-strings/download/@babel/plugin-proposal-json-strings-7.2.0.tgz", "integrity": "sha1-Vo7MRGxhSK5rJn8CVREwiR4p8xc=", "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-json-strings instead.", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-json-strings": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-object-rest-spread": {"version": "7.4.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-proposal-object-rest-spread/download/@babel/plugin-proposal-object-rest-spread-7.4.0.tgz", "integrity": "sha1-5JYFdSBerfKhq04MeflQTVuCqX8=", "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-object-rest-spread instead.", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-object-rest-spread": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-optional-catch-binding": {"version": "7.2.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-proposal-optional-catch-binding/download/@babel/plugin-proposal-optional-catch-binding-7.2.0.tgz", "integrity": "sha1-E12B7baKCB5V5W7EhUHs6AZcOPU=", "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-optional-catch-binding instead.", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-optional-catch-binding": "^7.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-unicode-property-regex": {"version": "7.4.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-proposal-unicode-property-regex/download/@babel/plugin-proposal-unicode-property-regex-7.4.0.tgz", "integrity": "sha1-IC2R7pd9dg74P09BaygNVovoRiM=", "deprecated": "This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-unicode-property-regex instead.", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-regex": "^7.0.0", "regexpu-core": "^4.5.4"}, "engines": {"node": ">=4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-async-generators": {"version": "7.2.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-syntax-async-generators/download/@babel/plugin-syntax-async-generators-7.2.0.tgz", "integrity": "sha1-aeHw2zTG9aDPfiszI78VmnbIy38=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-decorators": {"version": "7.2.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-syntax-decorators/download/@babel/plugin-syntax-decorators-7.2.0.tgz", "integrity": "sha1-xQsblX3MaeSxEntl4cM+72FXDBs=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-dynamic-import": {"version": "7.2.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-syntax-dynamic-import/download/@babel/plugin-syntax-dynamic-import-7.2.0.tgz", "integrity": "sha1-acFZ/69JmBIhYa2OvF5tH1XfhhI=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-json-strings": {"version": "7.2.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-syntax-json-strings/download/@babel/plugin-syntax-json-strings-7.2.0.tgz", "integrity": "sha1-cr0T9v/h0lk4Ep0qGGsR/WKVFHA=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-jsx": {"version": "7.2.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-syntax-jsx/download/@babel/plugin-syntax-jsx-7.2.0.tgz", "integrity": "sha1-C4WjtLx830zEuL8jYzW5B8oi58c=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-object-rest-spread": {"version": "7.2.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-syntax-object-rest-spread/download/@babel/plugin-syntax-object-rest-spread-7.2.0.tgz", "integrity": "sha1-O3o+czUQxX6CC5FCpleayLDfrS4=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-optional-catch-binding": {"version": "7.2.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-syntax-optional-catch-binding/download/@babel/plugin-syntax-optional-catch-binding-7.2.0.tgz", "integrity": "sha1-qUAT1u2okI3+akd+f57ahWVuz1w=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-arrow-functions": {"version": "7.2.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-arrow-functions/download/@babel/plugin-transform-arrow-functions-7.2.0.tgz", "integrity": "sha1-mur75Nb/xlY7+Pg3IJFijwB3lVA=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-async-to-generator": {"version": "7.4.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-async-to-generator/download/@babel/plugin-transform-async-to-generator-7.4.0.tgz", "integrity": "sha1-I0/j5Fjc6VhlwNFS0lYRmyN4NLA=", "dev": true, "dependencies": {"@babel/helper-module-imports": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-remap-async-to-generator": "^7.1.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-block-scoped-functions": {"version": "7.2.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-block-scoped-functions/download/@babel/plugin-transform-block-scoped-functions-7.2.0.tgz", "integrity": "sha1-XTzBHo1d3XUqpkyRSNDbbLef0ZA=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-block-scoping": {"version": "7.4.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-block-scoping/download/@babel/plugin-transform-block-scoping-7.4.0.tgz", "integrity": "sha1-Fk3zu0Hj3rlUxMoy/6n8qlbTC8s=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "lodash": "^4.17.11"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-classes": {"version": "7.4.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-classes/download/@babel/plugin-transform-classes-7.4.0.tgz", "integrity": "sha1-40KNPIo9AfM7EMUpuZi6FwcEPU0=", "dev": true, "dependencies": {"@babel/helper-annotate-as-pure": "^7.0.0", "@babel/helper-define-map": "^7.4.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-optimise-call-expression": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-replace-supers": "^7.4.0", "@babel/helper-split-export-declaration": "^7.4.0", "globals": "^11.1.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-computed-properties": {"version": "7.2.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-computed-properties/download/@babel/plugin-transform-computed-properties-7.2.0.tgz", "integrity": "sha1-g6ffamWIZbHI9kHVEMbzryICFto=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-destructuring": {"version": "7.4.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-destructuring/download/@babel/plugin-transform-destructuring-7.4.0.tgz", "integrity": "sha1-rLubJBjSkBB9szP01s2Kpq6gA0M=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-dotall-regex": {"version": "7.2.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-dotall-regex/download/@babel/plugin-transform-dotall-regex-7.2.0.tgz", "integrity": "sha1-8Kq7k9EgqKxh6SXqC6RAgS2+Dkk=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-regex": "^7.0.0", "regexpu-core": "^4.1.3"}, "engines": {"node": ">=4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-duplicate-keys": {"version": "7.2.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-duplicate-keys/download/@babel/plugin-transform-duplicate-keys-7.2.0.tgz", "integrity": "sha1-2VLEkw8xKk2//xjwspFOYMNVMLM=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-exponentiation-operator": {"version": "7.2.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-exponentiation-operator/download/@babel/plugin-transform-exponentiation-operator-7.2.0.tgz", "integrity": "sha1-pjhoKJ5bQAf3BU1GSRr1FDV2YAg=", "dev": true, "dependencies": {"@babel/helper-builder-binary-assignment-operator-visitor": "^7.1.0", "@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-for-of": {"version": "7.4.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-for-of/download/@babel/plugin-transform-for-of-7.4.0.tgz", "integrity": "sha1-VsjDZnf11KFrgLEve3aN4GSq618=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-function-name": {"version": "7.2.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-function-name/download/@babel/plugin-transform-function-name-7.2.0.tgz", "integrity": "sha1-95MDYoKf+ZoxdMOfCvzAJO9Zcxo=", "dev": true, "dependencies": {"@babel/helper-function-name": "^7.1.0", "@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-literals": {"version": "7.2.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-literals/download/@babel/plugin-transform-literals-7.2.0.tgz", "integrity": "sha1-aQNT6B+SZ9rU/Yz9d+r6hqulPqE=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-amd": {"version": "7.2.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-modules-amd/download/@babel/plugin-transform-modules-amd-7.2.0.tgz", "integrity": "sha1-gqm85FuVRB9heiQBHcidEtp/TuY=", "dev": true, "dependencies": {"@babel/helper-module-transforms": "^7.1.0", "@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-commonjs": {"version": "7.4.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-modules-commonjs/download/@babel/plugin-transform-modules-commonjs-7.4.0.tgz", "integrity": "sha1-O47GFxTTt10gxcz6FX8sLgh/1Mo=", "dev": true, "dependencies": {"@babel/helper-module-transforms": "^7.1.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-simple-access": "^7.1.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-systemjs": {"version": "7.4.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-modules-systemjs/download/@babel/plugin-transform-modules-systemjs-7.4.0.tgz", "integrity": "sha1-wkleVVKBNXl7yBb11Q+FFpjFhqE=", "dev": true, "dependencies": {"@babel/helper-hoist-variables": "^7.4.0", "@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-umd": {"version": "7.2.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-modules-umd/download/@babel/plugin-transform-modules-umd-7.2.0.tgz", "integrity": "sha1-dnjOdRafCHe46yI1U4wHQmjdAa4=", "dev": true, "dependencies": {"@babel/helper-module-transforms": "^7.1.0", "@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-named-capturing-groups-regex": {"version": "7.4.2", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-named-capturing-groups-regex/download/@babel/plugin-transform-named-capturing-groups-regex-7.4.2.tgz", "integrity": "sha1-gAORE21svMgHKNvbo8HG5G+GwS4=", "dev": true, "dependencies": {"regexp-tree": "^0.1.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-transform-new-target": {"version": "7.4.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-new-target/download/@babel/plugin-transform-new-target-7.4.0.tgz", "integrity": "sha1-Z2WKHZRO21PI1PowBEc6DdeDgVA=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-object-super": {"version": "7.2.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-object-super/download/@babel/plugin-transform-object-super-7.2.0.tgz", "integrity": "sha1-s11MEPVrq11lAEfa0PHY6IFLZZg=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-replace-supers": "^7.1.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-parameters": {"version": "7.4.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-parameters/download/@babel/plugin-transform-parameters-7.4.0.tgz", "integrity": "sha1-oTCUJvrE7s0qlDmkyMNRJKEaSKk=", "dev": true, "dependencies": {"@babel/helper-call-delegate": "^7.4.0", "@babel/helper-get-function-arity": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-regenerator": {"version": "7.4.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-regenerator/download/@babel/plugin-transform-regenerator-7.4.0.tgz", "integrity": "sha1-B4DifuRYzD/brRgpTXA+lyrh9tE=", "dev": true, "dependencies": {"regenerator-transform": "^0.13.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-runtime": {"version": "7.4.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-runtime/download/@babel/plugin-transform-runtime-7.4.0.tgz", "integrity": "sha1-tNjJJe2VdHG8V+C52lNAjrse1Fc=", "dev": true, "dependencies": {"@babel/helper-module-imports": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0", "resolve": "^1.8.1", "semver": "^5.5.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-shorthand-properties": {"version": "7.2.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-shorthand-properties/download/@babel/plugin-transform-shorthand-properties-7.2.0.tgz", "integrity": "sha1-YzOu4vjW7n4oYVRXKYk0o7RhmPA=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-spread": {"version": "7.2.2", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-spread/download/@babel/plugin-transform-spread-7.2.2.tgz", "integrity": "sha1-MQOpq+IvdCttQG7NPNSbd0kZtAY=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-sticky-regex": {"version": "7.2.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-sticky-regex/download/@babel/plugin-transform-sticky-regex-7.2.0.tgz", "integrity": "sha1-oeRUtZlVYKnB4NU338FQYf0mh+E=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-regex": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-template-literals": {"version": "7.2.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-template-literals/download/@babel/plugin-transform-template-literals-7.2.0.tgz", "integrity": "sha1-2H7QG46qx6kkc/YIyXwIneK6Hls=", "dev": true, "dependencies": {"@babel/helper-annotate-as-pure": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-typeof-symbol": {"version": "7.2.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-typeof-symbol/download/@babel/plugin-transform-typeof-symbol-7.2.0.tgz", "integrity": "sha1-EX0rzsL79ktLWdH5gZiUaC0p8rI=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-unicode-regex": {"version": "7.2.0", "resolved": "http://registry.npm.taobao.org/@babel/plugin-transform-unicode-regex/download/@babel/plugin-transform-unicode-regex-7.2.0.tgz", "integrity": "sha1-TrjbFvly+Ku1BiwWG4sRVUat4Is=", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/helper-regex": "^7.0.0", "regexpu-core": "^4.1.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/preset-env": {"version": "7.4.2", "resolved": "http://registry.npm.taobao.org/@babel/preset-env/download/@babel/preset-env-7.4.2.tgz", "integrity": "sha1-L1uh3i2u+p3MplOEj5bHzi5AZnY=", "dev": true, "dependencies": {"@babel/helper-module-imports": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-proposal-async-generator-functions": "^7.2.0", "@babel/plugin-proposal-json-strings": "^7.2.0", "@babel/plugin-proposal-object-rest-spread": "^7.4.0", "@babel/plugin-proposal-optional-catch-binding": "^7.2.0", "@babel/plugin-proposal-unicode-property-regex": "^7.4.0", "@babel/plugin-syntax-async-generators": "^7.2.0", "@babel/plugin-syntax-json-strings": "^7.2.0", "@babel/plugin-syntax-object-rest-spread": "^7.2.0", "@babel/plugin-syntax-optional-catch-binding": "^7.2.0", "@babel/plugin-transform-arrow-functions": "^7.2.0", "@babel/plugin-transform-async-to-generator": "^7.4.0", "@babel/plugin-transform-block-scoped-functions": "^7.2.0", "@babel/plugin-transform-block-scoping": "^7.4.0", "@babel/plugin-transform-classes": "^7.4.0", "@babel/plugin-transform-computed-properties": "^7.2.0", "@babel/plugin-transform-destructuring": "^7.4.0", "@babel/plugin-transform-dotall-regex": "^7.2.0", "@babel/plugin-transform-duplicate-keys": "^7.2.0", "@babel/plugin-transform-exponentiation-operator": "^7.2.0", "@babel/plugin-transform-for-of": "^7.4.0", "@babel/plugin-transform-function-name": "^7.2.0", "@babel/plugin-transform-literals": "^7.2.0", "@babel/plugin-transform-modules-amd": "^7.2.0", "@babel/plugin-transform-modules-commonjs": "^7.4.0", "@babel/plugin-transform-modules-systemjs": "^7.4.0", "@babel/plugin-transform-modules-umd": "^7.2.0", "@babel/plugin-transform-named-capturing-groups-regex": "^7.4.2", "@babel/plugin-transform-new-target": "^7.4.0", "@babel/plugin-transform-object-super": "^7.2.0", "@babel/plugin-transform-parameters": "^7.4.0", "@babel/plugin-transform-regenerator": "^7.4.0", "@babel/plugin-transform-shorthand-properties": "^7.2.0", "@babel/plugin-transform-spread": "^7.2.0", "@babel/plugin-transform-sticky-regex": "^7.2.0", "@babel/plugin-transform-template-literals": "^7.2.0", "@babel/plugin-transform-typeof-symbol": "^7.2.0", "@babel/plugin-transform-unicode-regex": "^7.2.0", "@babel/types": "^7.4.0", "browserslist": "^4.4.2", "core-js-compat": "^3.0.0", "invariant": "^2.2.2", "js-levenshtein": "^1.1.3", "semver": "^5.3.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/runtime": {"version": "7.27.6", "resolved": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "integrity": "sha512-vbavdySgbTTrmFE+EsiqUTzlOr5bzlnJtUv9PynGCAKvfQqjIXbvFdumPM/GxMDfyuGMJaJAU6TO4zc1Jf1i8Q==", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/runtime-corejs2": {"version": "7.4.2", "resolved": "http://registry.npm.taobao.org/@babel/runtime-corejs2/download/@babel/runtime-corejs2-7.4.2.tgz", "integrity": "sha1-oM7CxBcX+kFenCBPMrYD2IsXlsI=", "dev": true, "dependencies": {"core-js": "^2.6.5", "regenerator-runtime": "^0.13.2"}}, "node_modules/@babel/template": {"version": "7.4.0", "resolved": "http://registry.npm.taobao.org/@babel/template/download/@babel/template-7.4.0.tgz", "integrity": "sha1-EkdOnAd7rlhcXYNalcCwt5DCXIs=", "dev": true, "dependencies": {"@babel/code-frame": "^7.0.0", "@babel/parser": "^7.4.0", "@babel/types": "^7.4.0"}}, "node_modules/@babel/traverse": {"version": "7.4.0", "resolved": "http://registry.npm.taobao.org/@babel/traverse/download/@babel/traverse-7.4.0.tgz", "integrity": "sha1-FABpZ90dKzSUzdZQxobbna8N2to=", "dev": true, "dependencies": {"@babel/code-frame": "^7.0.0", "@babel/generator": "^7.4.0", "@babel/helper-function-name": "^7.1.0", "@babel/helper-split-export-declaration": "^7.4.0", "@babel/parser": "^7.4.0", "@babel/types": "^7.4.0", "debug": "^4.1.0", "globals": "^11.1.0", "lodash": "^4.17.11"}}, "node_modules/@babel/types": {"version": "7.4.0", "resolved": "http://registry.npm.taobao.org/@babel/types/download/@babel/types-7.4.0.tgz", "integrity": "sha1-Zwck930kzObMfYz2RZnVEdFkiUw=", "dev": true, "dependencies": {"esutils": "^2.0.2", "lodash": "^4.17.11", "to-fast-properties": "^2.0.0"}}, "node_modules/@intervolga/optimize-cssnano-plugin": {"version": "1.0.6", "resolved": "http://registry.npm.taobao.org/@intervolga/optimize-cssnano-plugin/download/@intervolga/optimize-cssnano-plugin-1.0.6.tgz", "integrity": "sha1-vnx4RhKLiPapsdEmGgrQbrXA/fg=", "dev": true, "dependencies": {"cssnano": "^4.0.0", "cssnano-preset-default": "^4.0.0", "postcss": "^7.0.0"}, "peerDependencies": {"webpack": "^4.0.0"}}, "node_modules/@mrmlnc/readdir-enhanced": {"version": "2.2.1", "resolved": "http://registry.npm.taobao.org/@mrmlnc/readdir-enhanced/download/@mrmlnc/readdir-enhanced-2.2.1.tgz", "integrity": "sha1-UkryQNGjYFJ7cwR17PoTRKpUDd4=", "dev": true, "dependencies": {"call-me-maybe": "^1.0.1", "glob-to-regexp": "^0.3.0"}, "engines": {"node": ">=4"}}, "node_modules/@nodelib/fs.stat": {"version": "1.1.3", "resolved": "http://registry.npm.taobao.org/@nodelib/fs.stat/download/@nodelib/fs.stat-1.1.3.tgz", "integrity": "sha1-K1o6s/kYzKSKjHVMCBaOPwPrphs=", "dev": true, "engines": {"node": ">= 6"}}, "node_modules/@soda/friendly-errors-webpack-plugin": {"version": "1.7.1", "resolved": "http://registry.npm.taobao.org/@soda/friendly-errors-webpack-plugin/download/@soda/friendly-errors-webpack-plugin-1.7.1.tgz", "integrity": "sha1-cG9kvLSouWQrSK46zkRMcDNNYV0=", "dev": true, "dependencies": {"chalk": "^1.1.3", "error-stack-parser": "^2.0.0", "string-width": "^2.0.0"}, "peerDependencies": {"webpack": "^2.0.0 || ^3.0.0 || ^4.0.0"}}, "node_modules/@soda/friendly-errors-webpack-plugin/node_modules/ansi-regex": {"version": "2.1.1", "resolved": "http://registry.npm.taobao.org/ansi-regex/download/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/@soda/friendly-errors-webpack-plugin/node_modules/ansi-styles": {"version": "2.2.1", "resolved": "http://registry.npm.taobao.org/ansi-styles/download/ansi-styles-2.2.1.tgz", "integrity": "sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/@soda/friendly-errors-webpack-plugin/node_modules/chalk": {"version": "1.1.3", "resolved": "http://registry.npm.taobao.org/chalk/download/chalk-1.1.3.tgz", "integrity": "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=", "dev": true, "dependencies": {"ansi-styles": "^2.2.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/@soda/friendly-errors-webpack-plugin/node_modules/strip-ansi": {"version": "3.0.1", "resolved": "http://registry.npm.taobao.org/strip-ansi/download/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "dev": true, "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/@soda/friendly-errors-webpack-plugin/node_modules/supports-color": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/supports-color/download/supports-color-2.0.0.tgz", "integrity": "sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=", "dev": true, "engines": {"node": ">=0.8.0"}}, "node_modules/@types/events": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/@types/events/download/@types/events-3.0.0.tgz", "integrity": "sha1-KGLz9Yqaf3w+eNefEw3U1xwlwqc=", "dev": true}, "node_modules/@types/glob": {"version": "7.1.1", "resolved": "http://registry.npm.taobao.org/@types/glob/download/@types/glob-7.1.1.tgz", "integrity": "sha1-qlmhxuP7xCHgfM0xqUTDDrpSFXU=", "dev": true, "dependencies": {"@types/events": "*", "@types/minimatch": "*", "@types/node": "*"}}, "node_modules/@types/minimatch": {"version": "3.0.3", "resolved": "http://registry.npm.taobao.org/@types/minimatch/download/@types/minimatch-3.0.3.tgz", "integrity": "sha1-PcoOPzOyAPx9ETnAzZbBJoyt/Z0=", "dev": true}, "node_modules/@types/node": {"version": "11.11.6", "resolved": "http://registry.npm.taobao.org/@types/node/download/@types/node-11.11.6.tgz?cache=0&other_urls=http%3A%2F%2Fregistry.npm.taobao.org%2F%40types%2Fnode%2Fdownload%2F%40types%2Fnode-11.11.6.tgz", "integrity": "sha1-35KdG7Lu5a/dpZikGTD+ULQ+qmo=", "dev": true}, "node_modules/@types/q": {"version": "1.5.2", "resolved": "http://registry.npm.taobao.org/@types/q/download/@types/q-1.5.2.tgz", "integrity": "sha1-aQoUdbhPKohP0HzXl8APXzE1bqg=", "dev": true}, "node_modules/@types/raf": {"version": "3.4.3", "resolved": "https://registry.npmjs.org/@types/raf/-/raf-3.4.3.tgz", "integrity": "sha512-c4YAvMedbPZ5tEyxzQdMoOhhJ4RD3rngZIdwC2/qDN3d7JpEhB6fiBRKVY1lg5B7Wk+uPBjn5f39j1/2MY1oOw==", "optional": true}, "node_modules/@vue/babel-helper-vue-jsx-merge-props": {"version": "1.0.0-beta.3", "resolved": "http://registry.npm.taobao.org/@vue/babel-helper-vue-jsx-merge-props/download/@vue/babel-helper-vue-jsx-merge-props-1.0.0-beta.3.tgz", "integrity": "sha1-5MLnEls+DSqdST5FeFCyq7D9PK0=", "dev": true}, "node_modules/@vue/babel-plugin-transform-vue-jsx": {"version": "1.0.0-beta.3", "resolved": "http://registry.npm.taobao.org/@vue/babel-plugin-transform-vue-jsx/download/@vue/babel-plugin-transform-vue-jsx-1.0.0-beta.3.tgz", "integrity": "sha1-oaROgB2O1hXknxRe8bPqyiwW4uY=", "dev": true, "dependencies": {"@babel/helper-module-imports": "^7.0.0", "@babel/plugin-syntax-jsx": "^7.2.0", "@vue/babel-helper-vue-jsx-merge-props": "^1.0.0-beta.3", "html-tags": "^2.0.0", "lodash.kebabcase": "^4.1.1", "svg-tags": "^1.0.0"}}, "node_modules/@vue/babel-preset-app": {"version": "3.5.1", "resolved": "http://registry.npm.taobao.org/@vue/babel-preset-app/download/@vue/babel-preset-app-3.5.1.tgz", "integrity": "sha1-Ez/oMsy5pVe6lG+mYDXb5bnGHlk=", "dev": true, "dependencies": {"@babel/plugin-proposal-class-properties": "^7.0.0", "@babel/plugin-proposal-decorators": "^7.1.0", "@babel/plugin-syntax-dynamic-import": "^7.0.0", "@babel/plugin-syntax-jsx": "^7.0.0", "@babel/plugin-transform-runtime": "^7.0.0", "@babel/preset-env": "^7.0.0", "@babel/runtime": "^7.0.0", "@babel/runtime-corejs2": "^7.2.0", "@vue/babel-preset-jsx": "^1.0.0-beta.2", "babel-plugin-dynamic-import-node": "^2.2.0", "core-js": "^2.6.5"}}, "node_modules/@vue/babel-preset-jsx": {"version": "1.0.0-beta.3", "resolved": "http://registry.npm.taobao.org/@vue/babel-preset-jsx/download/@vue/babel-preset-jsx-1.0.0-beta.3.tgz", "integrity": "sha1-FcWEvWLAKGqA8BlnSa44zeXNcDs=", "dev": true, "dependencies": {"@vue/babel-helper-vue-jsx-merge-props": "^1.0.0-beta.3", "@vue/babel-plugin-transform-vue-jsx": "^1.0.0-beta.3", "@vue/babel-sugar-functional-vue": "^1.0.0-beta.3", "@vue/babel-sugar-inject-h": "^1.0.0-beta.3", "@vue/babel-sugar-v-model": "^1.0.0-beta.3", "@vue/babel-sugar-v-on": "^1.0.0-beta.3"}}, "node_modules/@vue/babel-sugar-functional-vue": {"version": "1.0.0-beta.3", "resolved": "http://registry.npm.taobao.org/@vue/babel-sugar-functional-vue/download/@vue/babel-sugar-functional-vue-1.0.0-beta.3.tgz", "integrity": "sha1-QahVeGlx2su+gESFju/pjeCJvxI=", "dev": true, "dependencies": {"@babel/plugin-syntax-jsx": "^7.2.0"}}, "node_modules/@vue/babel-sugar-inject-h": {"version": "1.0.0-beta.3", "resolved": "http://registry.npm.taobao.org/@vue/babel-sugar-inject-h/download/@vue/babel-sugar-inject-h-1.0.0-beta.3.tgz", "integrity": "sha1-vh0At0oaif7TWpsUFac4w28SWWY=", "dev": true, "dependencies": {"@babel/plugin-syntax-jsx": "^7.2.0"}}, "node_modules/@vue/babel-sugar-v-model": {"version": "1.0.0-beta.3", "resolved": "http://registry.npm.taobao.org/@vue/babel-sugar-v-model/download/@vue/babel-sugar-v-model-1.0.0-beta.3.tgz", "integrity": "sha1-6pNbDgi/WMEloTSbgZFWBZWQmTw=", "dev": true, "dependencies": {"@babel/plugin-syntax-jsx": "^7.2.0", "@vue/babel-helper-vue-jsx-merge-props": "^1.0.0-beta.3", "@vue/babel-plugin-transform-vue-jsx": "^1.0.0-beta.3", "camelcase": "^5.0.0", "html-tags": "^2.0.0", "svg-tags": "^1.0.0"}}, "node_modules/@vue/babel-sugar-v-on": {"version": "1.0.0-beta.3", "resolved": "http://registry.npm.taobao.org/@vue/babel-sugar-v-on/download/@vue/babel-sugar-v-on-1.0.0-beta.3.tgz", "integrity": "sha1-L1/ttDiD9gP+dgEPJTuFx0ZYVf4=", "dev": true, "dependencies": {"@babel/plugin-syntax-jsx": "^7.2.0", "@vue/babel-plugin-transform-vue-jsx": "^1.0.0-beta.3", "camelcase": "^5.0.0"}}, "node_modules/@vue/cli-overlay": {"version": "3.5.1", "resolved": "http://registry.npm.taobao.org/@vue/cli-overlay/download/@vue/cli-overlay-3.5.1.tgz", "integrity": "sha1-jZIU1zyMmjAxRxkPFHbBNO9yb/k=", "dev": true}, "node_modules/@vue/cli-plugin-babel": {"version": "3.5.1", "resolved": "http://registry.npm.taobao.org/@vue/cli-plugin-babel/download/@vue/cli-plugin-babel-3.5.1.tgz", "integrity": "sha1-q4cp/1O7UIYTuZjN0uJaeN79MMg=", "dev": true, "dependencies": {"@babel/core": "^7.0.0", "@vue/babel-preset-app": "^3.5.1", "@vue/cli-shared-utils": "^3.5.1", "babel-loader": "^8.0.5", "webpack": ">=4 < 4.29"}}, "node_modules/@vue/cli-service": {"version": "3.5.1", "resolved": "http://registry.npm.taobao.org/@vue/cli-service/download/@vue/cli-service-3.5.1.tgz", "integrity": "sha1-1ACrlWVpqH85R+fWly81yX3oQUU=", "dev": true, "dependencies": {"@intervolga/optimize-cssnano-plugin": "^1.0.5", "@soda/friendly-errors-webpack-plugin": "^1.7.1", "@vue/cli-overlay": "^3.5.1", "@vue/cli-shared-utils": "^3.5.1", "@vue/component-compiler-utils": "^2.6.0", "@vue/preload-webpack-plugin": "^1.1.0", "@vue/web-component-wrapper": "^1.2.0", "acorn": "^6.1.0", "acorn-walk": "^6.1.1", "address": "^1.0.3", "autoprefixer": "^9.4.8", "cache-loader": "^2.0.1", "case-sensitive-paths-webpack-plugin": "^2.2.0", "chalk": "^2.4.2", "clipboardy": "^1.2.3", "cliui": "^4.1.0", "copy-webpack-plugin": "^4.6.0", "css-loader": "^1.0.1", "cssnano": "^4.1.10", "debug": "^4.1.1", "dotenv": "^6.2.0", "dotenv-expand": "^4.2.0", "escape-string-regexp": "^1.0.5", "file-loader": "^3.0.1", "fs-extra": "^7.0.1", "globby": "^9.0.0", "hash-sum": "^1.0.2", "html-webpack-plugin": "^3.2.0", "launch-editor-middleware": "^2.2.1", "lodash.defaultsdeep": "^4.6.0", "lodash.mapvalues": "^4.6.0", "lodash.transform": "^4.6.0", "mini-css-extract-plugin": "^0.5.0", "minimist": "^1.2.0", "ora": "^3.1.0", "portfinder": "^1.0.20", "postcss-loader": "^3.0.0", "read-pkg": "^4.0.1", "semver": "^5.6.0", "slash": "^2.0.0", "source-map-url": "^0.4.0", "ssri": "^6.0.1", "string.prototype.padend": "^3.0.0", "terser-webpack-plugin": "^1.2.2", "thread-loader": "^2.1.2", "url-loader": "^1.1.2", "vue-loader": "^15.6.4", "webpack": ">=4 < 4.29", "webpack-bundle-analyzer": "^3.0.4", "webpack-chain": "^4.11.0", "webpack-dev-server": "^3.2.0", "webpack-merge": "^4.2.1", "yorkie": "^2.0.0"}, "bin": {"vue-cli-service": "bin/vue-cli-service.js"}, "engines": {"node": ">=8"}, "peerDependencies": {"vue-template-compiler": "^2.0.0"}}, "node_modules/@vue/cli-service/node_modules/acorn": {"version": "6.1.1", "resolved": "http://registry.npm.taobao.org/acorn/download/acorn-6.1.1.tgz", "integrity": "sha1-fSWuBbuK0fm2mRCOEJTs14hK3B8=", "dev": true, "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/@vue/cli-shared-utils": {"version": "3.5.1", "resolved": "http://registry.npm.taobao.org/@vue/cli-shared-utils/download/@vue/cli-shared-utils-3.5.1.tgz", "integrity": "sha1-cdZvBvxhm6KN8nm9fTe6G6KckGY=", "dev": true, "dependencies": {"chalk": "^2.4.1", "execa": "^1.0.0", "joi": "^14.3.0", "launch-editor": "^2.2.1", "lru-cache": "^5.1.1", "node-ipc": "^9.1.1", "opn": "^5.3.0", "ora": "^3.1.0", "request": "^2.87.0", "request-promise-native": "^1.0.7", "semver": "^5.5.0", "string.prototype.padstart": "^3.0.0"}}, "node_modules/@vue/component-compiler-utils": {"version": "2.6.0", "resolved": "http://registry.npm.taobao.org/@vue/component-compiler-utils/download/@vue/component-compiler-utils-2.6.0.tgz", "integrity": "sha1-qkbSpvdkdECwuJMkNNIvEjceVDs=", "dev": true, "dependencies": {"consolidate": "^0.15.1", "hash-sum": "^1.0.2", "lru-cache": "^4.1.2", "merge-source-map": "^1.1.0", "postcss": "^7.0.14", "postcss-selector-parser": "^5.0.0", "prettier": "1.16.3", "source-map": "~0.6.1", "vue-template-es2015-compiler": "^1.9.0"}}, "node_modules/@vue/component-compiler-utils/node_modules/lru-cache": {"version": "4.1.5", "resolved": "http://registry.npm.taobao.org/lru-cache/download/lru-cache-4.1.5.tgz", "integrity": "sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80=", "dev": true, "dependencies": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "node_modules/@vue/component-compiler-utils/node_modules/source-map": {"version": "0.6.1", "resolved": "http://registry.npm.taobao.org/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/@vue/component-compiler-utils/node_modules/yallist": {"version": "2.1.2", "resolved": "http://registry.npm.taobao.org/yallist/download/yallist-2.1.2.tgz", "integrity": "sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=", "dev": true}, "node_modules/@vue/preload-webpack-plugin": {"version": "1.1.0", "resolved": "http://registry.npm.taobao.org/@vue/preload-webpack-plugin/download/@vue/preload-webpack-plugin-1.1.0.tgz", "integrity": "sha1-12jboAQmHAKbU6d8XqLV+e5PPM4=", "dev": true, "engines": {"node": ">=6.0.0"}, "peerDependencies": {"html-webpack-plugin": ">=2.26.0", "webpack": ">=4.0.0"}}, "node_modules/@vue/web-component-wrapper": {"version": "1.2.0", "resolved": "http://registry.npm.taobao.org/@vue/web-component-wrapper/download/@vue/web-component-wrapper-1.2.0.tgz", "integrity": "sha1-uw5G8VhafiibTuYGfcxaauYvHdE=", "dev": true}, "node_modules/@webassemblyjs/ast": {"version": "1.7.11", "resolved": "http://registry.npm.taobao.org/@webassemblyjs/ast/download/@webassemblyjs/ast-1.7.11.tgz", "integrity": "sha1-uYhYLK+7Kwlei1VlJvMMkNBXys4=", "dev": true, "dependencies": {"@webassemblyjs/helper-module-context": "1.7.11", "@webassemblyjs/helper-wasm-bytecode": "1.7.11", "@webassemblyjs/wast-parser": "1.7.11"}}, "node_modules/@webassemblyjs/floating-point-hex-parser": {"version": "1.7.11", "resolved": "http://registry.npm.taobao.org/@webassemblyjs/floating-point-hex-parser/download/@webassemblyjs/floating-point-hex-parser-1.7.11.tgz", "integrity": "sha1-pp8K9lAuuaPARVVbGmEp09Py4xM=", "dev": true}, "node_modules/@webassemblyjs/helper-api-error": {"version": "1.7.11", "resolved": "http://registry.npm.taobao.org/@webassemblyjs/helper-api-error/download/@webassemblyjs/helper-api-error-1.7.11.tgz", "integrity": "sha1-x7a7gQX4QDlRGis5zklPGTgYoyo=", "dev": true}, "node_modules/@webassemblyjs/helper-buffer": {"version": "1.7.11", "resolved": "http://registry.npm.taobao.org/@webassemblyjs/helper-buffer/download/@webassemblyjs/helper-buffer-1.7.11.tgz", "integrity": "sha1-MSLUjcxslFbtmC3r4WyPNxAd85s=", "dev": true}, "node_modules/@webassemblyjs/helper-code-frame": {"version": "1.7.11", "resolved": "http://registry.npm.taobao.org/@webassemblyjs/helper-code-frame/download/@webassemblyjs/helper-code-frame-1.7.11.tgz", "integrity": "sha1-z48QbnRmYqDaKb3vY1/NPRJINks=", "dev": true, "dependencies": {"@webassemblyjs/wast-printer": "1.7.11"}}, "node_modules/@webassemblyjs/helper-fsm": {"version": "1.7.11", "resolved": "http://registry.npm.taobao.org/@webassemblyjs/helper-fsm/download/@webassemblyjs/helper-fsm-1.7.11.tgz", "integrity": "sha1-3ziIKmJAgNA/dQP5Pj8XrFrAEYE=", "dev": true}, "node_modules/@webassemblyjs/helper-module-context": {"version": "1.7.11", "resolved": "http://registry.npm.taobao.org/@webassemblyjs/helper-module-context/download/@webassemblyjs/helper-module-context-1.7.11.tgz", "integrity": "sha1-2HTXIuUeYqwgJHaTXWScgC+g4gk=", "dev": true}, "node_modules/@webassemblyjs/helper-wasm-bytecode": {"version": "1.7.11", "resolved": "http://registry.npm.taobao.org/@webassemblyjs/helper-wasm-bytecode/download/@webassemblyjs/helper-wasm-bytecode-1.7.11.tgz", "integrity": "sha1-3ZoegX8cLrEFtM8QEwk8ufPJywY=", "dev": true}, "node_modules/@webassemblyjs/helper-wasm-section": {"version": "1.7.11", "resolved": "http://registry.npm.taobao.org/@webassemblyjs/helper-wasm-section/download/@webassemblyjs/helper-wasm-section-1.7.11.tgz", "integrity": "sha1-nJrEHs+fvP/8lvbSZ14t4zgR5oo=", "dev": true, "dependencies": {"@webassemblyjs/ast": "1.7.11", "@webassemblyjs/helper-buffer": "1.7.11", "@webassemblyjs/helper-wasm-bytecode": "1.7.11", "@webassemblyjs/wasm-gen": "1.7.11"}}, "node_modules/@webassemblyjs/ieee754": {"version": "1.7.11", "resolved": "http://registry.npm.taobao.org/@webassemblyjs/ieee754/download/@webassemblyjs/ieee754-1.7.11.tgz", "integrity": "sha1-yVg562N1ejGICq7HtlEtQZGsZAs=", "dev": true, "dependencies": {"@xtuc/ieee754": "^1.2.0"}}, "node_modules/@webassemblyjs/leb128": {"version": "1.7.11", "resolved": "http://registry.npm.taobao.org/@webassemblyjs/leb128/download/@webassemblyjs/leb128-1.7.11.tgz", "integrity": "sha1-1yZ6HunEWU/T9+NymIGOxlaH22M=", "dev": true, "dependencies": {"@xtuc/long": "4.2.1"}}, "node_modules/@webassemblyjs/utf8": {"version": "1.7.11", "resolved": "http://registry.npm.taobao.org/@webassemblyjs/utf8/download/@webassemblyjs/utf8-1.7.11.tgz", "integrity": "sha1-Btchjqn9yUpnk6qSIIFg2z0m7oI=", "dev": true}, "node_modules/@webassemblyjs/wasm-edit": {"version": "1.7.11", "resolved": "http://registry.npm.taobao.org/@webassemblyjs/wasm-edit/download/@webassemblyjs/wasm-edit-1.7.11.tgz", "integrity": "sha1-jHTKR01PlR0B266b1wgU7iKoIAU=", "dev": true, "dependencies": {"@webassemblyjs/ast": "1.7.11", "@webassemblyjs/helper-buffer": "1.7.11", "@webassemblyjs/helper-wasm-bytecode": "1.7.11", "@webassemblyjs/helper-wasm-section": "1.7.11", "@webassemblyjs/wasm-gen": "1.7.11", "@webassemblyjs/wasm-opt": "1.7.11", "@webassemblyjs/wasm-parser": "1.7.11", "@webassemblyjs/wast-printer": "1.7.11"}}, "node_modules/@webassemblyjs/wasm-gen": {"version": "1.7.11", "resolved": "http://registry.npm.taobao.org/@webassemblyjs/wasm-gen/download/@webassemblyjs/wasm-gen-1.7.11.tgz", "integrity": "sha1-m7upQvIjdWhqb7dZr816ycRdoag=", "dev": true, "dependencies": {"@webassemblyjs/ast": "1.7.11", "@webassemblyjs/helper-wasm-bytecode": "1.7.11", "@webassemblyjs/ieee754": "1.7.11", "@webassemblyjs/leb128": "1.7.11", "@webassemblyjs/utf8": "1.7.11"}}, "node_modules/@webassemblyjs/wasm-opt": {"version": "1.7.11", "resolved": "http://registry.npm.taobao.org/@webassemblyjs/wasm-opt/download/@webassemblyjs/wasm-opt-1.7.11.tgz", "integrity": "sha1-szHo5874+OLwB9QsOjagWAp9bKc=", "dev": true, "dependencies": {"@webassemblyjs/ast": "1.7.11", "@webassemblyjs/helper-buffer": "1.7.11", "@webassemblyjs/wasm-gen": "1.7.11", "@webassemblyjs/wasm-parser": "1.7.11"}}, "node_modules/@webassemblyjs/wasm-parser": {"version": "1.7.11", "resolved": "http://registry.npm.taobao.org/@webassemblyjs/wasm-parser/download/@webassemblyjs/wasm-parser-1.7.11.tgz", "integrity": "sha1-bj0g+mo1GfawhO+Tka1YIR77Cho=", "dev": true, "dependencies": {"@webassemblyjs/ast": "1.7.11", "@webassemblyjs/helper-api-error": "1.7.11", "@webassemblyjs/helper-wasm-bytecode": "1.7.11", "@webassemblyjs/ieee754": "1.7.11", "@webassemblyjs/leb128": "1.7.11", "@webassemblyjs/utf8": "1.7.11"}}, "node_modules/@webassemblyjs/wast-parser": {"version": "1.7.11", "resolved": "http://registry.npm.taobao.org/@webassemblyjs/wast-parser/download/@webassemblyjs/wast-parser-1.7.11.tgz", "integrity": "sha1-Jb0RdWLKjAAnIP+BFu+QctnKhpw=", "dev": true, "dependencies": {"@webassemblyjs/ast": "1.7.11", "@webassemblyjs/floating-point-hex-parser": "1.7.11", "@webassemblyjs/helper-api-error": "1.7.11", "@webassemblyjs/helper-code-frame": "1.7.11", "@webassemblyjs/helper-fsm": "1.7.11", "@xtuc/long": "4.2.1"}}, "node_modules/@webassemblyjs/wast-printer": {"version": "1.7.11", "resolved": "http://registry.npm.taobao.org/@webassemblyjs/wast-printer/download/@webassemblyjs/wast-printer-1.7.11.tgz", "integrity": "sha1-xCRbbeJCy1CizJUBdP2/ZceNeBM=", "dev": true, "dependencies": {"@webassemblyjs/ast": "1.7.11", "@webassemblyjs/wast-parser": "1.7.11", "@xtuc/long": "4.2.1"}}, "node_modules/@xtuc/ieee754": {"version": "1.2.0", "resolved": "http://registry.npm.taobao.org/@xtuc/ieee754/download/@xtuc/ieee754-1.2.0.tgz", "integrity": "sha1-7vAUoxRa5Hehy8AM0eVSM23Ot5A=", "dev": true}, "node_modules/@xtuc/long": {"version": "4.2.1", "resolved": "http://registry.npm.taobao.org/@xtuc/long/download/@xtuc/long-4.2.1.tgz", "integrity": "sha1-XIXWYvdvodNFdXZsXc1mFavNMNg=", "dev": true}, "node_modules/accepts": {"version": "1.3.5", "resolved": "http://registry.npm.taobao.org/accepts/download/accepts-1.3.5.tgz", "integrity": "sha1-63d99gEXI6OxTopywIBcjoZ0a9I=", "dev": true, "dependencies": {"mime-types": "~2.1.18", "negotiator": "0.6.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/acorn": {"version": "5.7.3", "resolved": "http://registry.npm.taobao.org/acorn/download/acorn-5.7.3.tgz", "integrity": "sha1-Z6ojG/iBKXS4UjWpZ3Hra9B+onk=", "dev": true, "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-dynamic-import": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/acorn-dynamic-import/download/acorn-dynamic-import-3.0.0.tgz", "integrity": "sha1-kBzu5Mf6rvfgetKkfokGddpQong=", "deprecated": "This is probably built in to whatever tool you're using. If you still need it... idk", "dev": true, "dependencies": {"acorn": "^5.0.0"}}, "node_modules/acorn-walk": {"version": "6.1.1", "resolved": "http://registry.npm.taobao.org/acorn-walk/download/acorn-walk-6.1.1.tgz", "integrity": "sha1-02O2b1+sXwGP+cOh57b44xDMORM=", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/address": {"version": "1.0.3", "resolved": "http://registry.npm.taobao.org/address/download/address-1.0.3.tgz", "integrity": "sha1-tfUGMfjWzsi9IMljljr7VeBsvOk=", "dev": true, "engines": {"node": ">= 0.12.0"}}, "node_modules/ajv": {"version": "6.10.0", "resolved": "http://registry.npm.taobao.org/ajv/download/ajv-6.10.0.tgz", "integrity": "sha1-kNDVRDnaWHzX6EO/twRfUL0ivfE=", "dev": true, "dependencies": {"fast-deep-equal": "^2.0.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}}, "node_modules/ajv-errors": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/ajv-errors/download/ajv-errors-1.0.1.tgz", "integrity": "sha1-81mGrOuRr63sQQL72FAUlQzvpk0=", "dev": true, "peerDependencies": {"ajv": ">=5.0.0"}}, "node_modules/ajv-keywords": {"version": "3.4.0", "resolved": "http://registry.npm.taobao.org/ajv-keywords/download/ajv-keywords-3.4.0.tgz", "integrity": "sha1-S4Mee1MUFafMUYzUBOc/YZPGNJ0=", "dev": true, "peerDependencies": {"ajv": "^6.9.1"}}, "node_modules/alphanum-sort": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/alphanum-sort/download/alphanum-sort-1.0.2.tgz", "integrity": "sha1-l6ERlkmyEa0zaR2fn0hqjsn74KM=", "dev": true}, "node_modules/ansi-colors": {"version": "3.2.4", "resolved": "http://registry.npm.taobao.org/ansi-colors/download/ansi-colors-3.2.4.tgz", "integrity": "sha1-46PaS/uubIapwoViXeEkojQCb78=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/ansi-html": {"version": "0.0.7", "resolved": "http://registry.npm.taobao.org/ansi-html/download/ansi-html-0.0.7.tgz", "integrity": "sha1-gTWEAhliqenm/QOflA0S9WynhZ4=", "dev": true, "engines": ["node >= 0.8.0"], "bin": {"ansi-html": "bin/ansi-html"}}, "node_modules/ansi-regex": {"version": "4.1.0", "resolved": "http://registry.npm.taobao.org/ansi-regex/download/ansi-regex-4.1.0.tgz", "integrity": "sha1-i5+PCM8ay4Q3Vqg5yox+MWjFGZc=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/ansi-styles": {"version": "3.2.1", "resolved": "http://registry.npm.taobao.org/ansi-styles/download/ansi-styles-3.2.1.tgz", "integrity": "sha1-QfuyAkPlCxK+DwS43tvwdSDOhB0=", "dev": true, "dependencies": {"color-convert": "^1.9.0"}, "engines": {"node": ">=4"}}, "node_modules/anymatch": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/anymatch/download/anymatch-2.0.0.tgz", "integrity": "sha1-vLJLTzeTTZqnrBe0ra+J58du8us=", "dev": true, "dependencies": {"micromatch": "^3.1.4", "normalize-path": "^2.1.1"}}, "node_modules/anymatch/node_modules/normalize-path": {"version": "2.1.1", "resolved": "http://registry.npm.taobao.org/normalize-path/download/normalize-path-2.1.1.tgz", "integrity": "sha1-GrKLVW4Zg2Oowab35vogE3/mrtk=", "dev": true, "dependencies": {"remove-trailing-separator": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/aproba": {"version": "1.2.0", "resolved": "http://registry.npm.taobao.org/aproba/download/aproba-1.2.0.tgz", "integrity": "sha1-aALmJk79GMeQobDVF/DyYnvyyUo=", "dev": true}, "node_modules/arch": {"version": "2.1.1", "resolved": "http://registry.npm.taobao.org/arch/download/arch-2.1.1.tgz", "integrity": "sha1-j1wnMao1owkpIhuwZA7tZRdeyE4=", "dev": true}, "node_modules/argparse": {"version": "1.0.10", "resolved": "http://registry.npm.taobao.org/argparse/download/argparse-1.0.10.tgz", "integrity": "sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=", "dev": true, "dependencies": {"sprintf-js": "~1.0.2"}}, "node_modules/arr-diff": {"version": "4.0.0", "resolved": "http://registry.npm.taobao.org/arr-diff/download/arr-diff-4.0.0.tgz", "integrity": "sha1-1kYQdP6/7HHn4VI1dhoyml3HxSA=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/arr-flatten": {"version": "1.1.0", "resolved": "http://registry.npm.taobao.org/arr-flatten/download/arr-flatten-1.1.0.tgz", "integrity": "sha1-NgSLv/TntH4TZkQxbJlmnqWukfE=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/arr-union": {"version": "3.1.0", "resolved": "http://registry.npm.taobao.org/arr-union/download/arr-union-3.1.0.tgz", "integrity": "sha1-45sJrqne+Gao8gbiiK9jkZuuOcQ=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/array-filter": {"version": "0.0.1", "resolved": "http://registry.npm.taobao.org/array-filter/download/array-filter-0.0.1.tgz", "integrity": "sha1-fajPLiZijtcygDWB/SH2fKzS7uw=", "dev": true}, "node_modules/array-flatten": {"version": "1.1.1", "resolved": "http://registry.npm.taobao.org/array-flatten/download/array-flatten-1.1.1.tgz", "integrity": "sha1-ml9pkFGx5wczKPKgCJaLZOopVdI=", "dev": true}, "node_modules/array-map": {"version": "0.0.0", "resolved": "http://registry.npm.taobao.org/array-map/download/array-map-0.0.0.tgz", "integrity": "sha1-iKK6tz0c97zVwbEYoAP2b2ZfpmI=", "dev": true}, "node_modules/array-reduce": {"version": "0.0.0", "resolved": "http://registry.npm.taobao.org/array-reduce/download/array-reduce-0.0.0.tgz", "integrity": "sha1-FziZ0//Rx9k4PkR5Ul2+J4yrXys=", "dev": true}, "node_modules/array-union": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/array-union/download/array-union-1.0.2.tgz", "integrity": "sha1-mjRBDk9OPaI96jdb5b5w8kd47Dk=", "dev": true, "dependencies": {"array-uniq": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/array-uniq": {"version": "1.0.3", "resolved": "http://registry.npm.taobao.org/array-uniq/download/array-uniq-1.0.3.tgz", "integrity": "sha1-r2rId6Jcx/dOBYiUdThY39sk/bY=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/array-unique": {"version": "0.3.2", "resolved": "http://registry.npm.taobao.org/array-unique/download/array-unique-0.3.2.tgz", "integrity": "sha1-qJS3XUvE9s1nnvMkSp/Y9Gri1Cg=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/asn1": {"version": "0.2.4", "resolved": "http://registry.npm.taobao.org/asn1/download/asn1-0.2.4.tgz", "integrity": "sha1-jSR136tVO7M+d7VOWeiAu4ziMTY=", "dev": true, "dependencies": {"safer-buffer": "~2.1.0"}}, "node_modules/asn1.js": {"version": "4.10.1", "resolved": "http://registry.npm.taobao.org/asn1.js/download/asn1.js-4.10.1.tgz", "integrity": "sha1-ucK/WAXx5kqt7tbfOiv6+1pz9aA=", "dev": true, "dependencies": {"bn.js": "^4.0.0", "inherits": "^2.0.1", "minimalistic-assert": "^1.0.0"}}, "node_modules/assert": {"version": "1.4.1", "resolved": "http://registry.npm.taobao.org/assert/download/assert-1.4.1.tgz", "integrity": "sha1-mZEtWRg2tab1s0XA8H7vwI/GXZE=", "dev": true, "dependencies": {"util": "0.10.3"}}, "node_modules/assert-plus": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/assert-plus/download/assert-plus-1.0.0.tgz", "integrity": "sha1-8S4PPF13sLHN2RRpQuTpbB5N1SU=", "dev": true, "engines": {"node": ">=0.8"}}, "node_modules/assert/node_modules/inherits": {"version": "2.0.1", "resolved": "http://registry.npm.taobao.org/inherits/download/inherits-2.0.1.tgz", "integrity": "sha1-sX0I0ya0Qj5Wjv9xn5GwscvfafE=", "dev": true}, "node_modules/assert/node_modules/util": {"version": "0.10.3", "resolved": "http://registry.npm.taobao.org/util/download/util-0.10.3.tgz", "integrity": "sha1-evsa/lCAUkZInj23/g7TeTNqwPk=", "dev": true, "dependencies": {"inherits": "2.0.1"}}, "node_modules/assign-symbols": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/assign-symbols/download/assign-symbols-1.0.0.tgz", "integrity": "sha1-WWZ/QfrdTyDMvCu5a41Pf3jsA2c=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/async": {"version": "1.5.2", "resolved": "http://registry.npm.taobao.org/async/download/async-1.5.2.tgz", "integrity": "sha1-7GphrlZIDAw8skHJVhjiCJL5Zyo=", "dev": true}, "node_modules/async-each": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/async-each/download/async-each-1.0.2.tgz", "integrity": "sha1-i4p8oqZY+Sfp8wfW0aQvQZnw9zU=", "dev": true}, "node_modules/async-limiter": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/async-limiter/download/async-limiter-1.0.0.tgz", "integrity": "sha1-ePrtjD0HSrgfIrTphdeehzj3IPg=", "dev": true}, "node_modules/async-validator": {"version": "1.8.5", "resolved": "https://registry.npmjs.org/async-validator/-/async-validator-1.8.5.tgz", "integrity": "sha512-tXBM+1m056MAX0E8TL2iCjg8WvSyXu0Zc8LNtYqrVeyoL3+esHRZ4SieE9fKQyyU09uONjnMEjrNBMqT0mbvmA==", "dependencies": {"babel-runtime": "6.x"}}, "node_modules/asynckit": {"version": "0.4.0", "resolved": "http://registry.npm.taobao.org/asynckit/download/asynckit-0.4.0.tgz", "integrity": "sha1-x57Zf380y48robyXkLzDZkdLS3k=", "dev": true}, "node_modules/atob": {"version": "2.1.2", "resolved": "http://registry.npm.taobao.org/atob/download/atob-2.1.2.tgz", "integrity": "sha1-bZUX654DDSQ2ZmZR6GvZ9vE1M8k=", "bin": {"atob": "bin/atob.js"}, "engines": {"node": ">= 4.5.0"}}, "node_modules/autoprefixer": {"version": "9.5.0", "resolved": "http://registry.npm.taobao.org/autoprefixer/download/autoprefixer-9.5.0.tgz", "integrity": "sha1-flHQNVwRWW5s+aCvyaROhtFZbHA=", "dev": true, "dependencies": {"browserslist": "^4.4.2", "caniuse-lite": "^1.0.30000947", "normalize-range": "^0.1.2", "num2fraction": "^1.2.2", "postcss": "^7.0.14", "postcss-value-parser": "^3.3.1"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "engines": {"node": ">=6.0.0"}}, "node_modules/aws-sign2": {"version": "0.7.0", "resolved": "http://registry.npm.taobao.org/aws-sign2/download/aws-sign2-0.7.0.tgz", "integrity": "sha1-tG6JCTSpWR8tL2+G1+ap8bP+dqg=", "dev": true, "engines": {"node": "*"}}, "node_modules/aws4": {"version": "1.8.0", "resolved": "http://registry.npm.taobao.org/aws4/download/aws4-1.8.0.tgz", "integrity": "sha1-8OAD2cqef1nHpQiUXXsu+aBKVC8=", "dev": true}, "node_modules/axios": {"version": "0.18.0", "resolved": "https://registry.npmjs.org/axios/-/axios-0.18.0.tgz", "integrity": "sha1-MtU+SFHv3AoRmTts0AB4nXDAUQI=", "deprecated": "Critical security vulnerability fixed in v0.21.1. For more information, see https://github.com/axios/axios/pull/3410", "dependencies": {"follow-redirects": "^1.3.0", "is-buffer": "^1.1.5"}}, "node_modules/babel-code-frame": {"version": "6.26.0", "resolved": "http://registry.npm.taobao.org/babel-code-frame/download/babel-code-frame-6.26.0.tgz", "integrity": "sha1-Y/1D99weO7fONZR9uP42mj9Yx0s=", "dev": true, "dependencies": {"chalk": "^1.1.3", "esutils": "^2.0.2", "js-tokens": "^3.0.2"}}, "node_modules/babel-code-frame/node_modules/ansi-regex": {"version": "2.1.1", "resolved": "http://registry.npm.taobao.org/ansi-regex/download/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/babel-code-frame/node_modules/ansi-styles": {"version": "2.2.1", "resolved": "http://registry.npm.taobao.org/ansi-styles/download/ansi-styles-2.2.1.tgz", "integrity": "sha1-tDLdM1i2NM914eRmQ2gkBTPB3b4=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/babel-code-frame/node_modules/chalk": {"version": "1.1.3", "resolved": "http://registry.npm.taobao.org/chalk/download/chalk-1.1.3.tgz", "integrity": "sha1-qBFcVeSnAv5NFQq9OHKCKn4J/Jg=", "dev": true, "dependencies": {"ansi-styles": "^2.2.1", "escape-string-regexp": "^1.0.2", "has-ansi": "^2.0.0", "strip-ansi": "^3.0.0", "supports-color": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/babel-code-frame/node_modules/js-tokens": {"version": "3.0.2", "resolved": "http://registry.npm.taobao.org/js-tokens/download/js-tokens-3.0.2.tgz", "integrity": "sha1-mGbfOVECEw449/mWvOtlRDIJwls=", "dev": true}, "node_modules/babel-code-frame/node_modules/strip-ansi": {"version": "3.0.1", "resolved": "http://registry.npm.taobao.org/strip-ansi/download/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "dev": true, "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/babel-code-frame/node_modules/supports-color": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/supports-color/download/supports-color-2.0.0.tgz", "integrity": "sha1-U10EXOa2Nj+kARcIRimZXp3zJMc=", "dev": true, "engines": {"node": ">=0.8.0"}}, "node_modules/babel-helper-vue-jsx-merge-props": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/babel-helper-vue-jsx-merge-props/-/babel-helper-vue-jsx-merge-props-2.0.3.tgz", "integrity": "sha512-gsLiKK7Qrb7zYJNgiXKpXblxbV5ffSwR0f5whkPAaBAR4fhi6bwRZxX9wBlIc5M/v8CCkXUbXZL4N/nSE97cqg=="}, "node_modules/babel-loader": {"version": "8.0.5", "resolved": "http://registry.npm.taobao.org/babel-loader/download/babel-loader-8.0.5.tgz", "integrity": "sha1-IlMi11CcIVdlWEC7pS5GtsLy/jM=", "dev": true, "dependencies": {"find-cache-dir": "^2.0.0", "loader-utils": "^1.0.2", "mkdirp": "^0.5.1", "util.promisify": "^1.0.0"}, "engines": {"node": ">= 6.9"}, "peerDependencies": {"@babel/core": "^7.0.0", "webpack": ">=2"}}, "node_modules/babel-plugin-dynamic-import-node": {"version": "2.2.0", "resolved": "http://registry.npm.taobao.org/babel-plugin-dynamic-import-node/download/babel-plugin-dynamic-import-node-2.2.0.tgz", "integrity": "sha1-wK37B9lfSkSV6aqsbsOGxNfCUk4=", "dev": true, "dependencies": {"object.assign": "^4.1.0"}}, "node_modules/babel-runtime": {"version": "6.26.0", "resolved": "https://registry.npmjs.org/babel-runtime/-/babel-runtime-6.26.0.tgz", "integrity": "sha512-ITKNuq2wKlW1fJg9sSW52eepoYgZBggvOAHC0u/CYu/qxQ9EVzThCgR69BnSXLHjy2f7SY5zaQ4yt7H9ZVxY2g==", "dependencies": {"core-js": "^2.4.0", "regenerator-runtime": "^0.11.0"}}, "node_modules/babel-runtime/node_modules/regenerator-runtime": {"version": "0.11.1", "resolved": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz", "integrity": "sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg=="}, "node_modules/balanced-match": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/balanced-match/download/balanced-match-1.0.0.tgz", "integrity": "sha1-ibTRmasr7kneFk6gK4nORi1xt2c=", "dev": true}, "node_modules/base": {"version": "0.11.2", "resolved": "http://registry.npm.taobao.org/base/download/base-0.11.2.tgz", "integrity": "sha1-e95c7RRbbVUakNuH+DxVi060io8=", "dev": true, "dependencies": {"cache-base": "^1.0.1", "class-utils": "^0.3.5", "component-emitter": "^1.2.1", "define-property": "^1.0.0", "isobject": "^3.0.1", "mixin-deep": "^1.2.0", "pascalcase": "^0.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/base/node_modules/define-property": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/define-property/download/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "dev": true, "dependencies": {"is-descriptor": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/base/node_modules/is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "deprecated": "Please upgrade to v1.0.1", "dev": true, "dependencies": {"kind-of": "^6.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/base/node_modules/is-data-descriptor": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "deprecated": "Please upgrade to v1.0.1", "dev": true, "dependencies": {"kind-of": "^6.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/base/node_modules/is-descriptor": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/is-descriptor/download/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "dev": true, "dependencies": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/base64-arraybuffer": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/base64-arraybuffer/-/base64-arraybuffer-1.0.2.tgz", "integrity": "sha512-I3yl4r9QB5ZRY3XuJVEPfc2XhZO6YweFPI+UovAzn+8/hb3oJ6lnysaFcjVpkCPfVWFUDvoZ8kmVDP7WyRtYtQ==", "engines": {"node": ">= 0.6.0"}}, "node_modules/base64-js": {"version": "1.3.0", "resolved": "http://registry.npm.taobao.org/base64-js/download/base64-js-1.3.0.tgz", "integrity": "sha1-yrHmEY8FEJXli1KBrqjBzSK/wOM=", "dev": true}, "node_modules/batch": {"version": "0.6.1", "resolved": "http://registry.npm.taobao.org/batch/download/batch-0.6.1.tgz", "integrity": "sha1-3DQxT05nkxgJP8dgJyUl+UvyXBY=", "dev": true}, "node_modules/bcrypt-pbkdf": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/bcrypt-pbkdf/download/bcrypt-pbkdf-1.0.2.tgz", "integrity": "sha1-pDAdOJtqQ/m2f/PKEaP2Y342Dp4=", "dev": true, "dependencies": {"tweetnacl": "^0.14.3"}}, "node_modules/bfj": {"version": "6.1.1", "resolved": "http://registry.npm.taobao.org/bfj/download/bfj-6.1.1.tgz", "integrity": "sha1-BaO3eE+9cs+jwi5WAC75kzZRbEg=", "dev": true, "dependencies": {"bluebird": "^3.5.1", "check-types": "^7.3.0", "hoopy": "^0.1.2", "tryer": "^1.0.0"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/big.js": {"version": "5.2.2", "resolved": "http://registry.npm.taobao.org/big.js/download/big.js-5.2.2.tgz", "integrity": "sha1-ZfCvOC9Xi83HQr2cKB6cstd2gyg=", "dev": true, "engines": {"node": "*"}}, "node_modules/binary-extensions": {"version": "1.13.0", "resolved": "http://registry.npm.taobao.org/binary-extensions/download/binary-extensions-1.13.0.tgz", "integrity": "sha1-lSPgATBqMkRLkHQj8d4hZCIvarE=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/bluebird": {"version": "3.5.3", "resolved": "http://registry.npm.taobao.org/bluebird/download/bluebird-3.5.3.tgz", "integrity": "sha1-fQHG+WFsmlGrD4xUmnnf5uwz76c=", "dev": true}, "node_modules/bn.js": {"version": "4.11.8", "resolved": "http://registry.npm.taobao.org/bn.js/download/bn.js-4.11.8.tgz", "integrity": "sha1-LN4J617jQfSEdGuwMJsyU7GxRC8=", "dev": true}, "node_modules/body-parser": {"version": "1.18.3", "resolved": "http://registry.npm.taobao.org/body-parser/download/body-parser-1.18.3.tgz", "integrity": "sha1-WykhmP/dVTs6DyDe0FkrlWlVyLQ=", "dev": true, "dependencies": {"bytes": "3.0.0", "content-type": "~1.0.4", "debug": "2.6.9", "depd": "~1.1.2", "http-errors": "~1.6.3", "iconv-lite": "0.4.23", "on-finished": "~2.3.0", "qs": "6.5.2", "raw-body": "2.3.3", "type-is": "~1.6.16"}, "engines": {"node": ">= 0.8"}}, "node_modules/body-parser/node_modules/debug": {"version": "2.6.9", "resolved": "http://registry.npm.taobao.org/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "dependencies": {"ms": "2.0.0"}}, "node_modules/body-parser/node_modules/ms": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}, "node_modules/bonjour": {"version": "3.5.0", "resolved": "http://registry.npm.taobao.org/bonjour/download/bonjour-3.5.0.tgz", "integrity": "sha1-jokKGD2O6aI5OzhExpGkK897yfU=", "dev": true, "dependencies": {"array-flatten": "^2.1.0", "deep-equal": "^1.0.1", "dns-equal": "^1.0.0", "dns-txt": "^2.0.2", "multicast-dns": "^6.0.1", "multicast-dns-service-types": "^1.1.0"}}, "node_modules/bonjour/node_modules/array-flatten": {"version": "2.1.2", "resolved": "http://registry.npm.taobao.org/array-flatten/download/array-flatten-2.1.2.tgz", "integrity": "sha1-JO+AoowaiTYX4hSbDG0NeIKTsJk=", "dev": true}, "node_modules/boolbase": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/boolbase/download/boolbase-1.0.0.tgz", "integrity": "sha1-aN/1++YMUes3cl6p4+0xDcwed24=", "dev": true}, "node_modules/brace-expansion": {"version": "1.1.11", "resolved": "http://registry.npm.taobao.org/brace-expansion/download/brace-expansion-1.1.11.tgz", "integrity": "sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=", "dev": true, "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "2.3.2", "resolved": "http://registry.npm.taobao.org/braces/download/braces-2.3.2.tgz", "integrity": "sha1-WXn9PxTNUxVl5fot8av/8d+u5yk=", "dev": true, "dependencies": {"arr-flatten": "^1.1.0", "array-unique": "^0.3.2", "extend-shallow": "^2.0.1", "fill-range": "^4.0.0", "isobject": "^3.0.1", "repeat-element": "^1.1.2", "snapdragon": "^0.8.1", "snapdragon-node": "^2.0.1", "split-string": "^3.0.2", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/braces/node_modules/extend-shallow": {"version": "2.0.1", "resolved": "http://registry.npm.taobao.org/extend-shallow/download/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/brorand": {"version": "1.1.0", "resolved": "http://registry.npm.taobao.org/brorand/download/brorand-1.1.0.tgz", "integrity": "sha1-EsJe/kCkXjwyPrhnWgoM5XsiNx8=", "dev": true}, "node_modules/browserify-aes": {"version": "1.2.0", "resolved": "http://registry.npm.taobao.org/browserify-aes/download/browserify-aes-1.2.0.tgz", "integrity": "sha1-Mmc0ZC9APavDADIJhTu3CtQo70g=", "dev": true, "dependencies": {"buffer-xor": "^1.0.3", "cipher-base": "^1.0.0", "create-hash": "^1.1.0", "evp_bytestokey": "^1.0.3", "inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "node_modules/browserify-cipher": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/browserify-cipher/download/browserify-cipher-1.0.1.tgz", "integrity": "sha1-jWR0wbhwv9q807z8wZNKEOlPFfA=", "dev": true, "dependencies": {"browserify-aes": "^1.0.4", "browserify-des": "^1.0.0", "evp_bytestokey": "^1.0.0"}}, "node_modules/browserify-des": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/browserify-des/download/browserify-des-1.0.2.tgz", "integrity": "sha1-OvTx9Zg5QDVy8cZiBDdfen9wPpw=", "dev": true, "dependencies": {"cipher-base": "^1.0.1", "des.js": "^1.0.0", "inherits": "^2.0.1", "safe-buffer": "^5.1.2"}}, "node_modules/browserify-rsa": {"version": "4.0.1", "resolved": "http://registry.npm.taobao.org/browserify-rsa/download/browserify-rsa-4.0.1.tgz", "integrity": "sha1-IeCr+vbyApzy+vsTNWenAdQTVSQ=", "dev": true, "dependencies": {"bn.js": "^4.1.0", "randombytes": "^2.0.1"}}, "node_modules/browserify-sign": {"version": "4.0.4", "resolved": "http://registry.npm.taobao.org/browserify-sign/download/browserify-sign-4.0.4.tgz", "integrity": "sha1-qk62jl17ZYuqa/alfmMMvXqT0pg=", "dev": true, "dependencies": {"bn.js": "^4.1.1", "browserify-rsa": "^4.0.0", "create-hash": "^1.1.0", "create-hmac": "^1.1.2", "elliptic": "^6.0.0", "inherits": "^2.0.1", "parse-asn1": "^5.0.0"}}, "node_modules/browserify-zlib": {"version": "0.2.0", "resolved": "http://registry.npm.taobao.org/browserify-zlib/download/browserify-zlib-0.2.0.tgz", "integrity": "sha1-KGlFnZqjviRf6P4sofRuLn9U1z8=", "dev": true, "dependencies": {"pako": "~1.0.5"}}, "node_modules/browserslist": {"version": "4.5.2", "resolved": "http://registry.npm.taobao.org/browserslist/download/browserslist-4.5.2.tgz", "integrity": "sha1-Nq0oHwQK9oRVWiPHgPXCCBx1LfA=", "dev": true, "dependencies": {"caniuse-lite": "^1.0.30000951", "electron-to-chromium": "^1.3.116", "node-releases": "^1.1.11"}, "bin": {"browserslist": "cli.js"}}, "node_modules/btoa": {"version": "1.2.1", "resolved": "https://registry.npmjs.org/btoa/-/btoa-1.2.1.tgz", "integrity": "sha512-SB4/MIGlsiVkMcHmT+pSmIPoNDoHg+7cMzmt3Uxt628MTz2487DKSqK/fuhFBrkuqrYv5UCEnACpF4dTFNKc/g==", "bin": {"btoa": "bin/btoa.js"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/buffer": {"version": "4.9.1", "resolved": "http://registry.npm.taobao.org/buffer/download/buffer-4.9.1.tgz", "integrity": "sha1-bRu2AbB6TvztlwlBMgkwJ8lbwpg=", "deprecated": "This version of 'buffer' is out-of-date. You must update to v4.9.2 or newer", "dev": true, "dependencies": {"base64-js": "^1.0.2", "ieee754": "^1.1.4", "isarray": "^1.0.0"}}, "node_modules/buffer-from": {"version": "1.1.1", "resolved": "http://registry.npm.taobao.org/buffer-from/download/buffer-from-1.1.1.tgz", "integrity": "sha1-MnE7wCj3XAL9txDXx7zsHyxgcO8=", "dev": true}, "node_modules/buffer-indexof": {"version": "1.1.1", "resolved": "http://registry.npm.taobao.org/buffer-indexof/download/buffer-indexof-1.1.1.tgz", "integrity": "sha1-Uvq8xqYG0aADAoAmSO9o9jnaJow=", "dev": true}, "node_modules/buffer-xor": {"version": "1.0.3", "resolved": "http://registry.npm.taobao.org/buffer-xor/download/buffer-xor-1.0.3.tgz", "integrity": "sha1-JuYe0UIvtw3ULm42cp7VHYVf6Nk=", "dev": true}, "node_modules/builtin-status-codes": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/builtin-status-codes/download/builtin-status-codes-3.0.0.tgz", "integrity": "sha1-hZgoeOIbmOHGZCXgPQF0eI9Wnug=", "dev": true}, "node_modules/bytes": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/bytes/download/bytes-3.0.0.tgz", "integrity": "sha1-0ygVQE1olpn4Wk6k+odV3ROpYEg=", "dev": true, "engines": {"node": ">= 0.8"}}, "node_modules/cacache": {"version": "11.3.2", "resolved": "http://registry.npm.taobao.org/cacache/download/cacache-11.3.2.tgz", "integrity": "sha1-LYHjCOPSWMo4Eltna5iyrJzmm/o=", "dev": true, "dependencies": {"bluebird": "^3.5.3", "chownr": "^1.1.1", "figgy-pudding": "^3.5.1", "glob": "^7.1.3", "graceful-fs": "^4.1.15", "lru-cache": "^5.1.1", "mississippi": "^3.0.0", "mkdirp": "^0.5.1", "move-concurrently": "^1.0.1", "promise-inflight": "^1.0.1", "rimraf": "^2.6.2", "ssri": "^6.0.1", "unique-filename": "^1.1.1", "y18n": "^4.0.0"}}, "node_modules/cache-base": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/cache-base/download/cache-base-1.0.1.tgz", "integrity": "sha1-Cn9GQWgxyLZi7jb+TnxZ129marI=", "dev": true, "dependencies": {"collection-visit": "^1.0.0", "component-emitter": "^1.2.1", "get-value": "^2.0.6", "has-value": "^1.0.0", "isobject": "^3.0.1", "set-value": "^2.0.0", "to-object-path": "^0.3.0", "union-value": "^1.0.0", "unset-value": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/cache-loader": {"version": "2.0.1", "resolved": "http://registry.npm.taobao.org/cache-loader/download/cache-loader-2.0.1.tgz", "integrity": "sha1-V1j0GmLXwjlB48PHAW5vrrA6ywc=", "dev": true, "dependencies": {"loader-utils": "^1.1.0", "mkdirp": "^0.5.1", "neo-async": "^2.6.0", "normalize-path": "^3.0.0", "schema-utils": "^1.0.0"}, "engines": {"node": ">= 6.9.0"}, "peerDependencies": {"webpack": "^4.0.0"}}, "node_modules/cache-loader/node_modules/schema-utils": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/schema-utils/download/schema-utils-1.0.0.tgz", "integrity": "sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A=", "dev": true, "dependencies": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}, "engines": {"node": ">= 4"}}, "node_modules/call-me-maybe": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/call-me-maybe/download/call-me-maybe-1.0.1.tgz", "integrity": "sha1-JtII6onje1y95gJQoV8DHBak1ms=", "dev": true}, "node_modules/caller-callsite": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/caller-callsite/download/caller-callsite-2.0.0.tgz", "integrity": "sha1-hH4PzgoiN1CpoCfFSzNzGtMVQTQ=", "dev": true, "dependencies": {"callsites": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/caller-path": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/caller-path/download/caller-path-2.0.0.tgz", "integrity": "sha1-Ro+DBE42mrIBD6xfBs7uFbsssfQ=", "dev": true, "dependencies": {"caller-callsite": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/callsites": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/callsites/download/callsites-2.0.0.tgz", "integrity": "sha1-BuuE8A7qQT2oav/vrL/7Ngk7PFA=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/camel-case": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/camel-case/download/camel-case-3.0.0.tgz", "integrity": "sha1-yjw2iKTpzzpM2nd9xNy8cTJJz3M=", "dev": true, "dependencies": {"no-case": "^2.2.0", "upper-case": "^1.1.1"}}, "node_modules/camelcase": {"version": "5.2.0", "resolved": "http://registry.npm.taobao.org/camelcase/download/camelcase-5.2.0.tgz", "integrity": "sha1-51IqvaXtlMwEieG4RmYQ6IQEz0U=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/caniuse-api": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/caniuse-api/download/caniuse-api-3.0.0.tgz", "integrity": "sha1-Xk2Q4idJYdRikZl99Znj7QCO5MA=", "dev": true, "dependencies": {"browserslist": "^4.0.0", "caniuse-lite": "^1.0.0", "lodash.memoize": "^4.1.2", "lodash.uniq": "^4.5.0"}}, "node_modules/caniuse-lite": {"version": "1.0.30000951", "resolved": "http://registry.npm.taobao.org/caniuse-lite/download/caniuse-lite-1.0.30000951.tgz", "integrity": "sha1-x8L9TXEIAoTIZ33UEDaN+Ng2iP4=", "dev": true}, "node_modules/canvg": {"version": "3.0.11", "resolved": "https://registry.npmjs.org/canvg/-/canvg-3.0.11.tgz", "integrity": "sha512-5ON+q7jCTgMp9cjpu4Jo6XbvfYwSB2Ow3kzHKfIyJfaCAOHLbdKPQqGKgfED/R5B+3TFFfe8pegYA+b423SRyA==", "optional": true, "dependencies": {"@babel/runtime": "^7.12.5", "@types/raf": "^3.4.0", "core-js": "^3.8.3", "raf": "^3.4.1", "regenerator-runtime": "^0.13.7", "rgbcolor": "^1.0.1", "stackblur-canvas": "^2.0.0", "svg-pathdata": "^6.0.3"}, "engines": {"node": ">=10.0.0"}}, "node_modules/canvg/node_modules/core-js": {"version": "3.44.0", "resolved": "https://registry.npmjs.org/core-js/-/core-js-3.44.0.tgz", "integrity": "sha512-aFCtd4l6GvAXwVEh3XbbVqJGHDJt0OZRa+5ePGx3LLwi12WfexqQxcsohb2wgsa/92xtl19Hd66G/L+TaAxDMw==", "hasInstallScript": true, "optional": true, "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "node_modules/case-sensitive-paths-webpack-plugin": {"version": "2.2.0", "resolved": "http://registry.npm.taobao.org/case-sensitive-paths-webpack-plugin/download/case-sensitive-paths-webpack-plugin-2.2.0.tgz", "integrity": "sha1-M3HvY2XvnCX6S4HBas4OnH3FjD4=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/caseless": {"version": "0.12.0", "resolved": "http://registry.npm.taobao.org/caseless/download/caseless-0.12.0.tgz", "integrity": "sha1-G2gcIf+EAzyCZUMJBolCDRhxUdw=", "dev": true}, "node_modules/chalk": {"version": "2.4.2", "resolved": "http://registry.npm.taobao.org/chalk/download/chalk-2.4.2.tgz", "integrity": "sha1-zUJUFnelQzPPVBpJEIwUMrRMlCQ=", "dev": true, "dependencies": {"ansi-styles": "^3.2.1", "escape-string-regexp": "^1.0.5", "supports-color": "^5.3.0"}, "engines": {"node": ">=4"}}, "node_modules/check-types": {"version": "7.4.0", "resolved": "http://registry.npm.taobao.org/check-types/download/check-types-7.4.0.tgz", "integrity": "sha1-A3jsG5YW7HH3dJMaPGUW+tjBUvQ=", "dev": true}, "node_modules/chokidar": {"version": "2.1.5", "resolved": "http://registry.npm.taobao.org/chokidar/download/chokidar-2.1.5.tgz?cache=0&other_urls=http%3A%2F%2Fregistry.npm.taobao.org%2Fchokidar%2Fdownload%2Fchokidar-2.1.5.tgz", "integrity": "sha1-CuhDTZYigaX1bHKGnnnLbZ2GrU0=", "dev": true, "dependencies": {"anymatch": "^2.0.0", "async-each": "^1.0.1", "braces": "^2.3.2", "glob-parent": "^3.1.0", "inherits": "^2.0.3", "is-binary-path": "^1.0.0", "is-glob": "^4.0.0", "normalize-path": "^3.0.0", "path-is-absolute": "^1.0.0", "readdirp": "^2.2.1", "upath": "^1.1.1"}, "optionalDependencies": {"fsevents": "^1.2.7"}}, "node_modules/chownr": {"version": "1.1.1", "resolved": "http://registry.npm.taobao.org/chownr/download/chownr-1.1.1.tgz", "integrity": "sha1-VHJri4//TfBTxCGH6AH7RBLfFJQ=", "dev": true}, "node_modules/chrome-trace-event": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/chrome-trace-event/download/chrome-trace-event-1.0.0.tgz", "integrity": "sha1-Rakb0sIMlBHwljtarrmhuV4JzEg=", "dev": true, "dependencies": {"tslib": "^1.9.0"}, "engines": {"node": ">=6.0"}}, "node_modules/ci-info": {"version": "1.6.0", "resolved": "http://registry.npm.taobao.org/ci-info/download/ci-info-1.6.0.tgz", "integrity": "sha1-LKINu5zrMtRSSmgzAzE/AwSx5Jc=", "dev": true}, "node_modules/cipher-base": {"version": "1.0.4", "resolved": "http://registry.npm.taobao.org/cipher-base/download/cipher-base-1.0.4.tgz", "integrity": "sha1-h2Dk7MJy9MNjUy+SbYdKriwTl94=", "dev": true, "dependencies": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}}, "node_modules/class-utils": {"version": "0.3.6", "resolved": "http://registry.npm.taobao.org/class-utils/download/class-utils-0.3.6.tgz", "integrity": "sha1-+TNprouafOAv1B+q0MqDAzGQxGM=", "dev": true, "dependencies": {"arr-union": "^3.1.0", "define-property": "^0.2.5", "isobject": "^3.0.0", "static-extend": "^0.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/class-utils/node_modules/define-property": {"version": "0.2.5", "resolved": "http://registry.npm.taobao.org/define-property/download/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/clean-css": {"version": "4.2.1", "resolved": "http://registry.npm.taobao.org/clean-css/download/clean-css-4.2.1.tgz", "integrity": "sha1-LUEe92uFabbQyEBo2r6FsKpeXBc=", "dev": true, "dependencies": {"source-map": "~0.6.0"}, "engines": {"node": ">= 4.0"}}, "node_modules/clean-css/node_modules/source-map": {"version": "0.6.1", "resolved": "http://registry.npm.taobao.org/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/cli-cursor": {"version": "2.1.0", "resolved": "http://registry.npm.taobao.org/cli-cursor/download/cli-cursor-2.1.0.tgz", "integrity": "sha1-s12sN2R5+sw+lHR9QdDQ9SOP/LU=", "dev": true, "dependencies": {"restore-cursor": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/cli-spinners": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/cli-spinners/download/cli-spinners-2.0.0.tgz", "integrity": "sha1-SweHVvwXqPcgQ/3J8fFL9PqH4t8=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/clipboardy": {"version": "1.2.3", "resolved": "http://registry.npm.taobao.org/clipboardy/download/clipboardy-1.2.3.tgz", "integrity": "sha1-BSY2G/eHJMHyC+JI1CjjZUM8B+8=", "dev": true, "dependencies": {"arch": "^2.1.0", "execa": "^0.8.0"}, "engines": {"node": ">=4"}}, "node_modules/clipboardy/node_modules/cross-spawn": {"version": "5.1.0", "resolved": "http://registry.npm.taobao.org/cross-spawn/download/cross-spawn-5.1.0.tgz", "integrity": "sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk=", "dev": true, "dependencies": {"lru-cache": "^4.0.1", "shebang-command": "^1.2.0", "which": "^1.2.9"}}, "node_modules/clipboardy/node_modules/execa": {"version": "0.8.0", "resolved": "http://registry.npm.taobao.org/execa/download/execa-0.8.0.tgz", "integrity": "sha1-2NdrvBtVIX7RkP1t1J08d07PyNo=", "dev": true, "dependencies": {"cross-spawn": "^5.0.1", "get-stream": "^3.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/clipboardy/node_modules/get-stream": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/get-stream/download/get-stream-3.0.0.tgz", "integrity": "sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/clipboardy/node_modules/lru-cache": {"version": "4.1.5", "resolved": "http://registry.npm.taobao.org/lru-cache/download/lru-cache-4.1.5.tgz", "integrity": "sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80=", "dev": true, "dependencies": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "node_modules/clipboardy/node_modules/yallist": {"version": "2.1.2", "resolved": "http://registry.npm.taobao.org/yallist/download/yallist-2.1.2.tgz", "integrity": "sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=", "dev": true}, "node_modules/cliui": {"version": "4.1.0", "resolved": "http://registry.npm.taobao.org/cliui/download/cliui-4.1.0.tgz", "integrity": "sha1-NIQi2+gtgAswIu709qwQvy5NG0k=", "dev": true, "dependencies": {"string-width": "^2.1.1", "strip-ansi": "^4.0.0", "wrap-ansi": "^2.0.0"}}, "node_modules/cliui/node_modules/ansi-regex": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/ansi-regex/download/ansi-regex-3.0.0.tgz", "integrity": "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/cliui/node_modules/strip-ansi": {"version": "4.0.0", "resolved": "http://registry.npm.taobao.org/strip-ansi/download/strip-ansi-4.0.0.tgz", "integrity": "sha1-qEeQIusaw2iocTibY1JixQXuNo8=", "dev": true, "dependencies": {"ansi-regex": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/clone": {"version": "1.0.4", "resolved": "http://registry.npm.taobao.org/clone/download/clone-1.0.4.tgz", "integrity": "sha1-2jCcwmPfFZlMaIypAheco8fNfH4=", "dev": true, "engines": {"node": ">=0.8"}}, "node_modules/coa": {"version": "2.0.2", "resolved": "http://registry.npm.taobao.org/coa/download/coa-2.0.2.tgz", "integrity": "sha1-Q/bCEVG07yv1cYfbDXPeIp4+fsM=", "dev": true, "dependencies": {"@types/q": "^1.5.1", "chalk": "^2.4.1", "q": "^1.1.2"}, "engines": {"node": ">= 4.0"}}, "node_modules/code-point-at": {"version": "1.1.0", "resolved": "http://registry.npm.taobao.org/code-point-at/download/code-point-at-1.1.0.tgz", "integrity": "sha1-DQcLTQQ6W+ozovGkDi7bPZpMz3c=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/collection-visit": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/collection-visit/download/collection-visit-1.0.0.tgz", "integrity": "sha1-S8A3PBZLwykbTTaMgpzxqApZ3KA=", "dev": true, "dependencies": {"map-visit": "^1.0.0", "object-visit": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/color": {"version": "3.1.0", "resolved": "http://registry.npm.taobao.org/color/download/color-3.1.0.tgz", "integrity": "sha1-2On7CWcyh1d0yEv5IoFd8DCND/w=", "dev": true, "dependencies": {"color-convert": "^1.9.1", "color-string": "^1.5.2"}}, "node_modules/color-convert": {"version": "1.9.3", "resolved": "http://registry.npm.taobao.org/color-convert/download/color-convert-1.9.3.tgz", "integrity": "sha1-u3GFBpDh8TZWfeYp0tVHHe2kweg=", "dev": true, "dependencies": {"color-name": "1.1.3"}}, "node_modules/color-name": {"version": "1.1.3", "resolved": "http://registry.npm.taobao.org/color-name/download/color-name-1.1.3.tgz", "integrity": "sha1-p9BVi9icQveV3UIyj3QIMcpTvCU=", "dev": true}, "node_modules/color-string": {"version": "1.5.3", "resolved": "http://registry.npm.taobao.org/color-string/download/color-string-1.5.3.tgz", "integrity": "sha1-ybvF8BtYtUkvPWhXRZy2WQziBMw=", "dev": true, "dependencies": {"color-name": "^1.0.0", "simple-swizzle": "^0.2.2"}}, "node_modules/combined-stream": {"version": "1.0.7", "resolved": "http://registry.npm.taobao.org/combined-stream/download/combined-stream-1.0.7.tgz", "integrity": "sha1-LR0kMXr7ir6V1tLAsHtXgTU52Cg=", "dev": true, "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "2.19.0", "resolved": "http://registry.npm.taobao.org/commander/download/commander-2.19.0.tgz", "integrity": "sha1-9hmKqE5bg8RgVLlN3tv+1e6f8So=", "dev": true}, "node_modules/commondir": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/commondir/download/commondir-1.0.1.tgz", "integrity": "sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=", "dev": true}, "node_modules/component-emitter": {"version": "1.2.1", "resolved": "http://registry.npm.taobao.org/component-emitter/download/component-emitter-1.2.1.tgz", "integrity": "sha1-E3kY1teCg/ffemt8WmPhQOaUJeY=", "dev": true}, "node_modules/compressible": {"version": "2.0.16", "resolved": "http://registry.npm.taobao.org/compressible/download/compressible-2.0.16.tgz", "integrity": "sha1-pJv5hY84IbZM4b4Clq/HOARmp38=", "dev": true, "dependencies": {"mime-db": ">= 1.38.0 < 2"}, "engines": {"node": ">= 0.6"}}, "node_modules/compression": {"version": "1.7.4", "resolved": "http://registry.npm.taobao.org/compression/download/compression-1.7.4.tgz", "integrity": "sha1-lVI+/xcMpXwpoMpB5v4TH0Hlu48=", "dev": true, "dependencies": {"accepts": "~1.3.5", "bytes": "3.0.0", "compressible": "~2.0.16", "debug": "2.6.9", "on-headers": "~1.0.2", "safe-buffer": "5.1.2", "vary": "~1.1.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/compression/node_modules/debug": {"version": "2.6.9", "resolved": "http://registry.npm.taobao.org/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "dependencies": {"ms": "2.0.0"}}, "node_modules/compression/node_modules/ms": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "http://registry.npm.taobao.org/concat-map/download/concat-map-0.0.1.tgz", "integrity": "sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=", "dev": true}, "node_modules/concat-stream": {"version": "1.6.2", "resolved": "http://registry.npm.taobao.org/concat-stream/download/concat-stream-1.6.2.tgz", "integrity": "sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=", "dev": true, "engines": ["node >= 0.8"], "dependencies": {"buffer-from": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^2.2.2", "typedarray": "^0.0.6"}}, "node_modules/connect-history-api-fallback": {"version": "1.6.0", "resolved": "http://registry.npm.taobao.org/connect-history-api-fallback/download/connect-history-api-fallback-1.6.0.tgz", "integrity": "sha1-izIIk1kwjRERFdgcrT/Oq4iPl7w=", "dev": true, "engines": {"node": ">=0.8"}}, "node_modules/console-browserify": {"version": "1.1.0", "resolved": "http://registry.npm.taobao.org/console-browserify/download/console-browserify-1.1.0.tgz", "integrity": "sha1-8CQcRXMKn8YyOyBtvzjtx0HQuxA=", "dev": true, "dependencies": {"date-now": "^0.1.4"}}, "node_modules/consolidate": {"version": "0.15.1", "resolved": "http://registry.npm.taobao.org/consolidate/download/consolidate-0.15.1.tgz", "integrity": "sha1-IasEMjXHGgfUXZqtmFk7DbpWurc=", "deprecated": "Please upgrade to consolidate v1.0.0+ as it has been modernized with several long-awaited fixes implemented. Maintenance is supported by Forward Email at https://forwardemail.net ; follow/watch https://github.com/ladjs/consolidate for updates and release changelog", "dev": true, "dependencies": {"bluebird": "^3.1.1"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/constants-browserify": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/constants-browserify/download/constants-browserify-1.0.0.tgz", "integrity": "sha1-wguW2MYXdIqvHBYCF2DNJ/y4y3U=", "dev": true}, "node_modules/content-disposition": {"version": "0.5.2", "resolved": "http://registry.npm.taobao.org/content-disposition/download/content-disposition-0.5.2.tgz", "integrity": "sha1-DPaLud318r55YcOoUXjLhdunjLQ=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/content-type": {"version": "1.0.4", "resolved": "http://registry.npm.taobao.org/content-type/download/content-type-1.0.4.tgz", "integrity": "sha1-4TjMdeBAxyexlm/l5fjJruJW/js=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/convert-source-map": {"version": "1.6.0", "resolved": "http://registry.npm.taobao.org/convert-source-map/download/convert-source-map-1.6.0.tgz", "integrity": "sha1-UbU3qMQ+DwTewZk7/83VBOdYrCA=", "dev": true, "dependencies": {"safe-buffer": "~5.1.1"}}, "node_modules/cookie": {"version": "0.3.1", "resolved": "http://registry.npm.taobao.org/cookie/download/cookie-0.3.1.tgz", "integrity": "sha1-5+Ch+e9DtMi6klxcWpboBtFoc7s=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/cookie-signature": {"version": "1.0.6", "resolved": "http://registry.npm.taobao.org/cookie-signature/download/cookie-signature-1.0.6.tgz", "integrity": "sha1-4wOogrNCzD7oylE6eZmXNNqzriw=", "dev": true}, "node_modules/copy-concurrently": {"version": "1.0.5", "resolved": "http://registry.npm.taobao.org/copy-concurrently/download/copy-concurrently-1.0.5.tgz", "integrity": "sha1-kilzmMrjSTf8r9bsgTnBgFHwteA=", "deprecated": "This package is no longer supported.", "dev": true, "dependencies": {"aproba": "^1.1.1", "fs-write-stream-atomic": "^1.0.8", "iferr": "^0.1.5", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.0"}}, "node_modules/copy-descriptor": {"version": "0.1.1", "resolved": "http://registry.npm.taobao.org/copy-descriptor/download/copy-descriptor-0.1.1.tgz", "integrity": "sha1-Z29us8OZl8LuGsOpJP1hJHSPV40=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/copy-webpack-plugin": {"version": "4.6.0", "resolved": "http://registry.npm.taobao.org/copy-webpack-plugin/download/copy-webpack-plugin-4.6.0.tgz", "integrity": "sha1-5/QN2KaEd9QF3Rt6hUquMksVi64=", "dev": true, "dependencies": {"cacache": "^10.0.4", "find-cache-dir": "^1.0.0", "globby": "^7.1.1", "is-glob": "^4.0.0", "loader-utils": "^1.1.0", "minimatch": "^3.0.4", "p-limit": "^1.0.0", "serialize-javascript": "^1.4.0"}, "engines": {"node": ">= 4"}}, "node_modules/copy-webpack-plugin/node_modules/cacache": {"version": "10.0.4", "resolved": "http://registry.npm.taobao.org/cacache/download/cacache-10.0.4.tgz", "integrity": "sha1-ZFI2eZnv+dQYiu/ZoU6dfGomNGA=", "dev": true, "dependencies": {"bluebird": "^3.5.1", "chownr": "^1.0.1", "glob": "^7.1.2", "graceful-fs": "^4.1.11", "lru-cache": "^4.1.1", "mississippi": "^2.0.0", "mkdirp": "^0.5.1", "move-concurrently": "^1.0.1", "promise-inflight": "^1.0.1", "rimraf": "^2.6.2", "ssri": "^5.2.4", "unique-filename": "^1.1.0", "y18n": "^4.0.0"}}, "node_modules/copy-webpack-plugin/node_modules/find-cache-dir": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/find-cache-dir/download/find-cache-dir-1.0.0.tgz", "integrity": "sha1-kojj6ePMN0hxfTnq3hfPcfww7m8=", "dev": true, "dependencies": {"commondir": "^1.0.1", "make-dir": "^1.0.0", "pkg-dir": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/copy-webpack-plugin/node_modules/find-up": {"version": "2.1.0", "resolved": "http://registry.npm.taobao.org/find-up/download/find-up-2.1.0.tgz", "integrity": "sha1-RdG35QbHF93UgndaK3eSCjwMV6c=", "dev": true, "dependencies": {"locate-path": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/copy-webpack-plugin/node_modules/globby": {"version": "7.1.1", "resolved": "http://registry.npm.taobao.org/globby/download/globby-7.1.1.tgz", "integrity": "sha1-+yzP+UAfhgCUXfral0QMypcrhoA=", "dev": true, "dependencies": {"array-union": "^1.0.1", "dir-glob": "^2.0.0", "glob": "^7.1.2", "ignore": "^3.3.5", "pify": "^3.0.0", "slash": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/copy-webpack-plugin/node_modules/locate-path": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/locate-path/download/locate-path-2.0.0.tgz", "integrity": "sha1-K1aLJl7slExtnA3pw9u7ygNUzY4=", "dev": true, "dependencies": {"p-locate": "^2.0.0", "path-exists": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/copy-webpack-plugin/node_modules/lru-cache": {"version": "4.1.5", "resolved": "http://registry.npm.taobao.org/lru-cache/download/lru-cache-4.1.5.tgz", "integrity": "sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80=", "dev": true, "dependencies": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "node_modules/copy-webpack-plugin/node_modules/make-dir": {"version": "1.3.0", "resolved": "http://registry.npm.taobao.org/make-dir/download/make-dir-1.3.0.tgz", "integrity": "sha1-ecEDO4BRW9bSTsmTPoYMp17ifww=", "dev": true, "dependencies": {"pify": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/copy-webpack-plugin/node_modules/mississippi": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/mississippi/download/mississippi-2.0.0.tgz", "integrity": "sha1-NEKlCPr8KFAEhv7qmUCWduTuWm8=", "dev": true, "dependencies": {"concat-stream": "^1.5.0", "duplexify": "^3.4.2", "end-of-stream": "^1.1.0", "flush-write-stream": "^1.0.0", "from2": "^2.1.0", "parallel-transform": "^1.1.0", "pump": "^2.0.1", "pumpify": "^1.3.3", "stream-each": "^1.1.0", "through2": "^2.0.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/copy-webpack-plugin/node_modules/p-limit": {"version": "1.3.0", "resolved": "http://registry.npm.taobao.org/p-limit/download/p-limit-1.3.0.tgz", "integrity": "sha1-uGvV8MJWkJEcdZD8v8IBDVSzzLg=", "dev": true, "dependencies": {"p-try": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/copy-webpack-plugin/node_modules/p-locate": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/p-locate/download/p-locate-2.0.0.tgz", "integrity": "sha1-IKAQOyIqcMj9OcwuWAaA893l7EM=", "dev": true, "dependencies": {"p-limit": "^1.1.0"}, "engines": {"node": ">=4"}}, "node_modules/copy-webpack-plugin/node_modules/p-try": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/p-try/download/p-try-1.0.0.tgz", "integrity": "sha1-y8ec26+P1CKOE/Yh8rGiN8GyB7M=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/copy-webpack-plugin/node_modules/pify": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/pify/download/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/copy-webpack-plugin/node_modules/pkg-dir": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/pkg-dir/download/pkg-dir-2.0.0.tgz", "integrity": "sha1-9tXREJ4Z1j7fQo4L1X4Sd3YVM0s=", "dev": true, "dependencies": {"find-up": "^2.1.0"}, "engines": {"node": ">=4"}}, "node_modules/copy-webpack-plugin/node_modules/pump": {"version": "2.0.1", "resolved": "http://registry.npm.taobao.org/pump/download/pump-2.0.1.tgz", "integrity": "sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk=", "dev": true, "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/copy-webpack-plugin/node_modules/slash": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/slash/download/slash-1.0.0.tgz", "integrity": "sha1-xB8vbDn8FtHNF61LXYlhFK5HDVU=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/copy-webpack-plugin/node_modules/ssri": {"version": "5.3.0", "resolved": "http://registry.npm.taobao.org/ssri/download/ssri-5.3.0.tgz", "integrity": "sha1-ujhyycbTOgcEp9cf8EXl7EiZnQY=", "dev": true, "dependencies": {"safe-buffer": "^5.1.1"}}, "node_modules/copy-webpack-plugin/node_modules/yallist": {"version": "2.1.2", "resolved": "http://registry.npm.taobao.org/yallist/download/yallist-2.1.2.tgz", "integrity": "sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=", "dev": true}, "node_modules/core-js": {"version": "2.6.5", "resolved": "http://registry.npm.taobao.org/core-js/download/core-js-2.6.5.tgz", "integrity": "sha1-RLyNJJ5/sv9dAOA0Gn/7lPv2eJU=", "deprecated": "core-js@<3.23.3 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Some versions have web compatibility issues. Please, upgrade your dependencies to the actual version of core-js."}, "node_modules/core-js-compat": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/core-js-compat/download/core-js-compat-3.0.0.tgz", "integrity": "sha1-zZgQuAAHQlNaSkN3OGYYXjEL1Pc=", "dev": true, "dependencies": {"browserslist": "^4.5.1", "core-js": "3.0.0", "core-js-pure": "3.0.0", "semver": "^5.6.0"}}, "node_modules/core-js-compat/node_modules/core-js": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/core-js/download/core-js-3.0.0.tgz", "integrity": "sha1-qNv6l40pv8Jjv7ZsVW0MqSTCiVc=", "deprecated": "core-js@<3.23.3 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Some versions have web compatibility issues. Please, upgrade your dependencies to the actual version of core-js.", "dev": true}, "node_modules/core-js-pure": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/core-js-pure/download/core-js-pure-3.0.0.tgz", "integrity": "sha1-pWea20h1QnyMBIivyT5vW3ElhZs=", "deprecated": "core-js-pure@<3.23.3 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Some versions have web compatibility issues. Please, upgrade your dependencies to the actual version of core-js-pure.", "dev": true}, "node_modules/core-util-is": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/core-util-is/download/core-util-is-1.0.2.tgz", "integrity": "sha1-tf1UIgqivFq1eqtxQMlAdUUDwac=", "dev": true}, "node_modules/cosmiconfig": {"version": "5.2.0", "resolved": "http://registry.npm.taobao.org/cosmiconfig/download/cosmiconfig-5.2.0.tgz", "integrity": "sha1-RQOOTSin/nhyA67enCW8pKCLEsg=", "dev": true, "dependencies": {"import-fresh": "^2.0.0", "is-directory": "^0.3.1", "js-yaml": "^3.13.0", "parse-json": "^4.0.0"}, "engines": {"node": ">=4"}}, "node_modules/create-ecdh": {"version": "4.0.3", "resolved": "http://registry.npm.taobao.org/create-ecdh/download/create-ecdh-4.0.3.tgz", "integrity": "sha1-yREbbzMEXEaX8UR4f5JUzcd8Rf8=", "dev": true, "dependencies": {"bn.js": "^4.1.0", "elliptic": "^6.0.0"}}, "node_modules/create-hash": {"version": "1.2.0", "resolved": "http://registry.npm.taobao.org/create-hash/download/create-hash-1.2.0.tgz", "integrity": "sha1-iJB4rxGmN1a8+1m9IhmWvjqe8ZY=", "dev": true, "dependencies": {"cipher-base": "^1.0.1", "inherits": "^2.0.1", "md5.js": "^1.3.4", "ripemd160": "^2.0.1", "sha.js": "^2.4.0"}}, "node_modules/create-hmac": {"version": "1.1.7", "resolved": "http://registry.npm.taobao.org/create-hmac/download/create-hmac-1.1.7.tgz", "integrity": "sha1-aRcMeLOrlXFHsriwRXLkfq0iQ/8=", "dev": true, "dependencies": {"cipher-base": "^1.0.3", "create-hash": "^1.1.0", "inherits": "^2.0.1", "ripemd160": "^2.0.0", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}}, "node_modules/cross-spawn": {"version": "6.0.5", "resolved": "http://registry.npm.taobao.org/cross-spawn/download/cross-spawn-6.0.5.tgz", "integrity": "sha1-Sl7Hxk364iw6FBJNus3uhG2Ay8Q=", "dev": true, "dependencies": {"nice-try": "^1.0.4", "path-key": "^2.0.1", "semver": "^5.5.0", "shebang-command": "^1.2.0", "which": "^1.2.9"}, "engines": {"node": ">=4.8"}}, "node_modules/crypto-browserify": {"version": "3.12.0", "resolved": "http://registry.npm.taobao.org/crypto-browserify/download/crypto-browserify-3.12.0.tgz", "integrity": "sha1-OWz58xN/A+S45TLFj2mCVOAPgOw=", "dev": true, "dependencies": {"browserify-cipher": "^1.0.0", "browserify-sign": "^4.0.0", "create-ecdh": "^4.0.0", "create-hash": "^1.1.0", "create-hmac": "^1.1.0", "diffie-hellman": "^5.0.0", "inherits": "^2.0.1", "pbkdf2": "^3.0.3", "public-encrypt": "^4.0.0", "randombytes": "^2.0.0", "randomfill": "^1.0.3"}, "engines": {"node": "*"}}, "node_modules/css-color-names": {"version": "0.0.4", "resolved": "http://registry.npm.taobao.org/css-color-names/download/css-color-names-0.0.4.tgz", "integrity": "sha1-gIrcLnnPhHOAabZGyyDsJ762KeA=", "dev": true, "engines": {"node": "*"}}, "node_modules/css-declaration-sorter": {"version": "4.0.1", "resolved": "http://registry.npm.taobao.org/css-declaration-sorter/download/css-declaration-sorter-4.0.1.tgz", "integrity": "sha1-wZiUD2OnbX42wecQGLABchBUyyI=", "dev": true, "dependencies": {"postcss": "^7.0.1", "timsort": "^0.3.0"}, "engines": {"node": ">4"}}, "node_modules/css-line-break": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/css-line-break/-/css-line-break-2.1.0.tgz", "integrity": "sha512-FHcKFCZcAha3LwfVBhCQbW2nCNbkZXn7KVUJcsT5/P8YmfsVja0FMPJr0B903j/E69HUphKiV9iQArX8SDYA4w==", "dependencies": {"utrie": "^1.0.2"}}, "node_modules/css-loader": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/css-loader/download/css-loader-1.0.1.tgz", "integrity": "sha1-aIW7UjOzXsR7AGBX2gHMZAtref4=", "dev": true, "dependencies": {"babel-code-frame": "^6.26.0", "css-selector-tokenizer": "^0.7.0", "icss-utils": "^2.1.0", "loader-utils": "^1.0.2", "lodash": "^4.17.11", "postcss": "^6.0.23", "postcss-modules-extract-imports": "^1.2.0", "postcss-modules-local-by-default": "^1.2.0", "postcss-modules-scope": "^1.1.0", "postcss-modules-values": "^1.3.0", "postcss-value-parser": "^3.3.0", "source-list-map": "^2.0.0"}, "engines": {"node": ">= 6.9.0 <7.0.0 || >= 8.9.0"}, "peerDependencies": {"webpack": "^4.0.0"}}, "node_modules/css-loader/node_modules/postcss": {"version": "6.0.23", "resolved": "http://registry.npm.taobao.org/postcss/download/postcss-6.0.23.tgz", "integrity": "sha1-YcgswyisYOZ3ZF+XkFTrmLwOMyQ=", "dev": true, "dependencies": {"chalk": "^2.4.1", "source-map": "^0.6.1", "supports-color": "^5.4.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/css-loader/node_modules/source-map": {"version": "0.6.1", "resolved": "http://registry.npm.taobao.org/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/css-select": {"version": "2.0.2", "resolved": "http://registry.npm.taobao.org/css-select/download/css-select-2.0.2.tgz", "integrity": "sha1-q0OGzsnh9miFVWSxfDcztDsqXt4=", "dev": true, "dependencies": {"boolbase": "^1.0.0", "css-what": "^2.1.2", "domutils": "^1.7.0", "nth-check": "^1.0.2"}}, "node_modules/css-select-base-adapter": {"version": "0.1.1", "resolved": "http://registry.npm.taobao.org/css-select-base-adapter/download/css-select-base-adapter-0.1.1.tgz", "integrity": "sha1-Oy/0lyzDYquIVhUHqVQIoUMhNdc=", "dev": true}, "node_modules/css-selector-tokenizer": {"version": "0.7.1", "resolved": "http://registry.npm.taobao.org/css-selector-tokenizer/download/css-selector-tokenizer-0.7.1.tgz", "integrity": "sha1-oXcnGovKUBkXL0+JH8bu2cv2jV0=", "dev": true, "dependencies": {"cssesc": "^0.1.0", "fastparse": "^1.1.1", "regexpu-core": "^1.0.0"}}, "node_modules/css-selector-tokenizer/node_modules/cssesc": {"version": "0.1.0", "resolved": "http://registry.npm.taobao.org/cssesc/download/cssesc-0.1.0.tgz", "integrity": "sha1-yBSQPkViM3GgR3tAEJqq++6t27Q=", "dev": true, "bin": {"cssesc": "bin/cssesc"}}, "node_modules/css-selector-tokenizer/node_modules/jsesc": {"version": "0.5.0", "resolved": "http://registry.npm.taobao.org/jsesc/download/jsesc-0.5.0.tgz", "integrity": "sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=", "dev": true, "bin": {"jsesc": "bin/jsesc"}}, "node_modules/css-selector-tokenizer/node_modules/regexpu-core": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/regexpu-core/download/regexpu-core-1.0.0.tgz", "integrity": "sha1-hqdj9Y7k18L2sQLkdkBQ3n7ZDGs=", "dev": true, "dependencies": {"regenerate": "^1.2.1", "regjsgen": "^0.2.0", "regjsparser": "^0.1.4"}}, "node_modules/css-selector-tokenizer/node_modules/regjsgen": {"version": "0.2.0", "resolved": "http://registry.npm.taobao.org/regjsgen/download/regjsgen-0.2.0.tgz", "integrity": "sha1-bAFq3qxVT3WCP+N6wFuS1aTtsfc=", "dev": true}, "node_modules/css-selector-tokenizer/node_modules/regjsparser": {"version": "0.1.5", "resolved": "http://registry.npm.taobao.org/regjsparser/download/regjsparser-0.1.5.tgz", "integrity": "sha1-fuj4Tcb6eS0/0K4ijSS9lJ6tIFw=", "dev": true, "dependencies": {"jsesc": "~0.5.0"}, "bin": {"regjsparser": "bin/parser"}}, "node_modules/css-tree": {"version": "1.0.0-alpha.28", "resolved": "http://registry.npm.taobao.org/css-tree/download/css-tree-1.0.0-alpha.28.tgz", "integrity": "sha1-joloGQ2IbJR3vI1h6W9hrz9/+n8=", "dev": true, "dependencies": {"mdn-data": "~1.1.0", "source-map": "^0.5.3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/css-unit-converter": {"version": "1.1.1", "resolved": "http://registry.npm.taobao.org/css-unit-converter/download/css-unit-converter-1.1.1.tgz", "integrity": "sha1-2bkoGtz9jO2TW9urqDeGiX9k6ZY=", "dev": true}, "node_modules/css-url-regex": {"version": "1.1.0", "resolved": "http://registry.npm.taobao.org/css-url-regex/download/css-url-regex-1.1.0.tgz", "integrity": "sha1-g4NCMMyfdMRX3lnuvRVD/uuDt+w=", "dev": true}, "node_modules/css-what": {"version": "2.1.3", "resolved": "http://registry.npm.taobao.org/css-what/download/css-what-2.1.3.tgz", "integrity": "sha1-ptdgRXM2X+dGhsPzEcVlE9iChfI=", "dev": true, "engines": {"node": "*"}}, "node_modules/cssesc": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/cssesc/download/cssesc-2.0.0.tgz", "integrity": "sha1-OxO9G7HLNuG8taTc0n9UxdyzVwM=", "dev": true, "bin": {"cssesc": "bin/cssesc"}, "engines": {"node": ">=4"}}, "node_modules/cssnano": {"version": "4.1.10", "resolved": "http://registry.npm.taobao.org/cssnano/download/cssnano-4.1.10.tgz", "integrity": "sha1-CsQfCxPRPUZUh+ERt3jULaYxuLI=", "dev": true, "dependencies": {"cosmiconfig": "^5.0.0", "cssnano-preset-default": "^4.0.7", "is-resolvable": "^1.0.0", "postcss": "^7.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/cssnano-preset-default": {"version": "4.0.7", "resolved": "http://registry.npm.taobao.org/cssnano-preset-default/download/cssnano-preset-default-4.0.7.tgz", "integrity": "sha1-UexmLM/KD4izltzZZ5zbkxvhf3Y=", "dev": true, "dependencies": {"css-declaration-sorter": "^4.0.1", "cssnano-util-raw-cache": "^4.0.1", "postcss": "^7.0.0", "postcss-calc": "^7.0.1", "postcss-colormin": "^4.0.3", "postcss-convert-values": "^4.0.1", "postcss-discard-comments": "^4.0.2", "postcss-discard-duplicates": "^4.0.2", "postcss-discard-empty": "^4.0.1", "postcss-discard-overridden": "^4.0.1", "postcss-merge-longhand": "^4.0.11", "postcss-merge-rules": "^4.0.3", "postcss-minify-font-values": "^4.0.2", "postcss-minify-gradients": "^4.0.2", "postcss-minify-params": "^4.0.2", "postcss-minify-selectors": "^4.0.2", "postcss-normalize-charset": "^4.0.1", "postcss-normalize-display-values": "^4.0.2", "postcss-normalize-positions": "^4.0.2", "postcss-normalize-repeat-style": "^4.0.2", "postcss-normalize-string": "^4.0.2", "postcss-normalize-timing-functions": "^4.0.2", "postcss-normalize-unicode": "^4.0.1", "postcss-normalize-url": "^4.0.1", "postcss-normalize-whitespace": "^4.0.2", "postcss-ordered-values": "^4.1.2", "postcss-reduce-initial": "^4.0.3", "postcss-reduce-transforms": "^4.0.2", "postcss-svgo": "^4.0.2", "postcss-unique-selectors": "^4.0.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/cssnano-util-get-arguments": {"version": "4.0.0", "resolved": "http://registry.npm.taobao.org/cssnano-util-get-arguments/download/cssnano-util-get-arguments-4.0.0.tgz", "integrity": "sha1-7ToIKZ8h11dBsg87gfGU7UnMFQ8=", "dev": true, "engines": {"node": ">=6.9.0"}}, "node_modules/cssnano-util-get-match": {"version": "4.0.0", "resolved": "http://registry.npm.taobao.org/cssnano-util-get-match/download/cssnano-util-get-match-4.0.0.tgz", "integrity": "sha1-wOTKB/U4a7F+xeUiULT1lhNlFW0=", "dev": true, "engines": {"node": ">=6.9.0"}}, "node_modules/cssnano-util-raw-cache": {"version": "4.0.1", "resolved": "http://registry.npm.taobao.org/cssnano-util-raw-cache/download/cssnano-util-raw-cache-4.0.1.tgz", "integrity": "sha1-sm1f1fcqEd/np4RvtMZyYPlr8oI=", "dev": true, "dependencies": {"postcss": "^7.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/cssnano-util-same-parent": {"version": "4.0.1", "resolved": "http://registry.npm.taobao.org/cssnano-util-same-parent/download/cssnano-util-same-parent-4.0.1.tgz", "integrity": "sha1-V0CC+yhZ0ttDOFWDXZqEVuoYu/M=", "dev": true, "engines": {"node": ">=6.9.0"}}, "node_modules/csso": {"version": "3.5.1", "resolved": "http://registry.npm.taobao.org/csso/download/csso-3.5.1.tgz", "integrity": "sha1-e564vmFiiXPBsmHhadLwJACOdYs=", "dev": true, "dependencies": {"css-tree": "1.0.0-alpha.29"}, "engines": {"node": ">=0.10.0"}}, "node_modules/csso/node_modules/css-tree": {"version": "1.0.0-alpha.29", "resolved": "http://registry.npm.taobao.org/css-tree/download/css-tree-1.0.0-alpha.29.tgz", "integrity": "sha1-P6nU7zFCy9HDAedmTB81K9gvWjk=", "dev": true, "dependencies": {"mdn-data": "~1.1.0", "source-map": "^0.5.3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/cyclist": {"version": "0.2.2", "resolved": "http://registry.npm.taobao.org/cyclist/download/cyclist-0.2.2.tgz", "integrity": "sha1-GzN5LhHpFKL9bW7WRHRkRE5fpkA=", "dev": true}, "node_modules/dashdash": {"version": "1.14.1", "resolved": "http://registry.npm.taobao.org/dashdash/download/dashdash-1.14.1.tgz", "integrity": "sha1-hTz6D3y+L+1d4gMmuN1YEDX24vA=", "dev": true, "dependencies": {"assert-plus": "^1.0.0"}, "engines": {"node": ">=0.10"}}, "node_modules/date-now": {"version": "0.1.4", "resolved": "http://registry.npm.taobao.org/date-now/download/date-now-0.1.4.tgz", "integrity": "sha1-6vQ5/U1ISK105cx9vvIAZyueNFs=", "dev": true}, "node_modules/de-indent": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/de-indent/download/de-indent-1.0.2.tgz", "integrity": "sha1-sgOOhG3DO6pXlhKNCAS0VbjB4h0=", "dev": true}, "node_modules/debug": {"version": "4.1.1", "resolved": "http://registry.npm.taobao.org/debug/download/debug-4.1.1.tgz", "integrity": "sha1-O3ImAlUQnGtYnO4FDx1RYTlmR5E=", "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "dev": true, "dependencies": {"ms": "^2.1.1"}}, "node_modules/decamelize": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/decamelize/download/decamelize-2.0.0.tgz", "integrity": "sha1-ZW17vICUxMeI6lPFhAkIycfQY8c=", "dev": true, "dependencies": {"xregexp": "4.0.0"}, "engines": {"node": ">=4"}}, "node_modules/decode-uri-component": {"version": "0.2.0", "resolved": "http://registry.npm.taobao.org/decode-uri-component/download/decode-uri-component-0.2.0.tgz", "integrity": "sha1-6zkTMzRYd1y4TNGh+uBiEGu4dUU=", "dev": true, "engines": {"node": ">=0.10"}}, "node_modules/deep-equal": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/deep-equal/download/deep-equal-1.0.1.tgz", "integrity": "sha1-9dJgKStmDghO/0zbyfCK0yR0SLU=", "dev": true}, "node_modules/deepmerge": {"version": "1.5.2", "resolved": "http://registry.npm.taobao.org/deepmerge/download/deepmerge-1.5.2.tgz", "integrity": "sha1-EEmdhohEza1P7ghC34x/bwyVp1M=", "engines": {"node": ">=0.10.0"}}, "node_modules/default-gateway": {"version": "4.2.0", "resolved": "http://registry.npm.taobao.org/default-gateway/download/default-gateway-4.2.0.tgz", "integrity": "sha1-FnEEx1AMIRX23WmwpTa7jtcgVSs=", "dev": true, "dependencies": {"execa": "^1.0.0", "ip-regex": "^2.1.0"}, "engines": {"node": ">=6"}}, "node_modules/defaults": {"version": "1.0.3", "resolved": "http://registry.npm.taobao.org/defaults/download/defaults-1.0.3.tgz", "integrity": "sha1-xlYFHpgX2f8I7YgUd/P+QBnz730=", "dev": true, "dependencies": {"clone": "^1.0.2"}}, "node_modules/define-properties": {"version": "1.1.3", "resolved": "http://registry.npm.taobao.org/define-properties/download/define-properties-1.1.3.tgz", "integrity": "sha1-z4jabL7ib+bbcJT2HYcMvYTO6fE=", "dev": true, "dependencies": {"object-keys": "^1.0.12"}, "engines": {"node": ">= 0.4"}}, "node_modules/define-property": {"version": "2.0.2", "resolved": "http://registry.npm.taobao.org/define-property/download/define-property-2.0.2.tgz", "integrity": "sha1-1Flono1lS6d+AqgX+HENcCyxbp0=", "dev": true, "dependencies": {"is-descriptor": "^1.0.2", "isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/define-property/node_modules/is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "deprecated": "Please upgrade to v1.0.1", "dev": true, "dependencies": {"kind-of": "^6.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/define-property/node_modules/is-data-descriptor": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "deprecated": "Please upgrade to v1.0.1", "dev": true, "dependencies": {"kind-of": "^6.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/define-property/node_modules/is-descriptor": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/is-descriptor/download/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "dev": true, "dependencies": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/del": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/del/download/del-3.0.0.tgz", "integrity": "sha1-U+z2mf/LyzljdpGrE7rxYIGXZuU=", "dev": true, "dependencies": {"globby": "^6.1.0", "is-path-cwd": "^1.0.0", "is-path-in-cwd": "^1.0.0", "p-map": "^1.1.1", "pify": "^3.0.0", "rimraf": "^2.2.8"}, "engines": {"node": ">=4"}}, "node_modules/del/node_modules/globby": {"version": "6.1.0", "resolved": "http://registry.npm.taobao.org/globby/download/globby-6.1.0.tgz", "integrity": "sha1-9abXDoOV4hyFj7BInWTfAkJNUGw=", "dev": true, "dependencies": {"array-union": "^1.0.1", "glob": "^7.0.3", "object-assign": "^4.0.1", "pify": "^2.0.0", "pinkie-promise": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/del/node_modules/globby/node_modules/pify": {"version": "2.3.0", "resolved": "http://registry.npm.taobao.org/pify/download/pify-2.3.0.tgz", "integrity": "sha1-7RQaasBDqEnqWISY59yosVMw6Qw=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/del/node_modules/pify": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/pify/download/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/delayed-stream": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/delayed-stream/download/delayed-stream-1.0.0.tgz", "integrity": "sha1-3zrhmayt+31ECqrgsp4icrJOxhk=", "dev": true, "engines": {"node": ">=0.4.0"}}, "node_modules/depd": {"version": "1.1.2", "resolved": "http://registry.npm.taobao.org/depd/download/depd-1.1.2.tgz", "integrity": "sha1-m81S4UwJd2PnSbJ0xDRu0uVgtak=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/des.js": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/des.js/download/des.js-1.0.0.tgz", "integrity": "sha1-wHTS4qpqipoH29YfmhXCzYPsjsw=", "dev": true, "dependencies": {"inherits": "^2.0.1", "minimalistic-assert": "^1.0.0"}}, "node_modules/destroy": {"version": "1.0.4", "resolved": "http://registry.npm.taobao.org/destroy/download/destroy-1.0.4.tgz", "integrity": "sha1-l4hXRCxEdJ5CBmE+N5RiBYJqvYA=", "dev": true}, "node_modules/detect-node": {"version": "2.0.4", "resolved": "http://registry.npm.taobao.org/detect-node/download/detect-node-2.0.4.tgz", "integrity": "sha1-AU7o+PZpxcWAI9pkuBecCDooxGw=", "dev": true}, "node_modules/diffie-hellman": {"version": "5.0.3", "resolved": "http://registry.npm.taobao.org/diffie-hellman/download/diffie-hellman-5.0.3.tgz", "integrity": "sha1-QOjumPVaIUlgcUaSHGPhrl89KHU=", "dev": true, "dependencies": {"bn.js": "^4.1.0", "miller-rabin": "^4.0.0", "randombytes": "^2.0.0"}}, "node_modules/dir-glob": {"version": "2.2.2", "resolved": "http://registry.npm.taobao.org/dir-glob/download/dir-glob-2.2.2.tgz", "integrity": "sha1-+gnwaUFTyJGLGLoN6vrpR2n8UMQ=", "dev": true, "dependencies": {"path-type": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/dns-equal": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/dns-equal/download/dns-equal-1.0.0.tgz", "integrity": "sha1-s55/HabrCnW6nBcySzR1PEfgZU0=", "dev": true}, "node_modules/dns-packet": {"version": "1.3.1", "resolved": "http://registry.npm.taobao.org/dns-packet/download/dns-packet-1.3.1.tgz", "integrity": "sha1-EqpCaYEHW+UAuRDu3NC0fdfe2lo=", "dev": true, "dependencies": {"ip": "^1.1.0", "safe-buffer": "^5.0.1"}}, "node_modules/dns-txt": {"version": "2.0.2", "resolved": "http://registry.npm.taobao.org/dns-txt/download/dns-txt-2.0.2.tgz", "integrity": "sha1-uR2Ab10nGI5Ks+fRB9iBocxGQrY=", "dev": true, "dependencies": {"buffer-indexof": "^1.0.0"}}, "node_modules/dom-converter": {"version": "0.2.0", "resolved": "http://registry.npm.taobao.org/dom-converter/download/dom-converter-0.2.0.tgz", "integrity": "sha1-ZyGp2u4uKTaClVtq/kFncWJ7t2g=", "dev": true, "dependencies": {"utila": "~0.4"}}, "node_modules/dom-serializer": {"version": "0.1.1", "resolved": "http://registry.npm.taobao.org/dom-serializer/download/dom-serializer-0.1.1.tgz", "integrity": "sha1-HsQFnihLq+027sKUHUqXChic58A=", "dev": true, "dependencies": {"domelementtype": "^1.3.0", "entities": "^1.1.1"}}, "node_modules/domain-browser": {"version": "1.2.0", "resolved": "http://registry.npm.taobao.org/domain-browser/download/domain-browser-1.2.0.tgz", "integrity": "sha1-PTH1AZGmdJ3RN1p/Ui6CPULlTto=", "dev": true, "engines": {"node": ">=0.4", "npm": ">=1.2"}}, "node_modules/domelementtype": {"version": "1.3.1", "resolved": "http://registry.npm.taobao.org/domelementtype/download/domelementtype-1.3.1.tgz", "integrity": "sha1-0EjESzew0Qp/Kj1f7j9DM9eQSB8=", "dev": true}, "node_modules/domhandler": {"version": "2.4.2", "resolved": "http://registry.npm.taobao.org/domhandler/download/domhandler-2.4.2.tgz", "integrity": "sha1-iAUJfpM9ZehVRvcm1g9euItE+AM=", "dev": true, "dependencies": {"domelementtype": "1"}}, "node_modules/dompurify": {"version": "2.5.8", "resolved": "https://registry.npmjs.org/dompurify/-/dompurify-2.5.8.tgz", "integrity": "sha512-o1vSNgrmYMQObbSSvF/1brBYEQPHhV1+gsmrusO7/GXtp1T9rCS8cXFqVxK/9crT1jA6Ccv+5MTSjBNqr7Sovw==", "optional": true}, "node_modules/domutils": {"version": "1.7.0", "resolved": "http://registry.npm.taobao.org/domutils/download/domutils-1.7.0.tgz", "integrity": "sha1-Vuo0HoNOBuZ0ivehyyXaZ+qfjCo=", "dev": true, "dependencies": {"dom-serializer": "0", "domelementtype": "1"}}, "node_modules/dot-prop": {"version": "4.2.0", "resolved": "http://registry.npm.taobao.org/dot-prop/download/dot-prop-4.2.0.tgz", "integrity": "sha1-HxngwuGqDjJ5fEl5nyg3rGr2nFc=", "dev": true, "dependencies": {"is-obj": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/dotenv": {"version": "6.2.0", "resolved": "http://registry.npm.taobao.org/dotenv/download/dotenv-6.2.0.tgz", "integrity": "sha1-lBwEEFNdlCyL7PKNPzV9vZ1HYGQ=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/dotenv-expand": {"version": "4.2.0", "resolved": "http://registry.npm.taobao.org/dotenv-expand/download/dotenv-expand-4.2.0.tgz", "integrity": "sha1-3vHxyl1gWdJKdm5YeULCEQbOEnU=", "dev": true}, "node_modules/duplexer": {"version": "0.1.1", "resolved": "http://registry.npm.taobao.org/duplexer/download/duplexer-0.1.1.tgz", "integrity": "sha1-rOb/gIwc5mtX0ev5eXessCM0z8E=", "dev": true}, "node_modules/duplexify": {"version": "3.7.1", "resolved": "http://registry.npm.taobao.org/duplexify/download/duplexify-3.7.1.tgz", "integrity": "sha1-Kk31MX9sz9kfhtb9JdjYoQO4gwk=", "dev": true, "dependencies": {"end-of-stream": "^1.0.0", "inherits": "^2.0.1", "readable-stream": "^2.0.0", "stream-shift": "^1.0.0"}}, "node_modules/easy-stack": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/easy-stack/download/easy-stack-1.0.0.tgz", "integrity": "sha1-EskbMIWjfwuqM26UhurEv5Tj54g=", "dev": true, "engines": {"node": ">=6.0.0"}}, "node_modules/ecc-jsbn": {"version": "0.1.2", "resolved": "http://registry.npm.taobao.org/ecc-jsbn/download/ecc-jsbn-0.1.2.tgz", "integrity": "sha1-OoOpBOVDUyh4dMVkt1SThoSamMk=", "dev": true, "dependencies": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}}, "node_modules/echarts": {"version": "5.6.0", "resolved": "https://registry.npmjs.org/echarts/-/echarts-5.6.0.tgz", "integrity": "sha512-oTbVTsXfKuEhxftHqL5xprgLoc0k7uScAwtryCgWF6hPYFLRwOUHiFmHGCBKP5NPFNkDVopOieyUqYGH8Fa3kA==", "dependencies": {"tslib": "2.3.0", "zrender": "5.6.1"}}, "node_modules/echarts/node_modules/tslib": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.3.0.tgz", "integrity": "sha512-N82ooyxVNm6h1riLCoyS9e3fuJ3AMG2zIZs2Gd1ATcSFjSA23Q0fzjjZeh0jbJvWVDZ0cJT8yaNNaaXHzueNjg=="}, "node_modules/ee-first": {"version": "1.1.1", "resolved": "http://registry.npm.taobao.org/ee-first/download/ee-first-1.1.1.tgz", "integrity": "sha1-WQxhFWsK4vTwJVcyoViyZrxWsh0=", "dev": true}, "node_modules/ejs": {"version": "2.6.1", "resolved": "http://registry.npm.taobao.org/ejs/download/ejs-2.6.1.tgz", "integrity": "sha1-SY7A1JVlWrxvI81hho2SZGQHGqA=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/electron-to-chromium": {"version": "1.3.119", "resolved": "http://registry.npm.taobao.org/electron-to-chromium/download/electron-to-chromium-1.3.119.tgz", "integrity": "sha1-mndw2mZyUq64H2Z4U/Z8KybgAZc=", "dev": true}, "node_modules/element-theme-chalk": {"version": "2.6.3", "resolved": "https://registry.npmjs.org/element-theme-chalk/-/element-theme-chalk-2.6.3.tgz", "integrity": "sha512-+OxV3BIk6fYSRuGhP48JWcCdLn58L8cjxesMSzYkW7IsPq2o9rdv19PiRSv1zLW4wgb1Gj/WUz8SHAGNWJicgQ=="}, "node_modules/element-ui": {"version": "2.6.3", "resolved": "https://registry.npmjs.org/element-ui/-/element-ui-2.6.3.tgz", "integrity": "sha512-vV2HfFkCH8KhxunSBFLnA+/jJY3UxMMbhBjiHoO3pZOX/8OQ8QbeatAnjBFDu6dRRGb0DGERUtY2WBeVYegs/A==", "dependencies": {"async-validator": "~1.8.1", "babel-helper-vue-jsx-merge-props": "^2.0.0", "deepmerge": "^1.2.0", "normalize-wheel": "^1.0.1", "resize-observer-polyfill": "^1.5.0", "throttle-debounce": "^1.0.1"}, "peerDependencies": {"vue": "^2.5.16"}}, "node_modules/elliptic": {"version": "6.4.1", "resolved": "http://registry.npm.taobao.org/elliptic/download/elliptic-6.4.1.tgz", "integrity": "sha1-wtC3d2kRuGcixjLDwGxg8vgZk5o=", "dev": true, "dependencies": {"bn.js": "^4.4.0", "brorand": "^1.0.1", "hash.js": "^1.0.0", "hmac-drbg": "^1.0.0", "inherits": "^2.0.1", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.0"}}, "node_modules/emojis-list": {"version": "2.1.0", "resolved": "http://registry.npm.taobao.org/emojis-list/download/emojis-list-2.1.0.tgz", "integrity": "sha1-TapNnbAPmBmIDHn6RXrlsJof04k=", "dev": true, "engines": {"node": ">= 0.10"}}, "node_modules/encodeurl": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/encodeurl/download/encodeurl-1.0.2.tgz", "integrity": "sha1-rT/0yG7C0CkyL1oCw6mmBslbP1k=", "dev": true, "engines": {"node": ">= 0.8"}}, "node_modules/end-of-stream": {"version": "1.4.1", "resolved": "http://registry.npm.taobao.org/end-of-stream/download/end-of-stream-1.4.1.tgz", "integrity": "sha1-7SljTRm6ukY7bOa4CjchPqtx7EM=", "dev": true, "dependencies": {"once": "^1.4.0"}}, "node_modules/enhanced-resolve": {"version": "4.1.0", "resolved": "http://registry.npm.taobao.org/enhanced-resolve/download/enhanced-resolve-4.1.0.tgz", "integrity": "sha1-Qcfgv9/nSsH/4eV61qXGyfN0Kn8=", "dev": true, "dependencies": {"graceful-fs": "^4.1.2", "memory-fs": "^0.4.0", "tapable": "^1.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/entities": {"version": "1.1.2", "resolved": "http://registry.npm.taobao.org/entities/download/entities-1.1.2.tgz", "integrity": "sha1-vfpzUplmTfr9NFKe1PhSKidf6lY=", "dev": true}, "node_modules/errno": {"version": "0.1.7", "resolved": "http://registry.npm.taobao.org/errno/download/errno-0.1.7.tgz", "integrity": "sha1-RoTXF3mtOa8Xfj8AeZb3xnyFJhg=", "dev": true, "dependencies": {"prr": "~1.0.1"}, "bin": {"errno": "cli.js"}}, "node_modules/error-ex": {"version": "1.3.2", "resolved": "http://registry.npm.taobao.org/error-ex/download/error-ex-1.3.2.tgz", "integrity": "sha1-tKxAZIEH/c3PriQvQovqihTU8b8=", "dev": true, "dependencies": {"is-arrayish": "^0.2.1"}}, "node_modules/error-stack-parser": {"version": "2.0.2", "resolved": "http://registry.npm.taobao.org/error-stack-parser/download/error-stack-parser-2.0.2.tgz", "integrity": "sha1-Sujbqiv5CotFBwe5FJ3KvKE1Ug0=", "dev": true, "dependencies": {"stackframe": "^1.0.4"}}, "node_modules/es-abstract": {"version": "1.13.0", "resolved": "http://registry.npm.taobao.org/es-abstract/download/es-abstract-1.13.0.tgz", "integrity": "sha1-rIYUX91QmdjdSVWMy6Lq+biOJOk=", "dev": true, "dependencies": {"es-to-primitive": "^1.2.0", "function-bind": "^1.1.1", "has": "^1.0.3", "is-callable": "^1.1.4", "is-regex": "^1.0.4", "object-keys": "^1.0.12"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-to-primitive": {"version": "1.2.0", "resolved": "http://registry.npm.taobao.org/es-to-primitive/download/es-to-primitive-1.2.0.tgz", "integrity": "sha1-7fckeAM0VujdqO8J4ArZZQcH83c=", "dev": true, "dependencies": {"is-callable": "^1.1.4", "is-date-object": "^1.0.1", "is-symbol": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/escape-html": {"version": "1.0.3", "resolved": "http://registry.npm.taobao.org/escape-html/download/escape-html-1.0.3.tgz", "integrity": "sha1-Aljq5NPQwJdN4cFpGI7wBR0dGYg=", "dev": true}, "node_modules/escape-string-regexp": {"version": "1.0.5", "resolved": "http://registry.npm.taobao.org/escape-string-regexp/download/escape-string-regexp-1.0.5.tgz", "integrity": "sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=", "dev": true, "engines": {"node": ">=0.8.0"}}, "node_modules/eslint-scope": {"version": "4.0.3", "resolved": "http://registry.npm.taobao.org/eslint-scope/download/eslint-scope-4.0.3.tgz", "integrity": "sha1-ygODMxD2iJoyZHgaqC5j65z+eEg=", "dev": true, "dependencies": {"esrecurse": "^4.1.0", "estraverse": "^4.1.1"}, "engines": {"node": ">=4.0.0"}}, "node_modules/esprima": {"version": "4.0.1", "resolved": "http://registry.npm.taobao.org/esprima/download/esprima-4.0.1.tgz", "integrity": "sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=", "dev": true, "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/esrecurse": {"version": "4.2.1", "resolved": "http://registry.npm.taobao.org/esrecurse/download/esrecurse-4.2.1.tgz", "integrity": "sha1-AHo7n9vCs7uH5IeeoZyS/b05Qs8=", "dev": true, "dependencies": {"estraverse": "^4.1.0"}, "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "4.2.0", "resolved": "http://registry.npm.taobao.org/estraverse/download/estraverse-4.2.0.tgz", "integrity": "sha1-De4/7TH81GlhjOc0IJn8GvoL2xM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/esutils": {"version": "2.0.2", "resolved": "http://registry.npm.taobao.org/esutils/download/esutils-2.0.2.tgz", "integrity": "sha1-Cr9PHKpbyx96nYrMbepPqqBLrJs=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/etag": {"version": "1.8.1", "resolved": "http://registry.npm.taobao.org/etag/download/etag-1.8.1.tgz", "integrity": "sha1-Qa4u62XvpiJorr/qg6x9eSmbCIc=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/event-pubsub": {"version": "4.3.0", "resolved": "http://registry.npm.taobao.org/event-pubsub/download/event-pubsub-4.3.0.tgz", "integrity": "sha1-9o2Ba8KfHsAsU53FjI3UDOcss24=", "dev": true, "engines": {"node": ">=4.0.0"}}, "node_modules/eventemitter3": {"version": "3.1.0", "resolved": "http://registry.npm.taobao.org/eventemitter3/download/eventemitter3-3.1.0.tgz", "integrity": "sha1-CQtNbNvWRe0Qv3UNS1QHlC17oWM=", "dev": true}, "node_modules/events": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/events/download/events-3.0.0.tgz?cache=0&other_urls=http%3A%2F%2Fregistry.npm.taobao.org%2Fevents%2Fdownload%2Fevents-3.0.0.tgz", "integrity": "sha1-mgoN+vYok9krh1uPJpjKQRSXPog=", "dev": true, "engines": {"node": ">=0.8.x"}}, "node_modules/eventsource": {"version": "1.0.7", "resolved": "http://registry.npm.taobao.org/eventsource/download/eventsource-1.0.7.tgz", "integrity": "sha1-j7xyyT/NNAiAkLwKTmT0tc7m2NA=", "dev": true, "dependencies": {"original": "^1.0.0"}, "engines": {"node": ">=0.12.0"}}, "node_modules/evp_bytestokey": {"version": "1.0.3", "resolved": "http://registry.npm.taobao.org/evp_bytestokey/download/evp_bytestokey-1.0.3.tgz", "integrity": "sha1-f8vbGY3HGVlDLv4ThCaE4FJaywI=", "dev": true, "dependencies": {"md5.js": "^1.3.4", "safe-buffer": "^5.1.1"}}, "node_modules/execa": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/execa/download/execa-1.0.0.tgz", "integrity": "sha1-xiNqW7TfbW8V6I5/AXeYIWdJ3dg=", "dev": true, "dependencies": {"cross-spawn": "^6.0.0", "get-stream": "^4.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "engines": {"node": ">=6"}}, "node_modules/expand-brackets": {"version": "2.1.4", "resolved": "http://registry.npm.taobao.org/expand-brackets/download/expand-brackets-2.1.4.tgz", "integrity": "sha1-t3c14xXOMPa27/D4OwQVGiJEliI=", "dev": true, "dependencies": {"debug": "^2.3.3", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "posix-character-classes": "^0.1.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/debug": {"version": "2.6.9", "resolved": "http://registry.npm.taobao.org/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "dependencies": {"ms": "2.0.0"}}, "node_modules/expand-brackets/node_modules/define-property": {"version": "0.2.5", "resolved": "http://registry.npm.taobao.org/define-property/download/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/extend-shallow": {"version": "2.0.1", "resolved": "http://registry.npm.taobao.org/extend-shallow/download/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/expand-brackets/node_modules/ms": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}, "node_modules/express": {"version": "4.16.4", "resolved": "http://registry.npm.taobao.org/express/download/express-4.16.4.tgz", "integrity": "sha1-/d72GSYQniTFFeqX/S8b2/Yt8S4=", "dev": true, "dependencies": {"accepts": "~1.3.5", "array-flatten": "1.1.1", "body-parser": "1.18.3", "content-disposition": "0.5.2", "content-type": "~1.0.4", "cookie": "0.3.1", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "~1.1.2", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "1.1.1", "fresh": "0.5.2", "merge-descriptors": "1.0.1", "methods": "~1.1.2", "on-finished": "~2.3.0", "parseurl": "~1.3.2", "path-to-regexp": "0.1.7", "proxy-addr": "~2.0.4", "qs": "6.5.2", "range-parser": "~1.2.0", "safe-buffer": "5.1.2", "send": "0.16.2", "serve-static": "1.13.2", "setprototypeof": "1.1.0", "statuses": "~1.4.0", "type-is": "~1.6.16", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/express/node_modules/debug": {"version": "2.6.9", "resolved": "http://registry.npm.taobao.org/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "dependencies": {"ms": "2.0.0"}}, "node_modules/express/node_modules/ms": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}, "node_modules/extend": {"version": "3.0.2", "resolved": "http://registry.npm.taobao.org/extend/download/extend-3.0.2.tgz", "integrity": "sha1-+LETa0Bx+9jrFAr/hYsQGewpFfo=", "dev": true}, "node_modules/extend-shallow": {"version": "3.0.2", "resolved": "http://registry.npm.taobao.org/extend-shallow/download/extend-shallow-3.0.2.tgz", "integrity": "sha1-Jqcarwc7OfshJxcnRhMcJwQCjbg=", "dev": true, "dependencies": {"assign-symbols": "^1.0.0", "is-extendable": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extend-shallow/node_modules/is-extendable": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/is-extendable/download/is-extendable-1.0.1.tgz", "integrity": "sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=", "dev": true, "dependencies": {"is-plain-object": "^2.0.4"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extglob": {"version": "2.0.4", "resolved": "http://registry.npm.taobao.org/extglob/download/extglob-2.0.4.tgz", "integrity": "sha1-rQD+TcYSqSMuhxhxHcXLWrAoVUM=", "dev": true, "dependencies": {"array-unique": "^0.3.2", "define-property": "^1.0.0", "expand-brackets": "^2.1.4", "extend-shallow": "^2.0.1", "fragment-cache": "^0.2.1", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extglob/node_modules/define-property": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/define-property/download/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "dev": true, "dependencies": {"is-descriptor": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extglob/node_modules/extend-shallow": {"version": "2.0.1", "resolved": "http://registry.npm.taobao.org/extend-shallow/download/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extglob/node_modules/is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "deprecated": "Please upgrade to v1.0.1", "dev": true, "dependencies": {"kind-of": "^6.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extglob/node_modules/is-data-descriptor": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "deprecated": "Please upgrade to v1.0.1", "dev": true, "dependencies": {"kind-of": "^6.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extglob/node_modules/is-descriptor": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/is-descriptor/download/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "dev": true, "dependencies": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/extsprintf": {"version": "1.3.0", "resolved": "http://registry.npm.taobao.org/extsprintf/download/extsprintf-1.3.0.tgz", "integrity": "sha1-lpGEQOMEGnpBT4xS48V06zw+HgU=", "dev": true, "engines": ["node >=0.6.0"]}, "node_modules/fast-deep-equal": {"version": "2.0.1", "resolved": "http://registry.npm.taobao.org/fast-deep-equal/download/fast-deep-equal-2.0.1.tgz?cache=0&other_urls=http%3A%2F%2Fregistry.npm.taobao.org%2Ffast-deep-equal%2Fdownload%2Ffast-deep-equal-2.0.1.tgz", "integrity": "sha1-ewUhjd+WZ79/Nwv3/bLLFf3Qqkk=", "dev": true}, "node_modules/fast-glob": {"version": "2.2.6", "resolved": "http://registry.npm.taobao.org/fast-glob/download/fast-glob-2.2.6.tgz", "integrity": "sha1-pdW2l+yN7aRo2Fp0A1KQoCWpUpU=", "dev": true, "dependencies": {"@mrmlnc/readdir-enhanced": "^2.2.1", "@nodelib/fs.stat": "^1.1.2", "glob-parent": "^3.1.0", "is-glob": "^4.0.0", "merge2": "^1.2.3", "micromatch": "^3.1.10"}, "engines": {"node": ">=4.0.0"}}, "node_modules/fast-json-stable-stringify": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/fast-json-stable-stringify/download/fast-json-stable-stringify-2.0.0.tgz", "integrity": "sha1-1RQsDK7msRifh9OnYREGT4bIu/I=", "dev": true}, "node_modules/fastparse": {"version": "1.1.2", "resolved": "http://registry.npm.taobao.org/fastparse/download/fastparse-1.1.2.tgz", "integrity": "sha1-kXKMWllC7O2FMSg8eUQe5BIsNak=", "dev": true}, "node_modules/faye-websocket": {"version": "0.10.0", "resolved": "http://registry.npm.taobao.org/faye-websocket/download/faye-websocket-0.10.0.tgz", "integrity": "sha1-TkkvjQTftviQA1B/btvy1QHnxvQ=", "dev": true, "dependencies": {"websocket-driver": ">=0.5.1"}, "engines": {"node": ">=0.4.0"}}, "node_modules/fflate": {"version": "0.8.2", "resolved": "https://registry.npmjs.org/fflate/-/fflate-0.8.2.tgz", "integrity": "sha512-cPJU47OaAoCbg0pBvzsgpTPhmhqI5eJjh/JIu8tPj5q+T7iLvW/JAYUqmE7KOB4R1ZyEhzBaIQpQpardBF5z8A=="}, "node_modules/figgy-pudding": {"version": "3.5.1", "resolved": "http://registry.npm.taobao.org/figgy-pudding/download/figgy-pudding-3.5.1.tgz", "integrity": "sha1-hiRwESkBxyeg5JWoB0S9W6odZ5A=", "deprecated": "This module is no longer supported.", "dev": true}, "node_modules/file-loader": {"version": "3.0.1", "resolved": "http://registry.npm.taobao.org/file-loader/download/file-loader-3.0.1.tgz", "integrity": "sha1-+OC6C1mZGLUa3+RdZtHnca1WD6o=", "dev": true, "dependencies": {"loader-utils": "^1.0.2", "schema-utils": "^1.0.0"}, "engines": {"node": ">= 6.9.0"}, "peerDependencies": {"webpack": "^4.0.0"}}, "node_modules/file-loader/node_modules/schema-utils": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/schema-utils/download/schema-utils-1.0.0.tgz", "integrity": "sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A=", "dev": true, "dependencies": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}, "engines": {"node": ">= 4"}}, "node_modules/filesize": {"version": "3.6.1", "resolved": "http://registry.npm.taobao.org/filesize/download/filesize-3.6.1.tgz", "integrity": "sha1-CQuz7gG2+AGoqL6Z0xcQs0Irsxc=", "dev": true, "engines": {"node": ">= 0.4.0"}}, "node_modules/fill-range": {"version": "4.0.0", "resolved": "http://registry.npm.taobao.org/fill-range/download/fill-range-4.0.0.tgz", "integrity": "sha1-1USBHUKPmOsGpj3EAtJAPDKMOPc=", "dev": true, "dependencies": {"extend-shallow": "^2.0.1", "is-number": "^3.0.0", "repeat-string": "^1.6.1", "to-regex-range": "^2.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/fill-range/node_modules/extend-shallow": {"version": "2.0.1", "resolved": "http://registry.npm.taobao.org/extend-shallow/download/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/finalhandler": {"version": "1.1.1", "resolved": "http://registry.npm.taobao.org/finalhandler/download/finalhandler-1.1.1.tgz", "integrity": "sha1-7r9O2EAHnIP0JJA4ydcDAIMBsQU=", "dev": true, "dependencies": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "parseurl": "~1.3.2", "statuses": "~1.4.0", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/finalhandler/node_modules/debug": {"version": "2.6.9", "resolved": "http://registry.npm.taobao.org/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "dependencies": {"ms": "2.0.0"}}, "node_modules/finalhandler/node_modules/ms": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}, "node_modules/find-cache-dir": {"version": "2.1.0", "resolved": "http://registry.npm.taobao.org/find-cache-dir/download/find-cache-dir-2.1.0.tgz", "integrity": "sha1-jQ+UzRP+Q8bHwmGg2GEVypGMBfc=", "dev": true, "dependencies": {"commondir": "^1.0.1", "make-dir": "^2.0.0", "pkg-dir": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/find-up": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/find-up/download/find-up-3.0.0.tgz", "integrity": "sha1-SRafHXmTQwZG2mHsxa41XCHJe3M=", "dev": true, "dependencies": {"locate-path": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/flush-write-stream": {"version": "1.1.1", "resolved": "http://registry.npm.taobao.org/flush-write-stream/download/flush-write-stream-1.1.1.tgz", "integrity": "sha1-jdfYc6G6vCB9lOrQwuDkQnbr8ug=", "dev": true, "dependencies": {"inherits": "^2.0.3", "readable-stream": "^2.3.6"}}, "node_modules/follow-redirects": {"version": "1.7.0", "resolved": "http://registry.npm.taobao.org/follow-redirects/download/follow-redirects-1.7.0.tgz", "integrity": "sha1-SJ68GY3A5/ZBZ70jsDxMGbV4THY=", "dependencies": {"debug": "^3.2.6"}, "engines": {"node": ">=4.0"}}, "node_modules/follow-redirects/node_modules/debug": {"version": "3.2.6", "resolved": "http://registry.npm.taobao.org/debug/download/debug-3.2.6.tgz", "integrity": "sha1-6D0X3hbYp++3cX7b5fsQE17uYps=", "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "dependencies": {"ms": "^2.1.1"}}, "node_modules/for-in": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/for-in/download/for-in-1.0.2.tgz", "integrity": "sha1-gQaNKVqBQuwKxybG4iAMMPttXoA=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/forever-agent": {"version": "0.6.1", "resolved": "http://registry.npm.taobao.org/forever-agent/download/forever-agent-0.6.1.tgz", "integrity": "sha1-+8cfDEGt6zf5bFd60e1C2P2sypE=", "dev": true, "engines": {"node": "*"}}, "node_modules/form-data": {"version": "2.3.3", "resolved": "http://registry.npm.taobao.org/form-data/download/form-data-2.3.3.tgz", "integrity": "sha1-3M5SwF9kTymManq5Nr1yTO/786Y=", "dev": true, "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}, "engines": {"node": ">= 0.12"}}, "node_modules/forwarded": {"version": "0.1.2", "resolved": "http://registry.npm.taobao.org/forwarded/download/forwarded-0.1.2.tgz", "integrity": "sha1-mMI9qxF1ZXuMBXPozszZGw/xjIQ=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/fragment-cache": {"version": "0.2.1", "resolved": "http://registry.npm.taobao.org/fragment-cache/download/fragment-cache-0.2.1.tgz", "integrity": "sha1-QpD60n8T6Jvn8zeZxrxaCr//DRk=", "dev": true, "dependencies": {"map-cache": "^0.2.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/fresh": {"version": "0.5.2", "resolved": "http://registry.npm.taobao.org/fresh/download/fresh-0.5.2.tgz", "integrity": "sha1-PYyt2Q2XZWn6g1qx+OSyOhBWBac=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/from2": {"version": "2.3.0", "resolved": "http://registry.npm.taobao.org/from2/download/from2-2.3.0.tgz", "integrity": "sha1-i/tVAr3kpNNs/e6gB/zKIdfjgq8=", "dev": true, "dependencies": {"inherits": "^2.0.1", "readable-stream": "^2.0.0"}}, "node_modules/fs-extra": {"version": "7.0.1", "resolved": "http://registry.npm.taobao.org/fs-extra/download/fs-extra-7.0.1.tgz?cache=0&other_urls=http%3A%2F%2Fregistry.npm.taobao.org%2Ffs-extra%2Fdownload%2Ffs-extra-7.0.1.tgz", "integrity": "sha1-TxicRKoSO4lfcigE9V6iPq3DSOk=", "dev": true, "dependencies": {"graceful-fs": "^4.1.2", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}, "engines": {"node": ">=6 <7 || >=8"}}, "node_modules/fs-write-stream-atomic": {"version": "1.0.10", "resolved": "http://registry.npm.taobao.org/fs-write-stream-atomic/download/fs-write-stream-atomic-1.0.10.tgz", "integrity": "sha1-tH31NJPvkR33VzHnCp3tAYnbQMk=", "deprecated": "This package is no longer supported.", "dev": true, "dependencies": {"graceful-fs": "^4.1.2", "iferr": "^0.1.5", "imurmurhash": "^0.1.4", "readable-stream": "1 || 2"}}, "node_modules/fs.realpath": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/fs.realpath/download/fs.realpath-1.0.0.tgz", "integrity": "sha1-FQStJSMVjKpA20onh8sBQRmU6k8=", "dev": true}, "node_modules/fsevents": {"version": "1.2.7", "resolved": "http://registry.npm.taobao.org/fsevents/download/fsevents-1.2.7.tgz", "integrity": "sha1-SFG2ZKN4PlIAOzxm6w7uEHSTOqQ=", "bundleDependencies": ["node-pre-gyp"], "deprecated": "Upgrade to fsevents v2 to mitigate potential security issues", "dev": true, "hasInstallScript": true, "optional": true, "os": ["darwin"], "dependencies": {"nan": "^2.9.2", "node-pre-gyp": "^0.10.0"}, "engines": {"node": ">=4.0"}}, "node_modules/fsevents/node_modules/abbrev": {"version": "1.1.1", "integrity": "sha512-nne9/IiQ/hzIhY6pdDnbBtz7DjPTKrY00P/zvPSm5pOFkl6xuGrGnXn/VtTNNfNtAfZ9/1RtehkszU9qcTii0Q==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/ansi-regex": {"version": "2.1.1", "integrity": "sha512-TIGnTpdo+E3+pCyAluZvtED5p5wCqLdezCyhPZzKPcxvFplEt4i+W7OONCKgeZFT3+y5NZZfOOS/Bdcanm1MYA==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/fsevents/node_modules/aproba": {"version": "1.2.0", "integrity": "sha512-Y9J6ZjXtoYh8RnXVCMOU/ttDmk1aBjunq9vO0ta5x85WDQiQfUF9sIPBITdbiiIVcBo03Hi3jMxigBtsddlXRw==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/are-we-there-yet": {"version": "1.1.5", "integrity": "sha512-5hYdAkZlcG8tOLujVDTgCT+uPX0VnpAH28gWsLfzpXYm7wP6mp5Q/gYyR7YQ0cKVJcXJnl3j2kpBan13PtQf6w==", "deprecated": "This package is no longer supported.", "dev": true, "inBundle": true, "optional": true, "dependencies": {"delegates": "^1.0.0", "readable-stream": "^2.0.6"}}, "node_modules/fsevents/node_modules/balanced-match": {"version": "1.0.0", "integrity": "sha512-9Y0g0Q8rmSt+H33DfKv7FOc3v+iRI+o1lbzt8jGcIosYW37IIW/2XVYq5NPdmaD5NQ59Nk26Kl/vZbwW9Fr8vg==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/brace-expansion": {"version": "1.1.11", "integrity": "sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/fsevents/node_modules/chownr": {"version": "1.1.1", "integrity": "sha512-j38EvO5+LHX84jlo6h4UzmOwi0UgW61WRyPtJz4qaadK5eY3BTS5TY/S1Stc3Uk2lIM6TPevAlULiEJwie860g==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/code-point-at": {"version": "1.1.0", "integrity": "sha512-RpAVKQA5T63xEj6/giIbUEtZwJ4UFIc3ZtvEkiaUERylqe8xb5IvqcgOurZLahv93CLKfxcw5YI+DZcUBRyLXA==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/fsevents/node_modules/concat-map": {"version": "0.0.1", "integrity": "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/console-control-strings": {"version": "1.1.0", "integrity": "sha512-ty/fTekppD2fIwRvnZAVdeOiGd1c7YXEixbgJTNzqcxJWKQnjJ/V1bNEEE6hygpM3WjwHFUVK6HTjWSzV4a8sQ==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/core-util-is": {"version": "1.0.2", "integrity": "sha512-3lqz5YjWTYnW6dlDa5TLaTCcShfar1e40rmcJVwCBJC6mWlFuj0eCHIElmG1g5kyuJ/GD+8Wn4FFCcz4gJPfaQ==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/debug": {"version": "2.6.9", "integrity": "sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"ms": "2.0.0"}}, "node_modules/fsevents/node_modules/deep-extend": {"version": "0.6.0", "integrity": "sha512-LOHxIOaPYdHlJRtCQfDIVZtfw/ufM8+rVj649RIHzcm/vGwQRXFt6OPqIFWsm2XEMrNIEtWR64sY1LEKD2vAOA==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=4.0.0"}}, "node_modules/fsevents/node_modules/delegates": {"version": "1.0.0", "integrity": "sha512-bd2L678uiWATM6m5Z1VzNCErI3jiGzt6HGY8OVICs40JQq/HALfbyNJmp0UDakEY4pMMaN0Ly5om/B1VI/+xfQ==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/detect-libc": {"version": "1.0.3", "integrity": "sha512-pGjwhsmsp4kL2RTz08wcOlGN83otlqHeD/Z5T8GXZB+/YcpQ/dgo+lbU8ZsGxV0HIvqqxo9l7mqYwyYMD9bKDg==", "dev": true, "inBundle": true, "optional": true, "bin": {"detect-libc": "bin/detect-libc.js"}, "engines": {"node": ">=0.10"}}, "node_modules/fsevents/node_modules/fs-minipass": {"version": "1.2.5", "integrity": "sha512-JhBl0skXjUPCFH7x6x61gQxrKyXsxB5gcgePLZCwfyCGGsTISMoIeObbrvVeP6Xmyaudw4TT43qV2Gz+iyd2oQ==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"minipass": "^2.2.1"}}, "node_modules/fsevents/node_modules/fs.realpath": {"version": "1.0.0", "integrity": "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/gauge": {"version": "2.7.4", "integrity": "sha512-14x4kjc6lkD3ltw589k0NrPD6cCNTD6CWoVUNpB85+DrtONoZn+Rug6xZU5RvSC4+TZPxA5AnBibQYAvZn41Hg==", "deprecated": "This package is no longer supported.", "dev": true, "inBundle": true, "optional": true, "dependencies": {"aproba": "^1.0.3", "console-control-strings": "^1.0.0", "has-unicode": "^2.0.0", "object-assign": "^4.1.0", "signal-exit": "^3.0.0", "string-width": "^1.0.1", "strip-ansi": "^3.0.1", "wide-align": "^1.1.0"}}, "node_modules/fsevents/node_modules/glob": {"version": "7.1.3", "integrity": "sha512-vcfuiIxogLV4DlGBHIUOwI0IbrJ8HWPc4MU7HzviGeNho/UJDfi6B5p3sHeWIQ0KGIU0Jpxi5ZHxemQfLkkAwQ==", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "inBundle": true, "optional": true, "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/fsevents/node_modules/has-unicode": {"version": "2.0.1", "integrity": "sha512-8Rf9Y83NBReMnx0gFzA8JImQACstCYWUplepDa9xprwwtmgEZUF0h/i5xSA625zB/I37EtrswSST6OXxwaaIJQ==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/iconv-lite": {"version": "0.4.24", "integrity": "sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/fsevents/node_modules/ignore-walk": {"version": "3.0.1", "integrity": "sha512-DTVlMx3IYPe0/JJcYP7Gxg7ttZZu3IInhuEhbchuqneY9wWe5Ojy2mXLBaQFUQmo0AW2r3qG7m1mg86js+gnlQ==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"minimatch": "^3.0.4"}}, "node_modules/fsevents/node_modules/inflight": {"version": "1.0.6", "integrity": "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==", "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.", "dev": true, "inBundle": true, "optional": true, "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/fsevents/node_modules/inherits": {"version": "2.0.3", "integrity": "sha512-x00IRNXNy63jwGkJmzPigoySHbaqpNuzKbBOmzK+g2OdZpQ9w+sxCN+VSB3ja7IAge2OP2qpfxTjeNcyjmW1uw==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/ini": {"version": "1.3.5", "integrity": "sha512-RZY5huIKCMRWDUqZlEi72f/lmXKMvuszcMBduliQ3nnWbx9X/ZBQO7DijMEYS9EhHBb2qacRUMtC7svLwe0lcw==", "deprecated": "Please update to ini >=1.3.6 to avoid a prototype pollution issue", "dev": true, "inBundle": true, "optional": true, "engines": {"node": "*"}}, "node_modules/fsevents/node_modules/is-fullwidth-code-point": {"version": "1.0.0", "integrity": "sha512-1pqUqRjkhPJ9miNq9SwMfdvi6lBJcd6eFxvfaivQhaH3SgisfiuudvFntdKOmxuee/77l+FPjKrQjWvmPjWrRw==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"number-is-nan": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/fsevents/node_modules/isarray": {"version": "1.0.0", "integrity": "sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/minimatch": {"version": "3.0.4", "integrity": "sha512-yJHVQEhyqPLUTgt9B83PXu6W3rx4MvvHvSUvToogpwoGDOUQ+yDrR0HRot+yOCdCO7u4hX3pWft6kWBBcqh0UA==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/fsevents/node_modules/minimist": {"version": "0.0.8", "integrity": "sha512-miQKw5Hv4NS1Psg2517mV4e4dYNaO3++hjAvLOAzKqZ61rH8NS1SK+vbfBWZ5PY/Me/bEWhUwqMghEW5Fb9T7Q==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/minipass": {"version": "2.3.5", "integrity": "sha512-Gi1W4k059gyRbyVUZQ4mEqLm0YIUiGYfvxhF6SIlk3ui1WVxMTGfGdQ2SInh3PDrRTVvPKgULkpJtT4RH10+VA==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"safe-buffer": "^5.1.2", "yallist": "^3.0.0"}}, "node_modules/fsevents/node_modules/minizlib": {"version": "1.2.1", "integrity": "sha512-7+4oTUOWKg7AuL3vloEWekXY2/D20cevzsrNT2kGWm+39J9hGTCBv8VI5Pm5lXZ/o3/mdR4f8rflAPhnQb8mPA==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"minipass": "^2.2.1"}}, "node_modules/fsevents/node_modules/mkdirp": {"version": "0.5.1", "integrity": "sha512-SknJC52obPfGQPnjIkXbmA6+5H15E+fR+E4iR2oQ3zzCLbd7/ONua69R/Gw7AgkTLsRG+r5fzksYwWe1AgTyWA==", "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "dev": true, "inBundle": true, "optional": true, "dependencies": {"minimist": "0.0.8"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/fsevents/node_modules/ms": {"version": "2.0.0", "integrity": "sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/needle": {"version": "2.2.4", "integrity": "sha512-HyoqEb4wr/rsoaIDfTH2aVL9nWtQqba2/HvMv+++m8u0dz808MaagKILxtfeSN7QU7nvbQ79zk3vYOJp9zsNEA==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"debug": "^2.1.2", "iconv-lite": "^0.4.4", "sax": "^1.2.4"}, "bin": {"needle": "bin/needle"}, "engines": {"node": ">= 0.10.x"}}, "node_modules/fsevents/node_modules/node-pre-gyp": {"version": "0.10.3", "integrity": "sha512-d1xFs+C/IPS8Id0qPTZ4bUT8wWryfR/OzzAFxweG+uLN85oPzyo2Iw6bVlLQ/JOdgNonXLCoRyqDzDWq4iw72A==", "deprecated": "Please upgrade to @mapbox/node-pre-gyp: the non-scoped node-pre-gyp package is deprecated and only the @mapbox scoped package will recieve updates in the future", "dev": true, "inBundle": true, "optional": true, "dependencies": {"detect-libc": "^1.0.2", "mkdirp": "^0.5.1", "needle": "^2.2.1", "nopt": "^4.0.1", "npm-packlist": "^1.1.6", "npmlog": "^4.0.2", "rc": "^1.2.7", "rimraf": "^2.6.1", "semver": "^5.3.0", "tar": "^4"}, "bin": {"node-pre-gyp": "bin/node-pre-gyp"}}, "node_modules/fsevents/node_modules/nopt": {"version": "4.0.1", "integrity": "sha512-+5XZFpQZEY0cg5JaxLwGxDlKNKYxuXwGt8/Oi3UXm5/4ymrJve9d2CURituxv3rSrVCGZj4m1U1JlHTdcKt2Ng==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"abbrev": "1", "osenv": "^0.1.4"}, "bin": {"nopt": "bin/nopt.js"}}, "node_modules/fsevents/node_modules/npm-bundled": {"version": "1.0.5", "integrity": "sha512-m/e6jgWu8/v5niCUKQi9qQl8QdeEduFA96xHDDzFGqly0OOjI7c+60KM/2sppfnUU9JJagf+zs+yGhqSOFj71g==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/npm-packlist": {"version": "1.2.0", "integrity": "sha512-7Mni4Z8Xkx0/oegoqlcao/JpPCPEMtUvsmB0q7mgvlMinykJLSRTYuFqoQLYgGY8biuxIeiHO+QNJKbCfljewQ==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"ignore-walk": "^3.0.1", "npm-bundled": "^1.0.1"}}, "node_modules/fsevents/node_modules/npmlog": {"version": "4.1.2", "integrity": "sha512-2uUqazuKlTaSI/dC8AzicUck7+IrEaOnN/e0jd3Xtt1KcGpwx30v50mL7oPyr/h9bL3E4aZccVwpwP+5W9Vjkg==", "deprecated": "This package is no longer supported.", "dev": true, "inBundle": true, "optional": true, "dependencies": {"are-we-there-yet": "~1.1.2", "console-control-strings": "~1.1.0", "gauge": "~2.7.3", "set-blocking": "~2.0.0"}}, "node_modules/fsevents/node_modules/number-is-nan": {"version": "1.0.1", "integrity": "sha512-4jbtZXNAsfZbAHiiqjLPBiCl16dES1zI4Hpzzxw61Tk+loF+sBDBKx1ICKKKwIqQ7M0mFn1TmkN7euSncWgHiQ==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/fsevents/node_modules/object-assign": {"version": "4.1.1", "integrity": "sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/fsevents/node_modules/once": {"version": "1.4.0", "integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"wrappy": "1"}}, "node_modules/fsevents/node_modules/os-homedir": {"version": "1.0.2", "integrity": "sha512-B5JU3cabzk8c67mRRd3ECmROafjYMXbuzlwtqdM8IbS8ktlTix8aFGb2bAGKrSRIlnfKwovGUUr72JUPyOb6kQ==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/fsevents/node_modules/os-tmpdir": {"version": "1.0.2", "integrity": "sha512-D2FR03Vir7FIu45XBY20mTb+/ZSWB00sjU9jdQXt83gDrI4Ztz5Fs7/yy74g2N5SVQY4xY1qDr4rNddwYRVX0g==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/fsevents/node_modules/osenv": {"version": "0.1.5", "integrity": "sha512-0CWcCECdMVc2Rw3U5w9ZjqX6ga6ubk1xDVKxtBQPK7wis/0F2r9T6k4ydGYhecl7YUBxBVxhL5oisPsNxAPe2g==", "deprecated": "This package is no longer supported.", "dev": true, "inBundle": true, "optional": true, "dependencies": {"os-homedir": "^1.0.0", "os-tmpdir": "^1.0.0"}}, "node_modules/fsevents/node_modules/path-is-absolute": {"version": "1.0.1", "integrity": "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/fsevents/node_modules/process-nextick-args": {"version": "2.0.0", "integrity": "sha512-MtEC1TqN0EU5nephaJ4rAtThHtC86dNN9qCuEhtshvpVBkAW5ZO7BASN9REnF9eoXGcRub+pFuKEpOHE+HbEMw==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/rc": {"version": "1.2.8", "integrity": "sha512-y3bGgqKj3QBdxLbLkomlohkvsA8gdAiUQlSBJnBhfn+BPxg4bc62d8TcBW15wavDfgexCgccckhcZvywyQYPOw==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "bin": {"rc": "cli.js"}}, "node_modules/fsevents/node_modules/rc/node_modules/minimist": {"version": "1.2.0", "integrity": "sha512-7Wl+Jz+IGWuSdgsQEJ4JunV0si/iMhg42MnQQG6h1R6TNeVenp4U9x5CC5v/gYqz/fENLQITAWXidNtVL0NNbw==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/readable-stream": {"version": "2.3.6", "integrity": "sha512-tQtKA9WIAhBF3+VLAseyMqZeBjW0AHJoxOtYqSUZNJxauErmLbVm2FW1y+J/YA9dUrAC39ITejlZWhVIwawkKw==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/fsevents/node_modules/rimraf": {"version": "2.6.3", "integrity": "sha512-mwqeW5XsA2qAejG46gYdENaxXjx9onRNCfn7L0duuP4hCuTIi/QO7PDK07KJfp1d+izWPrzEJDcSqBa0OZQriA==", "deprecated": "Rimraf versions prior to v4 are no longer supported", "dev": true, "inBundle": true, "optional": true, "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}}, "node_modules/fsevents/node_modules/safe-buffer": {"version": "5.1.2", "integrity": "sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/safer-buffer": {"version": "2.1.2", "integrity": "sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/sax": {"version": "1.2.4", "integrity": "sha512-NqVDv9TpANUjFm0N8uM5GxL36UgKi9/atZw+x7YFnQ8ckwFGKrl4xX4yWtrey3UJm5nP1kUbnYgLopqWNSRhWw==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/semver": {"version": "5.6.0", "integrity": "sha512-RS9R6R35NYgQn++fkDWaOmqGoj4Ek9gGs+DPxNUZKuwE183xjJroKvyo1IzVFeXvUrvmALy6FWD5xrdJT25gMg==", "dev": true, "inBundle": true, "optional": true, "bin": {"semver": "bin/semver"}}, "node_modules/fsevents/node_modules/set-blocking": {"version": "2.0.0", "integrity": "sha512-KiKBS8AnWGEyLzofFfmvKwpdPzqiy16LvQfK3yv/fVH7Bj13/wl3JSR1J+rfgRE9q7xUJK4qvgS8raSOeLUehw==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/signal-exit": {"version": "3.0.2", "integrity": "sha512-meQNNykwecVxdu1RlYMKpQx4+wefIYpmxi6gexo/KAbwquJrBUrBmKYJrE8KFkVQAAVWEnwNdu21PgrD77J3xA==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/string_decoder": {"version": "1.1.1", "integrity": "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/fsevents/node_modules/string-width": {"version": "1.0.2", "integrity": "sha512-0XsVpQLnVCXHJfyEs8tC0zpTVIr5PKKsQtkT29IwupnPTjtPmQ3xT/4yCREF9hYkV/3M3kzcUTSAZT6a6h81tw==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/fsevents/node_modules/strip-ansi": {"version": "3.0.1", "integrity": "sha512-VhumSSbBqDTP8p2ZLKj40UjBCV4+v8bUSEpUb4KjRgWk9pbqGF4REFj6KEagidb2f/M6AzC0EmFyDNGaw9OCzg==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/fsevents/node_modules/strip-json-comments": {"version": "2.0.1", "integrity": "sha512-4gB8na07fecVVkOI6Rs4e7T6NOTki5EmL7TUduTs6bu3EdnSycntVJ4re8kgZA+wx9IueI2Y11bfbgwtzuE0KQ==", "dev": true, "inBundle": true, "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/fsevents/node_modules/tar": {"version": "4.4.8", "integrity": "sha512-LzHF64s5chPQQS0IYBn9IN5h3i98c12bo4NCO7e0sGM2llXQ3p2FGC5sdENN4cTW48O915Sh+x+EXx7XW96xYQ==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"chownr": "^1.1.1", "fs-minipass": "^1.2.5", "minipass": "^2.3.4", "minizlib": "^1.1.1", "mkdirp": "^0.5.0", "safe-buffer": "^5.1.2", "yallist": "^3.0.2"}, "engines": {"node": ">=4.5"}}, "node_modules/fsevents/node_modules/util-deprecate": {"version": "1.0.2", "integrity": "sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/wide-align": {"version": "1.1.3", "integrity": "sha512-QGkOQc8XL6Bt5PwnsExKBPuMKBxnGxWWW3fU55Xt4feHozMUhdUMaBCk290qpm/wG5u/RSKzwdAC4i51YigihA==", "dev": true, "inBundle": true, "optional": true, "dependencies": {"string-width": "^1.0.2 || 2"}}, "node_modules/fsevents/node_modules/wrappy": {"version": "1.0.2", "integrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==", "dev": true, "inBundle": true, "optional": true}, "node_modules/fsevents/node_modules/yallist": {"version": "3.0.3", "integrity": "sha512-S+Zk8DEWE6oKpV+vI3qWkaK+jSbIK86pCwe2IF/xwIpQ8jEuxpw9NyaGjmp9+BoJv5FV2piqCDcoCtStppiq2A==", "dev": true, "inBundle": true, "optional": true}, "node_modules/function-bind": {"version": "1.1.1", "resolved": "http://registry.npm.taobao.org/function-bind/download/function-bind-1.1.1.tgz", "integrity": "sha1-pWiZ0+o8m6uHS7l3O3xe3pL0iV0=", "dev": true}, "node_modules/get-caller-file": {"version": "1.0.3", "resolved": "http://registry.npm.taobao.org/get-caller-file/download/get-caller-file-1.0.3.tgz", "integrity": "sha1-+Xj6TJDR3+f/LWvtoqUV5xO9z0o=", "dev": true}, "node_modules/get-stream": {"version": "4.1.0", "resolved": "http://registry.npm.taobao.org/get-stream/download/get-stream-4.1.0.tgz", "integrity": "sha1-wbJVV189wh1Zv8ec09K0axw6VLU=", "dev": true, "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/get-value": {"version": "2.0.6", "resolved": "http://registry.npm.taobao.org/get-value/download/get-value-2.0.6.tgz", "integrity": "sha1-3BXKHGcjh8p2vTesCjlbogQqLCg=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/getpass": {"version": "0.1.7", "resolved": "http://registry.npm.taobao.org/getpass/download/getpass-0.1.7.tgz", "integrity": "sha1-Xv+OPmhNVprkyysSgmBOi6YhSfo=", "dev": true, "dependencies": {"assert-plus": "^1.0.0"}}, "node_modules/glob": {"version": "7.1.3", "resolved": "http://registry.npm.taobao.org/glob/download/glob-7.1.3.tgz", "integrity": "sha1-OWCDLT8VdBCDQtr9OmezMsCWnfE=", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/glob-parent": {"version": "3.1.0", "resolved": "http://registry.npm.taobao.org/glob-parent/download/glob-parent-3.1.0.tgz", "integrity": "sha1-nmr2KZ2NO9K9QEMIMr0RPfkGxa4=", "dev": true, "dependencies": {"is-glob": "^3.1.0", "path-dirname": "^1.0.0"}}, "node_modules/glob-parent/node_modules/is-glob": {"version": "3.1.0", "resolved": "http://registry.npm.taobao.org/is-glob/download/is-glob-3.1.0.tgz", "integrity": "sha1-e6WuJCF4BKxwcHuWkiVnSGzD6Eo=", "dev": true, "dependencies": {"is-extglob": "^2.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/glob-to-regexp": {"version": "0.3.0", "resolved": "http://registry.npm.taobao.org/glob-to-regexp/download/glob-to-regexp-0.3.0.tgz", "integrity": "sha1-jFoUlNIGbFcMw7/kSWF1rMTVAqs=", "dev": true}, "node_modules/globals": {"version": "11.11.0", "resolved": "http://registry.npm.taobao.org/globals/download/globals-11.11.0.tgz", "integrity": "sha1-3Pk3V/ot5Uhvvu1xGFOK33ienC4=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/globby": {"version": "9.1.0", "resolved": "http://registry.npm.taobao.org/globby/download/globby-9.1.0.tgz", "integrity": "sha1-6Q9NUTQQnm2FWr3TG9sbCFQoWS4=", "dev": true, "dependencies": {"@types/glob": "^7.1.1", "array-union": "^1.0.2", "dir-glob": "^2.2.1", "fast-glob": "^2.2.6", "glob": "^7.1.3", "ignore": "^4.0.3", "pify": "^4.0.1", "slash": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/globby/node_modules/ignore": {"version": "4.0.6", "resolved": "http://registry.npm.taobao.org/ignore/download/ignore-4.0.6.tgz", "integrity": "sha1-dQ49tYYgh7RzfrrIIH/9HvJ7Jfw=", "dev": true, "engines": {"node": ">= 4"}}, "node_modules/graceful-fs": {"version": "4.1.15", "resolved": "http://registry.npm.taobao.org/graceful-fs/download/graceful-fs-4.1.15.tgz", "integrity": "sha1-/7cD4QZuig7qpMi4C6klPu77+wA=", "dev": true}, "node_modules/gzip-size": {"version": "5.0.0", "resolved": "http://registry.npm.taobao.org/gzip-size/download/gzip-size-5.0.0.tgz", "integrity": "sha1-pV7NmSIvTEj9jAHGJc47NJ0KDoA=", "dev": true, "dependencies": {"duplexer": "^0.1.1", "pify": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/gzip-size/node_modules/pify": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/pify/download/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/handle-thing": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/handle-thing/download/handle-thing-2.0.0.tgz", "integrity": "sha1-DgOWlf9QyT/CiFV9aW88HcZ3Z1Q=", "dev": true}, "node_modules/har-schema": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/har-schema/download/har-schema-2.0.0.tgz", "integrity": "sha1-qUwiJOvKwEeCoNkDVSHyRzW37JI=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/har-validator": {"version": "5.1.3", "resolved": "http://registry.npm.taobao.org/har-validator/download/har-validator-5.1.3.tgz", "integrity": "sha1-HvievT5JllV2de7ZiTEQ3DUPoIA=", "deprecated": "this library is no longer supported", "dev": true, "dependencies": {"ajv": "^6.5.5", "har-schema": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/has": {"version": "1.0.3", "resolved": "http://registry.npm.taobao.org/has/download/has-1.0.3.tgz", "integrity": "sha1-ci18v8H2qoJB8W3YFOAR4fQeh5Y=", "dev": true, "dependencies": {"function-bind": "^1.1.1"}, "engines": {"node": ">= 0.4.0"}}, "node_modules/has-ansi": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/has-ansi/download/has-ansi-2.0.0.tgz", "integrity": "sha1-NPUEnOHs3ysGSa8+8k5F7TVBbZE=", "dev": true, "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-ansi/node_modules/ansi-regex": {"version": "2.1.1", "resolved": "http://registry.npm.taobao.org/ansi-regex/download/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/has-flag": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/has-flag/download/has-flag-3.0.0.tgz", "integrity": "sha1-tdRU3CGZriJWmfNGfloH87lVuv0=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/has-symbols": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/has-symbols/download/has-symbols-1.0.0.tgz", "integrity": "sha1-uhqPGvKg/DllD1yFA2dwQSIGO0Q=", "dev": true, "engines": {"node": ">= 0.4"}}, "node_modules/has-value": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/has-value/download/has-value-1.0.0.tgz", "integrity": "sha1-GLKB2lhbHFxR3vJMkw7SmgvmsXc=", "dev": true, "dependencies": {"get-value": "^2.0.6", "has-values": "^1.0.0", "isobject": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-values": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/has-values/download/has-values-1.0.0.tgz", "integrity": "sha1-lbC2P+whRmGab+V/51Yo1aOe/k8=", "dev": true, "dependencies": {"is-number": "^3.0.0", "kind-of": "^4.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/has-values/node_modules/kind-of": {"version": "4.0.0", "resolved": "http://registry.npm.taobao.org/kind-of/download/kind-of-4.0.0.tgz", "integrity": "sha1-IIE989cSkosgc3hpGkUGb65y3Vc=", "dev": true, "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/hash-base": {"version": "3.0.4", "resolved": "http://registry.npm.taobao.org/hash-base/download/hash-base-3.0.4.tgz", "integrity": "sha1-X8hoaEfs1zSZQDMZprCj8/auSRg=", "dev": true, "dependencies": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}, "engines": {"node": ">=4"}}, "node_modules/hash-sum": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/hash-sum/download/hash-sum-1.0.2.tgz", "integrity": "sha1-M7QHd3VMZDJXPBIMw4CLvRDUfwQ=", "dev": true}, "node_modules/hash.js": {"version": "1.1.7", "resolved": "http://registry.npm.taobao.org/hash.js/download/hash.js-1.1.7.tgz", "integrity": "sha1-C6vKU46NTuSg+JiNaIZlN6ADz0I=", "dev": true, "dependencies": {"inherits": "^2.0.3", "minimalistic-assert": "^1.0.1"}}, "node_modules/he": {"version": "1.2.0", "resolved": "http://registry.npm.taobao.org/he/download/he-1.2.0.tgz", "integrity": "sha1-hK5l+n6vsWX922FWauFLrwVmTw8=", "dev": true, "bin": {"he": "bin/he"}}, "node_modules/hex-color-regex": {"version": "1.1.0", "resolved": "http://registry.npm.taobao.org/hex-color-regex/download/hex-color-regex-1.1.0.tgz", "integrity": "sha1-TAb8y0YC/iYCs8k9+C1+fb8aio4=", "dev": true}, "node_modules/hmac-drbg": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/hmac-drbg/download/hmac-drbg-1.0.1.tgz", "integrity": "sha1-0nRXAQJabHdabFRXk+1QL8DGSaE=", "dev": true, "dependencies": {"hash.js": "^1.0.3", "minimalistic-assert": "^1.0.0", "minimalistic-crypto-utils": "^1.0.1"}}, "node_modules/hoek": {"version": "6.1.2", "resolved": "http://registry.npm.taobao.org/hoek/download/hoek-6.1.2.tgz", "integrity": "sha1-mebQcFYYOd507kJ7YapHa9a939Y=", "deprecated": "This version has been deprecated in accordance with the hapi support policy (hapi.im/support). Please upgrade to the latest version to get the best features, bug fixes, and security patches. If you are unable to upgrade at this time, paid support is available for older versions (hapi.im/commercial).", "dev": true}, "node_modules/hoopy": {"version": "0.1.4", "resolved": "http://registry.npm.taobao.org/hoopy/download/hoopy-0.1.4.tgz", "integrity": "sha1-YJIH1mEQADOpqUAq096mdzgcGx0=", "dev": true, "engines": {"node": ">= 6.0.0"}}, "node_modules/hosted-git-info": {"version": "2.7.1", "resolved": "http://registry.npm.taobao.org/hosted-git-info/download/hosted-git-info-2.7.1.tgz", "integrity": "sha1-l/I2l3vW4SVAiTD/bePuxigewEc=", "dev": true}, "node_modules/hpack.js": {"version": "2.1.6", "resolved": "http://registry.npm.taobao.org/hpack.js/download/hpack.js-2.1.6.tgz", "integrity": "sha1-h3dMCUnlE/QuhFdbPEVoH63ioLI=", "dev": true, "dependencies": {"inherits": "^2.0.1", "obuf": "^1.0.0", "readable-stream": "^2.0.1", "wbuf": "^1.1.0"}}, "node_modules/hsl-regex": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/hsl-regex/download/hsl-regex-1.0.0.tgz", "integrity": "sha1-1JMwx4ntgZ4nakwNJy3/owsY/m4=", "dev": true}, "node_modules/hsla-regex": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/hsla-regex/download/hsla-regex-1.0.0.tgz", "integrity": "sha1-wc56MWjIxmFAM6S194d/OyJfnDg=", "dev": true}, "node_modules/html-comment-regex": {"version": "1.1.2", "resolved": "http://registry.npm.taobao.org/html-comment-regex/download/html-comment-regex-1.1.2.tgz", "integrity": "sha1-l9RoiutcgYhqNk+qDK0d2hTUM6c=", "dev": true}, "node_modules/html-entities": {"version": "1.2.1", "resolved": "http://registry.npm.taobao.org/html-entities/download/html-entities-1.2.1.tgz", "integrity": "sha1-DfKTUfByEWNRXfueVUPl9u7VFi8=", "dev": true, "engines": ["node >= 0.4.0"]}, "node_modules/html-minifier": {"version": "3.5.21", "resolved": "http://registry.npm.taobao.org/html-minifier/download/html-minifier-3.5.21.tgz", "integrity": "sha1-0AQOBUcw41TbAIRjWTGUAVIS0gw=", "dev": true, "dependencies": {"camel-case": "3.0.x", "clean-css": "4.2.x", "commander": "2.17.x", "he": "1.2.x", "param-case": "2.1.x", "relateurl": "0.2.x", "uglify-js": "3.4.x"}, "bin": {"html-minifier": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/html-minifier/node_modules/commander": {"version": "2.17.1", "resolved": "http://registry.npm.taobao.org/commander/download/commander-2.17.1.tgz", "integrity": "sha1-vXerfebelCBc6sxy8XFtKfIKd78=", "dev": true}, "node_modules/html-tags": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/html-tags/download/html-tags-2.0.0.tgz", "integrity": "sha1-ELMKOGCF9Dzt41PMj6fLDe7qZos=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/html-webpack-plugin": {"version": "3.2.0", "resolved": "http://registry.npm.taobao.org/html-webpack-plugin/download/html-webpack-plugin-3.2.0.tgz", "integrity": "sha1-sBq71yOsqqeze2r0SS69oD2d03s=", "deprecated": "3.x is no longer supported", "dev": true, "dependencies": {"html-minifier": "^3.2.3", "loader-utils": "^0.2.16", "lodash": "^4.17.3", "pretty-error": "^2.0.2", "tapable": "^1.0.0", "toposort": "^1.0.0", "util.promisify": "1.0.0"}, "engines": {"node": ">=6.9"}, "peerDependencies": {"webpack": "^1.0.0 || ^2.0.0 || ^3.0.0 || ^4.0.0"}}, "node_modules/html-webpack-plugin/node_modules/big.js": {"version": "3.2.0", "resolved": "http://registry.npm.taobao.org/big.js/download/big.js-3.2.0.tgz", "integrity": "sha1-pfwpi4G54Nyi5FiCR4S2XFK6WI4=", "dev": true, "engines": {"node": "*"}}, "node_modules/html-webpack-plugin/node_modules/json5": {"version": "0.5.1", "resolved": "http://registry.npm.taobao.org/json5/download/json5-0.5.1.tgz", "integrity": "sha1-Hq3nrMASA0rYTiOWdn6tn6VJWCE=", "dev": true, "bin": {"json5": "lib/cli.js"}}, "node_modules/html-webpack-plugin/node_modules/loader-utils": {"version": "0.2.17", "resolved": "http://registry.npm.taobao.org/loader-utils/download/loader-utils-0.2.17.tgz", "integrity": "sha1-+G5jdNQyBabmxg6RlvF8Apm/s0g=", "dev": true, "dependencies": {"big.js": "^3.1.3", "emojis-list": "^2.0.0", "json5": "^0.5.0", "object-assign": "^4.0.1"}}, "node_modules/html2canvas": {"version": "1.4.1", "resolved": "https://registry.npmjs.org/html2canvas/-/html2canvas-1.4.1.tgz", "integrity": "sha512-fPU6BHNpsyIhr8yyMpTLLxAbkaK8ArIBcmZIRiBLiDhjeqvXolaEmDGmELFuX9I4xDcaKKcJl+TKZLqruBbmWA==", "dependencies": {"css-line-break": "^2.1.0", "text-segmentation": "^1.0.3"}, "engines": {"node": ">=8.0.0"}}, "node_modules/htmlparser2": {"version": "3.10.1", "resolved": "http://registry.npm.taobao.org/htmlparser2/download/htmlparser2-3.10.1.tgz", "integrity": "sha1-vWedw/WYl7ajS7EHSchVu1OpOS8=", "dev": true, "dependencies": {"domelementtype": "^1.3.1", "domhandler": "^2.3.0", "domutils": "^1.5.1", "entities": "^1.1.1", "inherits": "^2.0.1", "readable-stream": "^3.1.1"}}, "node_modules/htmlparser2/node_modules/readable-stream": {"version": "3.2.0", "resolved": "http://registry.npm.taobao.org/readable-stream/download/readable-stream-3.2.0.tgz", "integrity": "sha1-3hfyKYZMEgqfVpRXVuTzLEBFJF0=", "dev": true, "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/http-deceiver": {"version": "1.2.7", "resolved": "http://registry.npm.taobao.org/http-deceiver/download/http-deceiver-1.2.7.tgz", "integrity": "sha1-+nFolEq5pRnTN8sL7HKE3D5yPYc=", "dev": true}, "node_modules/http-errors": {"version": "1.6.3", "resolved": "http://registry.npm.taobao.org/http-errors/download/http-errors-1.6.3.tgz", "integrity": "sha1-i1VoC7S+KDoLW/TqLjhYC+HZMg0=", "dev": true, "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.0", "statuses": ">= 1.4.0 < 2"}, "engines": {"node": ">= 0.6"}}, "node_modules/http-parser-js": {"version": "0.5.0", "resolved": "http://registry.npm.taobao.org/http-parser-js/download/http-parser-js-0.5.0.tgz", "integrity": "sha1-1l7b7ehDSdDcMDIIFaFdOcw8u9g=", "dev": true}, "node_modules/http-proxy": {"version": "1.17.0", "resolved": "http://registry.npm.taobao.org/http-proxy/download/http-proxy-1.17.0.tgz", "integrity": "sha1-etOElGWPhGBeL220Q230EPTlvpo=", "dev": true, "dependencies": {"eventemitter3": "^3.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/http-proxy-middleware": {"version": "0.19.1", "resolved": "http://registry.npm.taobao.org/http-proxy-middleware/download/http-proxy-middleware-0.19.1.tgz", "integrity": "sha1-GDx9xKoUeRUDBkmMIQza+WCApDo=", "dev": true, "dependencies": {"http-proxy": "^1.17.0", "is-glob": "^4.0.0", "lodash": "^4.17.11", "micromatch": "^3.1.10"}, "engines": {"node": ">=4.0.0"}}, "node_modules/http-signature": {"version": "1.2.0", "resolved": "http://registry.npm.taobao.org/http-signature/download/http-signature-1.2.0.tgz", "integrity": "sha1-muzZJRFHcvPZW2WmCruPfBj7rOE=", "dev": true, "dependencies": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}, "engines": {"node": ">=0.8", "npm": ">=1.3.7"}}, "node_modules/https-browserify": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/https-browserify/download/https-browserify-1.0.0.tgz", "integrity": "sha1-7AbBDgo0wPL68Zn3/X/Hj//QPHM=", "dev": true}, "node_modules/iconv-lite": {"version": "0.4.23", "resolved": "http://registry.npm.taobao.org/iconv-lite/download/iconv-lite-0.4.23.tgz", "integrity": "sha1-KXhx9jvlB63Pv8pxXQzQ7thOmmM=", "dev": true, "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/icss-replace-symbols": {"version": "1.1.0", "resolved": "http://registry.npm.taobao.org/icss-replace-symbols/download/icss-replace-symbols-1.1.0.tgz", "integrity": "sha1-Bupvg2ead0njhs/h/oEq5dsiPe0=", "dev": true}, "node_modules/icss-utils": {"version": "2.1.0", "resolved": "http://registry.npm.taobao.org/icss-utils/download/icss-utils-2.1.0.tgz", "integrity": "sha1-g/Cg7DeL8yRheLbCrZE28TWxyWI=", "dev": true, "dependencies": {"postcss": "^6.0.1"}}, "node_modules/icss-utils/node_modules/postcss": {"version": "6.0.23", "resolved": "http://registry.npm.taobao.org/postcss/download/postcss-6.0.23.tgz", "integrity": "sha1-YcgswyisYOZ3ZF+XkFTrmLwOMyQ=", "dev": true, "dependencies": {"chalk": "^2.4.1", "source-map": "^0.6.1", "supports-color": "^5.4.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/icss-utils/node_modules/source-map": {"version": "0.6.1", "resolved": "http://registry.npm.taobao.org/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/ieee754": {"version": "1.1.12", "resolved": "http://registry.npm.taobao.org/ieee754/download/ieee754-1.1.12.tgz", "integrity": "sha1-UL8k5bnIu5ivSWTJQc2wkY2ntgs=", "dev": true}, "node_modules/iferr": {"version": "0.1.5", "resolved": "http://registry.npm.taobao.org/iferr/download/iferr-0.1.5.tgz", "integrity": "sha1-xg7taebY/bazEEofy8ocGS3FtQE=", "dev": true}, "node_modules/ignore": {"version": "3.3.10", "resolved": "http://registry.npm.taobao.org/ignore/download/ignore-3.3.10.tgz", "integrity": "sha1-Cpf7h2mG6AgcYxFg+PnziRV/AEM=", "dev": true}, "node_modules/import-cwd": {"version": "2.1.0", "resolved": "http://registry.npm.taobao.org/import-cwd/download/import-cwd-2.1.0.tgz", "integrity": "sha1-qmzzbnInYShcs3HsZRn1PiQ1sKk=", "dev": true, "dependencies": {"import-from": "^2.1.0"}, "engines": {"node": ">=4"}}, "node_modules/import-fresh": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/import-fresh/download/import-fresh-2.0.0.tgz", "integrity": "sha1-2BNVwVYS04bGH53dOSLUMEgipUY=", "dev": true, "dependencies": {"caller-path": "^2.0.0", "resolve-from": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/import-from": {"version": "2.1.0", "resolved": "http://registry.npm.taobao.org/import-from/download/import-from-2.1.0.tgz", "integrity": "sha1-M1238qev/VOqpHHUuAId7ja387E=", "dev": true, "dependencies": {"resolve-from": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/import-local": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/import-local/download/import-local-2.0.0.tgz", "integrity": "sha1-VQcL44pZk88Y72236WH1vuXFoJ0=", "dev": true, "dependencies": {"pkg-dir": "^3.0.0", "resolve-cwd": "^2.0.0"}, "bin": {"import-local-fixture": "fixtures/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/imurmurhash": {"version": "0.1.4", "resolved": "http://registry.npm.taobao.org/imurmurhash/download/imurmurhash-0.1.4.tgz", "integrity": "sha1-khi5srkoojixPcT7a21XbyMUU+o=", "dev": true, "engines": {"node": ">=0.8.19"}}, "node_modules/indexes-of": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/indexes-of/download/indexes-of-1.0.1.tgz", "integrity": "sha1-8w9xbI4r00bHtn0985FVZqfAVgc=", "dev": true}, "node_modules/indexof": {"version": "0.0.1", "resolved": "http://registry.npm.taobao.org/indexof/download/indexof-0.0.1.tgz", "integrity": "sha1-gtwzbSMrkGIXnQWrMpOmYFn9Q10=", "dev": true}, "node_modules/inflight": {"version": "1.0.6", "resolved": "http://registry.npm.taobao.org/inflight/download/inflight-1.0.6.tgz", "integrity": "sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=", "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.", "dev": true, "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.3", "resolved": "http://registry.npm.taobao.org/inherits/download/inherits-2.0.3.tgz", "integrity": "sha1-Yzwsg+PaQqUC9SRmAiSA9CCCYd4=", "dev": true}, "node_modules/internal-ip": {"version": "4.2.0", "resolved": "http://registry.npm.taobao.org/internal-ip/download/internal-ip-4.2.0.tgz", "integrity": "sha1-RugbY42Ewzjlxn5CsaF9tn0IFPo=", "dev": true, "dependencies": {"default-gateway": "^4.0.1", "ipaddr.js": "^1.9.0"}, "engines": {"node": ">=6"}}, "node_modules/internal-ip/node_modules/ipaddr.js": {"version": "1.9.0", "resolved": "http://registry.npm.taobao.org/ipaddr.js/download/ipaddr.js-1.9.0.tgz?cache=0&other_urls=http%3A%2F%2Fregistry.npm.taobao.org%2Fipaddr.js%2Fdownload%2Fipaddr.js-1.9.0.tgz", "integrity": "sha1-N9905DCg5HVQ/lSi3v4w2KzZX2U=", "dev": true, "engines": {"node": ">= 0.10"}}, "node_modules/invariant": {"version": "2.2.4", "resolved": "http://registry.npm.taobao.org/invariant/download/invariant-2.2.4.tgz", "integrity": "sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY=", "dev": true, "dependencies": {"loose-envify": "^1.0.0"}}, "node_modules/invert-kv": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/invert-kv/download/invert-kv-2.0.0.tgz", "integrity": "sha1-c5P1r6Weyf9fZ6J2INEcIm4+7AI=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/ip": {"version": "1.1.5", "resolved": "http://registry.npm.taobao.org/ip/download/ip-1.1.5.tgz", "integrity": "sha1-vd7XARQpCCjAoDnnLvJfWq7ENUo=", "dev": true}, "node_modules/ip-regex": {"version": "2.1.0", "resolved": "http://registry.npm.taobao.org/ip-regex/download/ip-regex-2.1.0.tgz", "integrity": "sha1-+ni/XS5pE8kRzp+BnuUUa7bYROk=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/ipaddr.js": {"version": "1.8.0", "resolved": "http://registry.npm.taobao.org/ipaddr.js/download/ipaddr.js-1.8.0.tgz?cache=0&other_urls=http%3A%2F%2Fregistry.npm.taobao.org%2Fipaddr.js%2Fdownload%2Fipaddr.js-1.8.0.tgz", "integrity": "sha1-6qM9bd16zo9/b+DJygRA5wZzix4=", "dev": true, "engines": {"node": ">= 0.10"}}, "node_modules/is-absolute-url": {"version": "2.1.0", "resolved": "http://registry.npm.taobao.org/is-absolute-url/download/is-absolute-url-2.1.0.tgz", "integrity": "sha1-UFMN+4T8yap9vnhS6Do3uTufKqY=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-accessor-descriptor": {"version": "0.1.6", "resolved": "http://registry.npm.taobao.org/is-accessor-descriptor/download/is-accessor-descriptor-0.1.6.tgz", "integrity": "sha1-qeEss66Nh2cn7u84Q/igiXtcmNY=", "deprecated": "Please upgrade to v0.1.7", "dev": true, "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-accessor-descriptor/node_modules/kind-of": {"version": "3.2.2", "resolved": "http://registry.npm.taobao.org/kind-of/download/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-arrayish": {"version": "0.2.1", "resolved": "http://registry.npm.taobao.org/is-arrayish/download/is-arrayish-0.2.1.tgz", "integrity": "sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=", "dev": true}, "node_modules/is-binary-path": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/is-binary-path/download/is-binary-path-1.0.1.tgz", "integrity": "sha1-dfFmQrSA8YenEcgUFh/TpKdlWJg=", "dev": true, "dependencies": {"binary-extensions": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-buffer": {"version": "1.1.6", "resolved": "http://registry.npm.taobao.org/is-buffer/download/is-buffer-1.1.6.tgz", "integrity": "sha1-76ouqdqg16suoTqXsritUf776L4="}, "node_modules/is-callable": {"version": "1.1.4", "resolved": "http://registry.npm.taobao.org/is-callable/download/is-callable-1.1.4.tgz", "integrity": "sha1-HhrfIZ4e62hNaR+dagX/DTCiTXU=", "dev": true, "engines": {"node": ">= 0.4"}}, "node_modules/is-ci": {"version": "1.2.1", "resolved": "http://registry.npm.taobao.org/is-ci/download/is-ci-1.2.1.tgz?cache=0&other_urls=http%3A%2F%2Fregistry.npm.taobao.org%2Fis-ci%2Fdownload%2Fis-ci-1.2.1.tgz", "integrity": "sha1-43ecjuF/zPQoSI9uKBGH8uYyhBw=", "dev": true, "dependencies": {"ci-info": "^1.5.0"}, "bin": {"is-ci": "bin.js"}}, "node_modules/is-color-stop": {"version": "1.1.0", "resolved": "http://registry.npm.taobao.org/is-color-stop/download/is-color-stop-1.1.0.tgz", "integrity": "sha1-z/9HGu5N1cnhWFmPvhKWe1za00U=", "dev": true, "dependencies": {"css-color-names": "^0.0.4", "hex-color-regex": "^1.1.0", "hsl-regex": "^1.0.0", "hsla-regex": "^1.0.0", "rgb-regex": "^1.0.1", "rgba-regex": "^1.0.0"}}, "node_modules/is-data-descriptor": {"version": "0.1.4", "resolved": "http://registry.npm.taobao.org/is-data-descriptor/download/is-data-descriptor-0.1.4.tgz", "integrity": "sha1-C17mSDiOLIYCgueT8YVv7D8wG1Y=", "deprecated": "Please upgrade to v0.1.5", "dev": true, "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-data-descriptor/node_modules/kind-of": {"version": "3.2.2", "resolved": "http://registry.npm.taobao.org/kind-of/download/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-date-object": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/is-date-object/download/is-date-object-1.0.1.tgz", "integrity": "sha1-mqIOtq7rv/d/vTPnTKAbM1gdOhY=", "dev": true, "engines": {"node": ">= 0.4"}}, "node_modules/is-descriptor": {"version": "0.1.6", "resolved": "http://registry.npm.taobao.org/is-descriptor/download/is-descriptor-0.1.6.tgz", "integrity": "sha1-Nm2CQN3kh8pRgjsaufB6EKeCUco=", "dev": true, "dependencies": {"is-accessor-descriptor": "^0.1.6", "is-data-descriptor": "^0.1.4", "kind-of": "^5.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-descriptor/node_modules/kind-of": {"version": "5.1.0", "resolved": "http://registry.npm.taobao.org/kind-of/download/kind-of-5.1.0.tgz", "integrity": "sha1-cpyR4thXt6QZofmqZWhcTDP1hF0=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-directory": {"version": "0.3.1", "resolved": "http://registry.npm.taobao.org/is-directory/download/is-directory-0.3.1.tgz", "integrity": "sha1-YTObbyR1/Hcv2cnYP1yFddwVSuE=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-extendable": {"version": "0.1.1", "resolved": "http://registry.npm.taobao.org/is-extendable/download/is-extendable-0.1.1.tgz", "integrity": "sha1-YrEQ4omkcUGOPsNqYX1HLjAd/Ik=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-extglob": {"version": "2.1.1", "resolved": "http://registry.npm.taobao.org/is-extglob/download/is-extglob-2.1.1.tgz", "integrity": "sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/is-fullwidth-code-point/download/is-fullwidth-code-point-2.0.0.tgz", "integrity": "sha1-o7MKXE8ZkYMWeqq5O+764937ZU8=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/is-glob": {"version": "4.0.0", "resolved": "http://registry.npm.taobao.org/is-glob/download/is-glob-4.0.0.tgz", "integrity": "sha1-lSHHaEXMJhCoUgPd8ICpWML/q8A=", "dev": true, "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-number": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/is-number/download/is-number-3.0.0.tgz", "integrity": "sha1-JP1iAaR4LPUFYcgQJ2r8fRLXEZU=", "dev": true, "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-number/node_modules/kind-of": {"version": "3.2.2", "resolved": "http://registry.npm.taobao.org/kind-of/download/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-obj": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/is-obj/download/is-obj-1.0.1.tgz", "integrity": "sha1-PkcprB9f3gJc19g6iW2rn09n2w8=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-path-cwd": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/is-path-cwd/download/is-path-cwd-1.0.0.tgz", "integrity": "sha1-0iXsIxMuie3Tj9p2dHLmLmXxEG0=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-path-in-cwd": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/is-path-in-cwd/download/is-path-in-cwd-1.0.1.tgz", "integrity": "sha1-WsSLNF72dTOb1sekipEhELJBz1I=", "dev": true, "dependencies": {"is-path-inside": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-path-inside": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/is-path-inside/download/is-path-inside-1.0.1.tgz", "integrity": "sha1-jvW33lBDej/cprToZe96pVy0gDY=", "dev": true, "dependencies": {"path-is-inside": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-plain-object": {"version": "2.0.4", "resolved": "http://registry.npm.taobao.org/is-plain-object/download/is-plain-object-2.0.4.tgz", "integrity": "sha1-LBY7P6+xtgbZ0Xko8FwqHDjgdnc=", "dev": true, "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-regex": {"version": "1.0.4", "resolved": "http://registry.npm.taobao.org/is-regex/download/is-regex-1.0.4.tgz", "integrity": "sha1-VRdIm1RwkbCTDglWVM7SXul+lJE=", "dev": true, "dependencies": {"has": "^1.0.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/is-resolvable": {"version": "1.1.0", "resolved": "http://registry.npm.taobao.org/is-resolvable/download/is-resolvable-1.1.0.tgz", "integrity": "sha1-+xj4fOH+uSUWnJpAfBkxijIG7Yg=", "dev": true}, "node_modules/is-stream": {"version": "1.1.0", "resolved": "http://registry.npm.taobao.org/is-stream/download/is-stream-1.1.0.tgz", "integrity": "sha1-EtSj3U5o4Lec6428hBc66A2RykQ=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-svg": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/is-svg/download/is-svg-3.0.0.tgz", "integrity": "sha1-kyHb0pwhLlypnE+peUxxS8r6L3U=", "dev": true, "dependencies": {"html-comment-regex": "^1.1.0"}, "engines": {"node": ">=4"}}, "node_modules/is-symbol": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/is-symbol/download/is-symbol-1.0.2.tgz", "integrity": "sha1-oFX2rlcZLK7jKeeoYBGLSXqVDzg=", "dev": true, "dependencies": {"has-symbols": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "node_modules/is-typedarray": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/is-typedarray/download/is-typedarray-1.0.0.tgz", "integrity": "sha1-5HnICFjfDBsR3dppQPlgEfzaSpo=", "dev": true}, "node_modules/is-windows": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/is-windows/download/is-windows-1.0.2.tgz", "integrity": "sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/is-wsl": {"version": "1.1.0", "resolved": "http://registry.npm.taobao.org/is-wsl/download/is-wsl-1.1.0.tgz", "integrity": "sha1-HxbkqiKwTRM2tmGIpmrzxgDDpm0=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/isarray": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/isarray/download/isarray-1.0.0.tgz", "integrity": "sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=", "dev": true}, "node_modules/isemail": {"version": "3.2.0", "resolved": "http://registry.npm.taobao.org/isemail/download/isemail-3.2.0.tgz", "integrity": "sha1-WTEKAhkxqfsGu7UeFVzgs/I2gyw=", "dev": true, "dependencies": {"punycode": "2.x.x"}, "engines": {"node": ">=4.0.0"}}, "node_modules/isexe": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/isexe/download/isexe-2.0.0.tgz", "integrity": "sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=", "dev": true}, "node_modules/isobject": {"version": "3.0.1", "resolved": "http://registry.npm.taobao.org/isobject/download/isobject-3.0.1.tgz", "integrity": "sha1-TkMekrEalzFjaqH5yNHMvP2reN8=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/isstream": {"version": "0.1.2", "resolved": "http://registry.npm.taobao.org/isstream/download/isstream-0.1.2.tgz", "integrity": "sha1-R+Y/evVa+m+S4VAOaQ64uFKcCZo=", "dev": true}, "node_modules/javascript-stringify": {"version": "1.6.0", "resolved": "http://registry.npm.taobao.org/javascript-stringify/download/javascript-stringify-1.6.0.tgz", "integrity": "sha1-FC0RHzpuPa6PSpr9d9RYVbWpzOM=", "dev": true}, "node_modules/joi": {"version": "14.3.1", "resolved": "http://registry.npm.taobao.org/joi/download/joi-14.3.1.tgz", "integrity": "sha1-FkomLsC4VUZuDDXuoqiFrotscDw=", "deprecated": "This module has moved and is now available at @hapi/joi. Please update your dependencies as this version is no longer maintained an may contain bugs and security issues.", "dev": true, "dependencies": {"hoek": "6.x.x", "isemail": "3.x.x", "topo": "3.x.x"}}, "node_modules/js-cookie": {"version": "2.2.0", "resolved": "http://registry.npm.taobao.org/js-cookie/download/js-cookie-2.2.0.tgz", "integrity": "sha1-Gywnmm7s44ChIWi5JIUmWzWx7/s="}, "node_modules/js-levenshtein": {"version": "1.1.6", "resolved": "http://registry.npm.taobao.org/js-levenshtein/download/js-levenshtein-1.1.6.tgz", "integrity": "sha1-xs7ljrNVA3LfjeuF+tXOZs4B1Z0=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/js-message": {"version": "1.0.5", "resolved": "http://registry.npm.taobao.org/js-message/download/js-message-1.0.5.tgz", "integrity": "sha1-IwDSSxrwjondCVvBpMnJz8uJLRU=", "dev": true, "engines": {"node": ">=0.6.0"}}, "node_modules/js-queue": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/js-queue/download/js-queue-2.0.0.tgz", "integrity": "sha1-NiITz4YPRo8BJfxslqvBdCUx+Ug=", "dev": true, "dependencies": {"easy-stack": "^1.0.0"}, "engines": {"node": ">=1.0.0"}}, "node_modules/js-tokens": {"version": "4.0.0", "resolved": "http://registry.npm.taobao.org/js-tokens/download/js-tokens-4.0.0.tgz", "integrity": "sha1-GSA/tZmR35jjoocFDUZHzerzJJk=", "dev": true}, "node_modules/js-yaml": {"version": "3.13.0", "resolved": "http://registry.npm.taobao.org/js-yaml/download/js-yaml-3.13.0.tgz?cache=0&other_urls=http%3A%2F%2Fregistry.npm.taobao.org%2Fjs-yaml%2Fdownload%2Fjs-yaml-3.13.0.tgz", "integrity": "sha1-OO5xeKwO6iyX/22W//SxjH2M+Y4=", "dev": true, "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsbn": {"version": "0.1.1", "resolved": "http://registry.npm.taobao.org/jsbn/download/jsbn-0.1.1.tgz", "integrity": "sha1-peZUwuWi3rXyAdls77yoDA7y9RM=", "dev": true}, "node_modules/jsesc": {"version": "2.5.2", "resolved": "http://registry.npm.taobao.org/jsesc/download/jsesc-2.5.2.tgz", "integrity": "sha1-gFZNLkg9rPbo7yCWUKZ98/DCg6Q=", "dev": true, "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=4"}}, "node_modules/json-parse-better-errors": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/json-parse-better-errors/download/json-parse-better-errors-1.0.2.tgz", "integrity": "sha1-u4Z8+zRQ5pEHwTHRxRS6s9yLyqk=", "dev": true}, "node_modules/json-schema": {"version": "0.2.3", "resolved": "http://registry.npm.taobao.org/json-schema/download/json-schema-0.2.3.tgz", "integrity": "sha1-tIDIkuWaLwWVTOcnvT8qTogvnhM=", "dev": true}, "node_modules/json-schema-traverse": {"version": "0.4.1", "resolved": "http://registry.npm.taobao.org/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz", "integrity": "sha1-afaofZUTq4u4/mO9sJecRI5oRmA=", "dev": true}, "node_modules/json-stringify-safe": {"version": "5.0.1", "resolved": "http://registry.npm.taobao.org/json-stringify-safe/download/json-stringify-safe-5.0.1.tgz", "integrity": "sha1-Epai1Y/UXxmg9s4B1lcB4sc1tus=", "dev": true}, "node_modules/json3": {"version": "3.3.2", "resolved": "http://registry.npm.taobao.org/json3/download/json3-3.3.2.tgz", "integrity": "sha1-PAQ0dD35Pi9cQq7nsZvLSDV19OE=", "deprecated": "Please use the native JSON object instead of JSON 3", "dev": true}, "node_modules/json5": {"version": "2.1.0", "resolved": "http://registry.npm.taobao.org/json5/download/json5-2.1.0.tgz", "integrity": "sha1-56DGLEgoXGKNIKELhcibuAfDKFA=", "dev": true, "dependencies": {"minimist": "^1.2.0"}, "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/jsonfile": {"version": "4.0.0", "resolved": "http://registry.npm.taobao.org/jsonfile/download/jsonfile-4.0.0.tgz", "integrity": "sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=", "dev": true, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/jsonify": {"version": "0.0.0", "resolved": "http://registry.npm.taobao.org/jsonify/download/jsonify-0.0.0.tgz", "integrity": "sha1-LHS27kHZPKUbe1qu6PUDYx0lKnM=", "dev": true, "engines": {"node": "*"}}, "node_modules/jspdf": {"version": "2.5.2", "resolved": "https://registry.npmjs.org/jspdf/-/jspdf-2.5.2.tgz", "integrity": "sha512-myeX9c+p7znDWPk0eTrujCzNjT+CXdXyk7YmJq5nD5V7uLLKmSXnlQ/Jn/kuo3X09Op70Apm0rQSnFWyGK8uEQ==", "dependencies": {"@babel/runtime": "^7.23.2", "atob": "^2.1.2", "btoa": "^1.2.1", "fflate": "^0.8.1"}, "optionalDependencies": {"canvg": "^3.0.6", "core-js": "^3.6.0", "dompurify": "^2.5.4", "html2canvas": "^1.0.0-rc.5"}}, "node_modules/jspdf/node_modules/core-js": {"version": "3.44.0", "resolved": "https://registry.npmjs.org/core-js/-/core-js-3.44.0.tgz", "integrity": "sha512-aFCtd4l6GvAXwVEh3XbbVqJGHDJt0OZRa+5ePGx3LLwi12WfexqQxcsohb2wgsa/92xtl19Hd66G/L+TaAxDMw==", "hasInstallScript": true, "optional": true, "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "node_modules/jsprim": {"version": "1.4.1", "resolved": "http://registry.npm.taobao.org/jsprim/download/jsprim-1.4.1.tgz", "integrity": "sha1-MT5mvB5cwG5Di8G3SZwuXFastqI=", "dev": true, "engines": ["node >=0.6.0"], "dependencies": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.2.3", "verror": "1.10.0"}}, "node_modules/jwt-decode": {"version": "2.2.0", "resolved": "http://registry.npm.taobao.org/jwt-decode/download/jwt-decode-2.2.0.tgz", "integrity": "sha1-fYa9VmefWM5qhHBKZX3TkruoGnk="}, "node_modules/killable": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/killable/download/killable-1.0.1.tgz", "integrity": "sha1-TIzkQRh6Bhx0dPuHygjipjgZSJI=", "dev": true}, "node_modules/kind-of": {"version": "6.0.2", "resolved": "http://registry.npm.taobao.org/kind-of/download/kind-of-6.0.2.tgz", "integrity": "sha1-ARRrNqYhjmTljzqNZt5df8b20FE=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/launch-editor": {"version": "2.2.1", "resolved": "http://registry.npm.taobao.org/launch-editor/download/launch-editor-2.2.1.tgz", "integrity": "sha1-hxtaPuOdZoD8wm03kwtu7aidsMo=", "dev": true, "dependencies": {"chalk": "^2.3.0", "shell-quote": "^1.6.1"}}, "node_modules/launch-editor-middleware": {"version": "2.2.1", "resolved": "http://registry.npm.taobao.org/launch-editor-middleware/download/launch-editor-middleware-2.2.1.tgz", "integrity": "sha1-4UsH5scVSwpLhqD9NFeE5FgEwVc=", "dev": true, "dependencies": {"launch-editor": "^2.2.1"}}, "node_modules/lcid": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/lcid/download/lcid-2.0.0.tgz", "integrity": "sha1-bvXS32DlL4LrIopMNz6NHzlyU88=", "dev": true, "dependencies": {"invert-kv": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/loader-runner": {"version": "2.4.0", "resolved": "http://registry.npm.taobao.org/loader-runner/download/loader-runner-2.4.0.tgz", "integrity": "sha1-7UcGa/5TTX6ExMe5mYwqdWB9k1c=", "dev": true, "engines": {"node": ">=4.3.0 <5.0.0 || >=5.10"}}, "node_modules/loader-utils": {"version": "1.2.3", "resolved": "http://registry.npm.taobao.org/loader-utils/download/loader-utils-1.2.3.tgz", "integrity": "sha1-H/XcaRHJ8KBiUxpMBLYJQGEIwsc=", "dev": true, "dependencies": {"big.js": "^5.2.2", "emojis-list": "^2.0.0", "json5": "^1.0.1"}, "engines": {"node": ">=4.0.0"}}, "node_modules/loader-utils/node_modules/json5": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/json5/download/json5-1.0.1.tgz", "integrity": "sha1-d5+wAYYE+oVOrL9iUhgNg1Q+Pb4=", "dev": true, "dependencies": {"minimist": "^1.2.0"}, "bin": {"json5": "lib/cli.js"}}, "node_modules/locate-path": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/locate-path/download/locate-path-3.0.0.tgz", "integrity": "sha1-2+w7OrdZdYBxtY/ln8QYca8hQA4=", "dev": true, "dependencies": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/lodash": {"version": "4.17.11", "resolved": "http://registry.npm.taobao.org/lodash/download/lodash-4.17.11.tgz", "integrity": "sha1-s56mIp72B+zYniyN8SU2iRysm40=", "dev": true}, "node_modules/lodash.defaultsdeep": {"version": "4.6.0", "resolved": "http://registry.npm.taobao.org/lodash.defaultsdeep/download/lodash.defaultsdeep-4.6.0.tgz", "integrity": "sha1-vsECT4WxvZbL6kBbI8FK1kQ6b4E=", "dev": true}, "node_modules/lodash.kebabcase": {"version": "4.1.1", "resolved": "http://registry.npm.taobao.org/lodash.kebabcase/download/lodash.kebabcase-4.1.1.tgz", "integrity": "sha1-hImxyw0p/4gZXM7KRI/21swpXDY=", "dev": true}, "node_modules/lodash.mapvalues": {"version": "4.6.0", "resolved": "http://registry.npm.taobao.org/lodash.mapvalues/download/lodash.mapvalues-4.6.0.tgz", "integrity": "sha1-G6+lAF3p3W9PJmaMMMo3IwzJaJw=", "dev": true}, "node_modules/lodash.memoize": {"version": "4.1.2", "resolved": "http://registry.npm.taobao.org/lodash.memoize/download/lodash.memoize-4.1.2.tgz", "integrity": "sha1-vMbEmkKihA7Zl/Mj6tpezRguC/4=", "dev": true}, "node_modules/lodash.transform": {"version": "4.6.0", "resolved": "http://registry.npm.taobao.org/lodash.transform/download/lodash.transform-4.6.0.tgz", "integrity": "sha1-EjBkIvYzJK7YSD0/ODMrX2cFR6A=", "dev": true}, "node_modules/lodash.uniq": {"version": "4.5.0", "resolved": "http://registry.npm.taobao.org/lodash.uniq/download/lodash.uniq-4.5.0.tgz", "integrity": "sha1-0CJTc662Uq3BvILklFM5qEJ1R3M=", "dev": true}, "node_modules/log-symbols": {"version": "2.2.0", "resolved": "http://registry.npm.taobao.org/log-symbols/download/log-symbols-2.2.0.tgz", "integrity": "sha1-V0Dhxdbw39pK2TI7UzIQfva0xAo=", "dev": true, "dependencies": {"chalk": "^2.0.1"}, "engines": {"node": ">=4"}}, "node_modules/loglevel": {"version": "1.6.1", "resolved": "http://registry.npm.taobao.org/loglevel/download/loglevel-1.6.1.tgz", "integrity": "sha1-4PyVEztu8nbNyIh82vJKpvFW+Po=", "dev": true, "engines": {"node": ">= 0.6.0"}}, "node_modules/loose-envify": {"version": "1.4.0", "resolved": "http://registry.npm.taobao.org/loose-envify/download/loose-envify-1.4.0.tgz", "integrity": "sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=", "dev": true, "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "bin": {"loose-envify": "cli.js"}}, "node_modules/lower-case": {"version": "1.1.4", "resolved": "http://registry.npm.taobao.org/lower-case/download/lower-case-1.1.4.tgz", "integrity": "sha1-miyr0bno4K6ZOkv31YdcOcQujqw=", "dev": true}, "node_modules/lru-cache": {"version": "5.1.1", "resolved": "http://registry.npm.taobao.org/lru-cache/download/lru-cache-5.1.1.tgz", "integrity": "sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=", "dev": true, "dependencies": {"yallist": "^3.0.2"}}, "node_modules/make-dir": {"version": "2.1.0", "resolved": "http://registry.npm.taobao.org/make-dir/download/make-dir-2.1.0.tgz", "integrity": "sha1-XwMQ4YuL6JjMBwCSlaMK5B6R5vU=", "dev": true, "dependencies": {"pify": "^4.0.1", "semver": "^5.6.0"}, "engines": {"node": ">=6"}}, "node_modules/map-age-cleaner": {"version": "0.1.3", "resolved": "http://registry.npm.taobao.org/map-age-cleaner/download/map-age-cleaner-0.1.3.tgz", "integrity": "sha1-fVg6cwZDTAVf5HSw9FB45uG0uSo=", "dev": true, "dependencies": {"p-defer": "^1.0.0"}, "engines": {"node": ">=6"}}, "node_modules/map-cache": {"version": "0.2.2", "resolved": "http://registry.npm.taobao.org/map-cache/download/map-cache-0.2.2.tgz", "integrity": "sha1-wyq9C9ZSXZsFFkW7TyasXcmKDb8=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/map-visit": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/map-visit/download/map-visit-1.0.0.tgz", "integrity": "sha1-7Nyo8TFE5mDxtb1B8S80edmN+48=", "dev": true, "dependencies": {"object-visit": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/md5.js": {"version": "1.3.5", "resolved": "http://registry.npm.taobao.org/md5.js/download/md5.js-1.3.5.tgz", "integrity": "sha1-tdB7jjIW4+J81yjXL3DR5qNCAF8=", "dev": true, "dependencies": {"hash-base": "^3.0.0", "inherits": "^2.0.1", "safe-buffer": "^5.1.2"}}, "node_modules/mdn-data": {"version": "1.1.4", "resolved": "http://registry.npm.taobao.org/mdn-data/download/mdn-data-1.1.4.tgz", "integrity": "sha1-ULXU/8RXUnZXPE7tuHgIEqhBnwE=", "dev": true}, "node_modules/media-typer": {"version": "0.3.0", "resolved": "http://registry.npm.taobao.org/media-typer/download/media-typer-0.3.0.tgz", "integrity": "sha1-hxDXrwqmJvj/+hzgAWhUUmMlV0g=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/mem": {"version": "4.2.0", "resolved": "http://registry.npm.taobao.org/mem/download/mem-4.2.0.tgz", "integrity": "sha1-XuBXaA7Zy42tinjYIPmoiXoQICU=", "dev": true, "dependencies": {"map-age-cleaner": "^0.1.1", "mimic-fn": "^2.0.0", "p-is-promise": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/mem/node_modules/mimic-fn": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/mimic-fn/download/mimic-fn-2.0.0.tgz", "integrity": "sha1-CRP/CxIdtE71hIJCw4u7NdRMq94=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/memory-fs": {"version": "0.4.1", "resolved": "http://registry.npm.taobao.org/memory-fs/download/memory-fs-0.4.1.tgz", "integrity": "sha1-OpoguEYlI+RHz7x+i7gO1me/xVI=", "dev": true, "dependencies": {"errno": "^0.1.3", "readable-stream": "^2.0.1"}}, "node_modules/merge-descriptors": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/merge-descriptors/download/merge-descriptors-1.0.1.tgz", "integrity": "sha1-sAqqVW3YtEVoFQ7J0blT8/kMu2E=", "dev": true}, "node_modules/merge-source-map": {"version": "1.1.0", "resolved": "http://registry.npm.taobao.org/merge-source-map/download/merge-source-map-1.1.0.tgz", "integrity": "sha1-L93n5gIJOfcJBqaPLXrmheTIxkY=", "dev": true, "dependencies": {"source-map": "^0.6.1"}}, "node_modules/merge-source-map/node_modules/source-map": {"version": "0.6.1", "resolved": "http://registry.npm.taobao.org/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/merge2": {"version": "1.2.3", "resolved": "http://registry.npm.taobao.org/merge2/download/merge2-1.2.3.tgz", "integrity": "sha1-fumdvWm7ZIFoklPwGEiKG5ArDtU=", "dev": true, "engines": {"node": ">= 4.5.0"}}, "node_modules/methods": {"version": "1.1.2", "resolved": "http://registry.npm.taobao.org/methods/download/methods-1.1.2.tgz", "integrity": "sha1-VSmk1nZUE07cxSZmVoNbD4Ua/O4=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/micromatch": {"version": "3.1.10", "resolved": "http://registry.npm.taobao.org/micromatch/download/micromatch-3.1.10.tgz", "integrity": "sha1-cIWbyVyYQJUvNZoGij/En57PrCM=", "dev": true, "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "braces": "^2.3.1", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "extglob": "^2.0.4", "fragment-cache": "^0.2.1", "kind-of": "^6.0.2", "nanomatch": "^1.2.9", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/miller-rabin": {"version": "4.0.1", "resolved": "http://registry.npm.taobao.org/miller-rabin/download/miller-rabin-4.0.1.tgz", "integrity": "sha1-8IA1HIZbDcViqEYpZtqlNUPHik0=", "dev": true, "dependencies": {"bn.js": "^4.0.0", "brorand": "^1.0.1"}, "bin": {"miller-rabin": "bin/miller-rabin"}}, "node_modules/mime": {"version": "2.4.0", "resolved": "http://registry.npm.taobao.org/mime/download/mime-2.4.0.tgz", "integrity": "sha1-4FH9iBNYWF8yed8zP+aU2gvP/dY=", "dev": true, "bin": {"mime": "cli.js"}, "engines": {"node": ">=4.0.0"}}, "node_modules/mime-db": {"version": "1.38.0", "resolved": "http://registry.npm.taobao.org/mime-db/download/mime-db-1.38.0.tgz", "integrity": "sha1-GiqrFtqesWe0nG5N8tnGjWPY4q0=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.22", "resolved": "http://registry.npm.taobao.org/mime-types/download/mime-types-2.1.22.tgz", "integrity": "sha1-/ms1WhkJJqt2mMmgVWoRGZshmb0=", "dev": true, "dependencies": {"mime-db": "~1.38.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimic-fn": {"version": "1.2.0", "resolved": "http://registry.npm.taobao.org/mimic-fn/download/mimic-fn-1.2.0.tgz", "integrity": "sha1-ggyGo5M0ZA6ZUWkovQP8qIBX0CI=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/mini-css-extract-plugin": {"version": "0.5.0", "resolved": "http://registry.npm.taobao.org/mini-css-extract-plugin/download/mini-css-extract-plugin-0.5.0.tgz", "integrity": "sha1-rABZsCuWklFaY3EVsMyf7To1x7A=", "dev": true, "dependencies": {"loader-utils": "^1.1.0", "schema-utils": "^1.0.0", "webpack-sources": "^1.1.0"}, "engines": {"node": ">= 6.9.0 <7.0.0 || >= 8.9.0"}, "peerDependencies": {"webpack": "^4.4.0"}}, "node_modules/mini-css-extract-plugin/node_modules/schema-utils": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/schema-utils/download/schema-utils-1.0.0.tgz", "integrity": "sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A=", "dev": true, "dependencies": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}, "engines": {"node": ">= 4"}}, "node_modules/minimalistic-assert": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/minimalistic-assert/download/minimalistic-assert-1.0.1.tgz", "integrity": "sha1-LhlN4ERibUoQ5/f7wAznPoPk1cc=", "dev": true}, "node_modules/minimalistic-crypto-utils": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/minimalistic-crypto-utils/download/minimalistic-crypto-utils-1.0.1.tgz", "integrity": "sha1-9sAMHAsIIkblxNmd+4x8CDsrWCo=", "dev": true}, "node_modules/minimatch": {"version": "3.0.4", "resolved": "http://registry.npm.taobao.org/minimatch/download/minimatch-3.0.4.tgz?cache=0&other_urls=http%3A%2F%2Fregistry.npm.taobao.org%2Fminimatch%2Fdownload%2Fminimatch-3.0.4.tgz", "integrity": "sha1-UWbihkV/AzBgZL5Ul+jbsMPTIIM=", "dev": true, "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/minimist": {"version": "1.2.0", "resolved": "http://registry.npm.taobao.org/minimist/download/minimist-1.2.0.tgz", "integrity": "sha1-o1AIsg9BOD7sH7kU9M1d95omQoQ=", "dev": true}, "node_modules/mississippi": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/mississippi/download/mississippi-3.0.0.tgz", "integrity": "sha1-6goykfl+C16HdrNj1fChLZTGcCI=", "dev": true, "dependencies": {"concat-stream": "^1.5.0", "duplexify": "^3.4.2", "end-of-stream": "^1.1.0", "flush-write-stream": "^1.0.0", "from2": "^2.1.0", "parallel-transform": "^1.1.0", "pump": "^3.0.0", "pumpify": "^1.3.3", "stream-each": "^1.1.0", "through2": "^2.0.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/mixin-deep": {"version": "1.3.1", "resolved": "http://registry.npm.taobao.org/mixin-deep/download/mixin-deep-1.3.1.tgz", "integrity": "sha1-pJ5yaNzhoNlpjkUybFYm3zVD0P4=", "deprecated": "Critical bug fixed in v2.0.1, please upgrade to the latest version.", "dev": true, "dependencies": {"for-in": "^1.0.2", "is-extendable": "^1.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/mixin-deep/node_modules/is-extendable": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/is-extendable/download/is-extendable-1.0.1.tgz", "integrity": "sha1-p0cPnkJnM9gb2B4RVSZOOjUHyrQ=", "dev": true, "dependencies": {"is-plain-object": "^2.0.4"}, "engines": {"node": ">=0.10.0"}}, "node_modules/mkdirp": {"version": "0.5.1", "resolved": "http://registry.npm.taobao.org/mkdirp/download/mkdirp-0.5.1.tgz", "integrity": "sha1-MAV0OOrGz3+MR2fzhkjWaX11yQM=", "deprecated": "Legacy versions of mkdirp are no longer supported. Please update to mkdirp 1.x. (Note that the API surface has changed to use Promises in 1.x.)", "dev": true, "dependencies": {"minimist": "0.0.8"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/mkdirp/node_modules/minimist": {"version": "0.0.8", "resolved": "http://registry.npm.taobao.org/minimist/download/minimist-0.0.8.tgz", "integrity": "sha1-hX/Kv8M5fSYluCKCYuhqp6ARsF0=", "dev": true}, "node_modules/moment": {"version": "2.24.0", "resolved": "https://registry.npmjs.org/moment/-/moment-2.24.0.tgz", "integrity": "sha512-bV7f+6l2QigeBBZSM/6yTNq4P2fNpSWj/0e7jQcy87A8e7o2nAfP/34/2ky5Vw4B9S446EtIhodAzkFCcR4dQg==", "engines": {"node": "*"}}, "node_modules/move-concurrently": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/move-concurrently/download/move-concurrently-1.0.1.tgz", "integrity": "sha1-viwAX9oy4LKa8fBdfEszIUxwH5I=", "deprecated": "This package is no longer supported.", "dev": true, "dependencies": {"aproba": "^1.1.1", "copy-concurrently": "^1.0.0", "fs-write-stream-atomic": "^1.0.8", "mkdirp": "^0.5.1", "rimraf": "^2.5.4", "run-queue": "^1.0.3"}}, "node_modules/ms": {"version": "2.1.1", "resolved": "http://registry.npm.taobao.org/ms/download/ms-2.1.1.tgz", "integrity": "sha1-MKWGTrPrsKZvLr5tcnrwagnYbgo="}, "node_modules/multicast-dns": {"version": "6.2.3", "resolved": "http://registry.npm.taobao.org/multicast-dns/download/multicast-dns-6.2.3.tgz", "integrity": "sha1-oOx72QVcQoL3kMPIL04o2zsxsik=", "dev": true, "dependencies": {"dns-packet": "^1.3.1", "thunky": "^1.0.2"}, "bin": {"multicast-dns": "cli.js"}}, "node_modules/multicast-dns-service-types": {"version": "1.1.0", "resolved": "http://registry.npm.taobao.org/multicast-dns-service-types/download/multicast-dns-service-types-1.1.0.tgz", "integrity": "sha1-iZ8R2WhuXgXLkbNdXw5jt3PPyQE=", "dev": true}, "node_modules/nan": {"version": "2.13.2", "resolved": "http://registry.npm.taobao.org/nan/download/nan-2.13.2.tgz", "integrity": "sha1-9R3Hrma6fV1V4ebU2AkugCya7+c=", "dev": true, "optional": true}, "node_modules/nanomatch": {"version": "1.2.13", "resolved": "http://registry.npm.taobao.org/nanomatch/download/nanomatch-1.2.13.tgz", "integrity": "sha1-uHqKpPwN6P5r6IiVs4mD/yZb0Rk=", "dev": true, "dependencies": {"arr-diff": "^4.0.0", "array-unique": "^0.3.2", "define-property": "^2.0.2", "extend-shallow": "^3.0.2", "fragment-cache": "^0.2.1", "is-windows": "^1.0.2", "kind-of": "^6.0.2", "object.pick": "^1.3.0", "regex-not": "^1.0.0", "snapdragon": "^0.8.1", "to-regex": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/negotiator": {"version": "0.6.1", "resolved": "http://registry.npm.taobao.org/negotiator/download/negotiator-0.6.1.tgz", "integrity": "sha1-KzJxhOiZIQEXeyhWP7XnECrNDKk=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/neo-async": {"version": "2.6.0", "resolved": "http://registry.npm.taobao.org/neo-async/download/neo-async-2.6.0.tgz", "integrity": "sha1-udFeTXHGdikIZUtRg+04t1M0CDU=", "dev": true}, "node_modules/nice-try": {"version": "1.0.5", "resolved": "http://registry.npm.taobao.org/nice-try/download/nice-try-1.0.5.tgz", "integrity": "sha1-ozeKdpbOfSI+iPybdkvX7xCJ42Y=", "dev": true}, "node_modules/no-case": {"version": "2.3.2", "resolved": "http://registry.npm.taobao.org/no-case/download/no-case-2.3.2.tgz", "integrity": "sha1-YLgTOWvjmz8SiKTB7V0efSi0ZKw=", "dev": true, "dependencies": {"lower-case": "^1.1.1"}}, "node_modules/node-forge": {"version": "0.7.5", "resolved": "http://registry.npm.taobao.org/node-forge/download/node-forge-0.7.5.tgz", "integrity": "sha1-bBUsNFzhHFL0ZcKr2VfoY5zWdN8=", "dev": true, "engines": {"node": "*"}}, "node_modules/node-ipc": {"version": "9.1.1", "resolved": "http://registry.npm.taobao.org/node-ipc/download/node-ipc-9.1.1.tgz", "integrity": "sha1-TiRe1pOOZRAOWV68XcNLFujdXWk=", "dev": true, "dependencies": {"event-pubsub": "4.3.0", "js-message": "1.0.5", "js-queue": "2.0.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/node-libs-browser": {"version": "2.2.0", "resolved": "http://registry.npm.taobao.org/node-libs-browser/download/node-libs-browser-2.2.0.tgz", "integrity": "sha1-xy9g2dRt4IqUDe27JfP/ovm7qnc=", "dev": true, "dependencies": {"assert": "^1.1.1", "browserify-zlib": "^0.2.0", "buffer": "^4.3.0", "console-browserify": "^1.1.0", "constants-browserify": "^1.0.0", "crypto-browserify": "^3.11.0", "domain-browser": "^1.1.1", "events": "^3.0.0", "https-browserify": "^1.0.0", "os-browserify": "^0.3.0", "path-browserify": "0.0.0", "process": "^0.11.10", "punycode": "^1.2.4", "querystring-es3": "^0.2.0", "readable-stream": "^2.3.3", "stream-browserify": "^2.0.1", "stream-http": "^2.7.2", "string_decoder": "^1.0.0", "timers-browserify": "^2.0.4", "tty-browserify": "0.0.0", "url": "^0.11.0", "util": "^0.11.0", "vm-browserify": "0.0.4"}}, "node_modules/node-libs-browser/node_modules/punycode": {"version": "1.4.1", "resolved": "http://registry.npm.taobao.org/punycode/download/punycode-1.4.1.tgz", "integrity": "sha1-wNWmOycYgArY4esPpSachN1BhF4=", "dev": true}, "node_modules/node-releases": {"version": "1.1.11", "resolved": "http://registry.npm.taobao.org/node-releases/download/node-releases-1.1.11.tgz", "integrity": "sha1-mghBpLDZK31RQe0XnnZPQq0icko=", "dev": true, "dependencies": {"semver": "^5.3.0"}}, "node_modules/normalize-package-data": {"version": "2.5.0", "resolved": "http://registry.npm.taobao.org/normalize-package-data/download/normalize-package-data-2.5.0.tgz", "integrity": "sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=", "dev": true, "dependencies": {"hosted-git-info": "^2.1.4", "resolve": "^1.10.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}}, "node_modules/normalize-path": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/normalize-path/download/normalize-path-3.0.0.tgz", "integrity": "sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/normalize-range": {"version": "0.1.2", "resolved": "http://registry.npm.taobao.org/normalize-range/download/normalize-range-0.1.2.tgz", "integrity": "sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/normalize-url": {"version": "3.3.0", "resolved": "http://registry.npm.taobao.org/normalize-url/download/normalize-url-3.3.0.tgz", "integrity": "sha1-suHE3E98bVd0PfczpPWXjRhlBVk=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/normalize-wheel": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/normalize-wheel/-/normalize-wheel-1.0.1.tgz", "integrity": "sha512-1OnlAPZ3zgrk8B91HyRj+eVv+kS5u+Z0SCsak6Xil/kmgEia50ga7zfkumayonZrImffAxPU/5WcyGhzetHNPA=="}, "node_modules/npm-run-path": {"version": "2.0.2", "resolved": "http://registry.npm.taobao.org/npm-run-path/download/npm-run-path-2.0.2.tgz", "integrity": "sha1-NakjLfo11wZ7TLLd8jV7GHFTbF8=", "dev": true, "dependencies": {"path-key": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/nth-check": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/nth-check/download/nth-check-1.0.2.tgz", "integrity": "sha1-sr0pXDfj3VijvwcAN2Zjuk2c8Fw=", "dev": true, "dependencies": {"boolbase": "~1.0.0"}}, "node_modules/num2fraction": {"version": "1.2.2", "resolved": "http://registry.npm.taobao.org/num2fraction/download/num2fraction-1.2.2.tgz", "integrity": "sha1-b2gragJ6Tp3fpFZM0lidHU5mnt4=", "dev": true}, "node_modules/number-is-nan": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/number-is-nan/download/number-is-nan-1.0.1.tgz", "integrity": "sha1-CXtgK1NCKlIsGvuHkDGDNpQaAR0=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/oauth-sign": {"version": "0.9.0", "resolved": "http://registry.npm.taobao.org/oauth-sign/download/oauth-sign-0.9.0.tgz", "integrity": "sha1-R6ewFrqmi1+g7PPe4IqFxnmsZFU=", "dev": true, "engines": {"node": "*"}}, "node_modules/object-assign": {"version": "4.1.1", "resolved": "http://registry.npm.taobao.org/object-assign/download/object-assign-4.1.1.tgz", "integrity": "sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/object-copy": {"version": "0.1.0", "resolved": "http://registry.npm.taobao.org/object-copy/download/object-copy-0.1.0.tgz", "integrity": "sha1-fn2Fi3gb18mRpBupde04EnVOmYw=", "dev": true, "dependencies": {"copy-descriptor": "^0.1.0", "define-property": "^0.2.5", "kind-of": "^3.0.3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object-copy/node_modules/define-property": {"version": "0.2.5", "resolved": "http://registry.npm.taobao.org/define-property/download/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object-copy/node_modules/kind-of": {"version": "3.2.2", "resolved": "http://registry.npm.taobao.org/kind-of/download/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object-keys": {"version": "1.1.0", "resolved": "http://registry.npm.taobao.org/object-keys/download/object-keys-1.1.0.tgz", "integrity": "sha1-Eb0iNI3S4JagRasG9shbzDQPoDI=", "dev": true, "engines": {"node": ">= 0.4"}}, "node_modules/object-visit": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/object-visit/download/object-visit-1.0.1.tgz", "integrity": "sha1-95xEk68MU3e1n+OdOV5BBC3QRbs=", "dev": true, "dependencies": {"isobject": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object.assign": {"version": "4.1.0", "resolved": "http://registry.npm.taobao.org/object.assign/download/object.assign-4.1.0.tgz", "integrity": "sha1-lovxEA15Vrs8oIbwBvhGs7xACNo=", "dev": true, "dependencies": {"define-properties": "^1.1.2", "function-bind": "^1.1.1", "has-symbols": "^1.0.0", "object-keys": "^1.0.11"}, "engines": {"node": ">= 0.4"}}, "node_modules/object.getownpropertydescriptors": {"version": "2.0.3", "resolved": "http://registry.npm.taobao.org/object.getownpropertydescriptors/download/object.getownpropertydescriptors-2.0.3.tgz", "integrity": "sha1-h1jIRvW0B62rDyNuCYbxSwUcqhY=", "dev": true, "dependencies": {"define-properties": "^1.1.2", "es-abstract": "^1.5.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/object.pick": {"version": "1.3.0", "resolved": "http://registry.npm.taobao.org/object.pick/download/object.pick-1.3.0.tgz", "integrity": "sha1-h6EKxMFpS9Lhy/U1kaZhQftd10c=", "dev": true, "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/object.values": {"version": "1.1.0", "resolved": "http://registry.npm.taobao.org/object.values/download/object.values-1.1.0.tgz", "integrity": "sha1-v2gQ712j5TJXkOqqK+IT6oRiTak=", "dev": true, "dependencies": {"define-properties": "^1.1.3", "es-abstract": "^1.12.0", "function-bind": "^1.1.1", "has": "^1.0.3"}, "engines": {"node": ">= 0.4"}}, "node_modules/obuf": {"version": "1.1.2", "resolved": "http://registry.npm.taobao.org/obuf/download/obuf-1.1.2.tgz", "integrity": "sha1-Cb6jND1BhZ69RGKS0RydTbYZCE4=", "dev": true}, "node_modules/on-finished": {"version": "2.3.0", "resolved": "http://registry.npm.taobao.org/on-finished/download/on-finished-2.3.0.tgz", "integrity": "sha1-IPEzZIGwg811M3mSoWlxqi2QaUc=", "dev": true, "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/on-headers": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/on-headers/download/on-headers-1.0.2.tgz", "integrity": "sha1-dysK5qqlJcOZ5Imt+tkMQD6zwo8=", "dev": true, "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "resolved": "http://registry.npm.taobao.org/once/download/once-1.4.0.tgz", "integrity": "sha1-WDsap3WWHUsROsF9nFC6753Xa9E=", "dev": true, "dependencies": {"wrappy": "1"}}, "node_modules/onetime": {"version": "2.0.1", "resolved": "http://registry.npm.taobao.org/onetime/download/onetime-2.0.1.tgz", "integrity": "sha1-BnQoIw/WdEOyeUsiu6UotoZ5YtQ=", "dev": true, "dependencies": {"mimic-fn": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/opener": {"version": "1.5.1", "resolved": "http://registry.npm.taobao.org/opener/download/opener-1.5.1.tgz", "integrity": "sha1-bS8Od/GgrwAyrKcWwsH7uOfoq+0=", "dev": true, "bin": {"opener": "bin/opener-bin.js"}}, "node_modules/opn": {"version": "5.5.0", "resolved": "http://registry.npm.taobao.org/opn/download/opn-5.5.0.tgz?cache=0&other_urls=http%3A%2F%2Fregistry.npm.taobao.org%2Fopn%2Fdownload%2Fopn-5.5.0.tgz", "integrity": "sha1-/HFk+rVtI1kExRw7J9pnWMo7m/w=", "dev": true, "dependencies": {"is-wsl": "^1.1.0"}, "engines": {"node": ">=4"}}, "node_modules/ora": {"version": "3.2.0", "resolved": "http://registry.npm.taobao.org/ora/download/ora-3.2.0.tgz", "integrity": "sha1-Z+mKfhH38KyV3qqvEbsE3j0J5IE=", "dev": true, "dependencies": {"chalk": "^2.4.2", "cli-cursor": "^2.1.0", "cli-spinners": "^2.0.0", "log-symbols": "^2.2.0", "strip-ansi": "^5.0.0", "wcwidth": "^1.0.1"}, "engines": {"node": ">=6"}}, "node_modules/original": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/original/download/original-1.0.2.tgz", "integrity": "sha1-5EKmHP/hxf0gpl8yYcJmY7MD8l8=", "dev": true, "dependencies": {"url-parse": "^1.4.3"}}, "node_modules/os-browserify": {"version": "0.3.0", "resolved": "http://registry.npm.taobao.org/os-browserify/download/os-browserify-0.3.0.tgz", "integrity": "sha1-hUNzx/XCMVkU/Jv8a9gjj92h7Cc=", "dev": true}, "node_modules/os-locale": {"version": "3.1.0", "resolved": "http://registry.npm.taobao.org/os-locale/download/os-locale-3.1.0.tgz", "integrity": "sha1-qAKm7hfyTBBIOrmTVxnO9O0Wvxo=", "dev": true, "dependencies": {"execa": "^1.0.0", "lcid": "^2.0.0", "mem": "^4.0.0"}, "engines": {"node": ">=6"}}, "node_modules/p-defer": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/p-defer/download/p-defer-1.0.0.tgz", "integrity": "sha1-n26xgvbJqozXQwBKfU+WsZaw+ww=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/p-finally": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/p-finally/download/p-finally-1.0.0.tgz", "integrity": "sha1-P7z7FbiZpEEjs0ttzBi3JDNqLK4=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/p-is-promise": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/p-is-promise/download/p-is-promise-2.0.0.tgz", "integrity": "sha1-dVTj1XIQmofh8/U/an2F0bGU9MU=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/p-limit": {"version": "2.2.0", "resolved": "http://registry.npm.taobao.org/p-limit/download/p-limit-2.2.0.tgz", "integrity": "sha1-QXyZQeYCepq8ulCS3SkE4lW1+8I=", "dev": true, "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/p-locate": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/p-locate/download/p-locate-3.0.0.tgz", "integrity": "sha1-Mi1poFwCZLJZl9n0DNiokasAZKQ=", "dev": true, "dependencies": {"p-limit": "^2.0.0"}, "engines": {"node": ">=6"}}, "node_modules/p-map": {"version": "1.2.0", "resolved": "http://registry.npm.taobao.org/p-map/download/p-map-1.2.0.tgz", "integrity": "sha1-5OlPMR6rvIYzoeeZCBZfyiYkG2s=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/p-try": {"version": "2.1.0", "resolved": "http://registry.npm.taobao.org/p-try/download/p-try-2.1.0.tgz", "integrity": "sha1-waDxAw6X3gGLsscYkp0q9ZRj5QU=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/pako": {"version": "1.0.10", "resolved": "http://registry.npm.taobao.org/pako/download/pako-1.0.10.tgz", "integrity": "sha1-Qyi621CGpCaqkPVBl31JVdpclzI=", "dev": true}, "node_modules/parallel-transform": {"version": "1.1.0", "resolved": "http://registry.npm.taobao.org/parallel-transform/download/parallel-transform-1.1.0.tgz", "integrity": "sha1-1BDwZbBdojCB/NEPKIVMKb2jOwY=", "dev": true, "dependencies": {"cyclist": "~0.2.2", "inherits": "^2.0.3", "readable-stream": "^2.1.5"}}, "node_modules/param-case": {"version": "2.1.1", "resolved": "http://registry.npm.taobao.org/param-case/download/param-case-2.1.1.tgz", "integrity": "sha1-35T9jPZTHs915r75oIWPvHK+Ikc=", "dev": true, "dependencies": {"no-case": "^2.2.0"}}, "node_modules/parse-asn1": {"version": "5.1.4", "resolved": "http://registry.npm.taobao.org/parse-asn1/download/parse-asn1-5.1.4.tgz", "integrity": "sha1-N/Zij4I/vesic7TVQENKIvPvH8w=", "dev": true, "dependencies": {"asn1.js": "^4.0.0", "browserify-aes": "^1.0.0", "create-hash": "^1.1.0", "evp_bytestokey": "^1.0.0", "pbkdf2": "^3.0.3", "safe-buffer": "^5.1.1"}}, "node_modules/parse-json": {"version": "4.0.0", "resolved": "http://registry.npm.taobao.org/parse-json/download/parse-json-4.0.0.tgz", "integrity": "sha1-vjX1Qlvh9/bHRxhPmKeIy5lHfuA=", "dev": true, "dependencies": {"error-ex": "^1.3.1", "json-parse-better-errors": "^1.0.1"}, "engines": {"node": ">=4"}}, "node_modules/parseurl": {"version": "1.3.2", "resolved": "http://registry.npm.taobao.org/parseurl/download/parseurl-1.3.2.tgz", "integrity": "sha1-/CidTtiZMRlGDBViUyYs3I3mW/M=", "dev": true, "engines": {"node": ">= 0.8"}}, "node_modules/pascalcase": {"version": "0.1.1", "resolved": "http://registry.npm.taobao.org/pascalcase/download/pascalcase-0.1.1.tgz", "integrity": "sha1-s2PlXoAGym/iF4TS2yK9FdeRfxQ=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/path-browserify": {"version": "0.0.0", "resolved": "http://registry.npm.taobao.org/path-browserify/download/path-browserify-0.0.0.tgz", "integrity": "sha1-oLhwcpquIUAFt9UDLsLLuw+0RRo=", "dev": true}, "node_modules/path-dirname": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/path-dirname/download/path-dirname-1.0.2.tgz", "integrity": "sha1-zDPSTVJeCZpTiMAzbG4yuRYGCeA=", "dev": true}, "node_modules/path-exists": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/path-exists/download/path-exists-3.0.0.tgz", "integrity": "sha1-zg6+ql94yxiSXqfYENe1mwEP1RU=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/path-is-absolute/download/path-is-absolute-1.0.1.tgz", "integrity": "sha1-F0uSaHNVNP+8es5r9TpanhtcX18=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/path-is-inside": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/path-is-inside/download/path-is-inside-1.0.2.tgz", "integrity": "sha1-NlQX3t5EQw0cEa9hAn+s8HS9/FM=", "dev": true}, "node_modules/path-key": {"version": "2.0.1", "resolved": "http://registry.npm.taobao.org/path-key/download/path-key-2.0.1.tgz", "integrity": "sha1-QRyttXTFoUDTpLGRDUDYDMn0C0A=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/path-parse": {"version": "1.0.6", "resolved": "http://registry.npm.taobao.org/path-parse/download/path-parse-1.0.6.tgz", "integrity": "sha1-1i27VnlAXXLEc37FhgDp3c8G0kw=", "dev": true}, "node_modules/path-to-regexp": {"version": "0.1.7", "resolved": "http://registry.npm.taobao.org/path-to-regexp/download/path-to-regexp-0.1.7.tgz", "integrity": "sha1-32BBeABfUi8V60SQ5yR6G/qmf4w=", "dev": true}, "node_modules/path-type": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/path-type/download/path-type-3.0.0.tgz", "integrity": "sha1-zvMdyOCho7sNEFwM2Xzzv0f0428=", "dev": true, "dependencies": {"pify": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/path-type/node_modules/pify": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/pify/download/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/pbkdf2": {"version": "3.0.17", "resolved": "http://registry.npm.taobao.org/pbkdf2/download/pbkdf2-3.0.17.tgz", "integrity": "sha1-l2wgZTBhexTrsyEUI597CTNuk6Y=", "dev": true, "dependencies": {"create-hash": "^1.1.2", "create-hmac": "^1.1.4", "ripemd160": "^2.0.1", "safe-buffer": "^5.0.1", "sha.js": "^2.4.8"}, "engines": {"node": ">=0.12"}}, "node_modules/performance-now": {"version": "2.1.0", "resolved": "http://registry.npm.taobao.org/performance-now/download/performance-now-2.1.0.tgz", "integrity": "sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=", "devOptional": true}, "node_modules/pify": {"version": "4.0.1", "resolved": "http://registry.npm.taobao.org/pify/download/pify-4.0.1.tgz", "integrity": "sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/pinkie": {"version": "2.0.4", "resolved": "http://registry.npm.taobao.org/pinkie/download/pinkie-2.0.4.tgz", "integrity": "sha1-clVrgM+g1IqXToDnckjoDtT3+HA=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/pinkie-promise": {"version": "2.0.1", "resolved": "http://registry.npm.taobao.org/pinkie-promise/download/pinkie-promise-2.0.1.tgz", "integrity": "sha1-ITXW36ejWMBprJsXh3YogihFD/o=", "dev": true, "dependencies": {"pinkie": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/pkg-dir": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/pkg-dir/download/pkg-dir-3.0.0.tgz", "integrity": "sha1-J0kCDyOe2ZCIGx9xIQ1R62UjvqM=", "dev": true, "dependencies": {"find-up": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/portfinder": {"version": "1.0.20", "resolved": "http://registry.npm.taobao.org/portfinder/download/portfinder-1.0.20.tgz", "integrity": "sha1-vqaGMuVLLhOrewxHdem0G/Jw5Eo=", "dev": true, "dependencies": {"async": "^1.5.2", "debug": "^2.2.0", "mkdirp": "0.5.x"}, "engines": {"node": ">= 0.12.0"}}, "node_modules/portfinder/node_modules/debug": {"version": "2.6.9", "resolved": "http://registry.npm.taobao.org/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "dependencies": {"ms": "2.0.0"}}, "node_modules/portfinder/node_modules/ms": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}, "node_modules/posix-character-classes": {"version": "0.1.1", "resolved": "http://registry.npm.taobao.org/posix-character-classes/download/posix-character-classes-0.1.1.tgz", "integrity": "sha1-AerA/jta9xoqbAL+q7jB/vfgDqs=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/postcss": {"version": "7.0.14", "resolved": "http://registry.npm.taobao.org/postcss/download/postcss-7.0.14.tgz", "integrity": "sha1-RSftaxyg2CxTzl7BogQcI0a71uU=", "dev": true, "dependencies": {"chalk": "^2.4.2", "source-map": "^0.6.1", "supports-color": "^6.1.0"}, "engines": {"node": ">=6.0.0"}}, "node_modules/postcss-calc": {"version": "7.0.1", "resolved": "http://registry.npm.taobao.org/postcss-calc/download/postcss-calc-7.0.1.tgz", "integrity": "sha1-Ntd7qwI7Dsu5eJ2E3LI8SUEUVDY=", "dev": true, "dependencies": {"css-unit-converter": "^1.1.1", "postcss": "^7.0.5", "postcss-selector-parser": "^5.0.0-rc.4", "postcss-value-parser": "^3.3.1"}}, "node_modules/postcss-colormin": {"version": "4.0.3", "resolved": "http://registry.npm.taobao.org/postcss-colormin/download/postcss-colormin-4.0.3.tgz", "integrity": "sha1-rgYLzpPteUrHEmTwgTLVUJVr04E=", "dev": true, "dependencies": {"browserslist": "^4.0.0", "color": "^3.0.0", "has": "^1.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/postcss-convert-values": {"version": "4.0.1", "resolved": "http://registry.npm.taobao.org/postcss-convert-values/download/postcss-convert-values-4.0.1.tgz", "integrity": "sha1-yjgT7U2g+BL51DcDWE5Enr4Ymn8=", "dev": true, "dependencies": {"postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/postcss-discard-comments": {"version": "4.0.2", "resolved": "http://registry.npm.taobao.org/postcss-discard-comments/download/postcss-discard-comments-4.0.2.tgz", "integrity": "sha1-H7q9LCRr/2qq15l7KwkY9NevQDM=", "dev": true, "dependencies": {"postcss": "^7.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/postcss-discard-duplicates": {"version": "4.0.2", "resolved": "http://registry.npm.taobao.org/postcss-discard-duplicates/download/postcss-discard-duplicates-4.0.2.tgz", "integrity": "sha1-P+EzzTyCKC5VD8myORdqkge3hOs=", "dev": true, "dependencies": {"postcss": "^7.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/postcss-discard-empty": {"version": "4.0.1", "resolved": "http://registry.npm.taobao.org/postcss-discard-empty/download/postcss-discard-empty-4.0.1.tgz", "integrity": "sha1-yMlR6fc+2UKAGUWERKAq2Qu592U=", "dev": true, "dependencies": {"postcss": "^7.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/postcss-discard-overridden": {"version": "4.0.1", "resolved": "http://registry.npm.taobao.org/postcss-discard-overridden/download/postcss-discard-overridden-4.0.1.tgz", "integrity": "sha1-ZSrvipZybwKfXj4AFG7npOdV/1c=", "dev": true, "dependencies": {"postcss": "^7.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/postcss-load-config": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/postcss-load-config/download/postcss-load-config-2.0.0.tgz", "integrity": "sha1-8TEt2/WRLNdHF3CDxe96GdYu5IQ=", "dev": true, "dependencies": {"cosmiconfig": "^4.0.0", "import-cwd": "^2.0.0"}, "engines": {"node": ">= 4"}}, "node_modules/postcss-load-config/node_modules/cosmiconfig": {"version": "4.0.0", "resolved": "http://registry.npm.taobao.org/cosmiconfig/download/cosmiconfig-4.0.0.tgz", "integrity": "sha1-dgORVJWAu9LfHlYrwXexPCkJctw=", "dev": true, "dependencies": {"is-directory": "^0.3.1", "js-yaml": "^3.9.0", "parse-json": "^4.0.0", "require-from-string": "^2.0.1"}, "engines": {"node": ">=4"}}, "node_modules/postcss-loader": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/postcss-loader/download/postcss-loader-3.0.0.tgz", "integrity": "sha1-a5eUPkfHLYRfqeA/Jzdz1OjdbC0=", "dev": true, "dependencies": {"loader-utils": "^1.1.0", "postcss": "^7.0.0", "postcss-load-config": "^2.0.0", "schema-utils": "^1.0.0"}, "engines": {"node": ">= 6"}}, "node_modules/postcss-loader/node_modules/schema-utils": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/schema-utils/download/schema-utils-1.0.0.tgz", "integrity": "sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A=", "dev": true, "dependencies": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}, "engines": {"node": ">= 4"}}, "node_modules/postcss-merge-longhand": {"version": "4.0.11", "resolved": "http://registry.npm.taobao.org/postcss-merge-longhand/download/postcss-merge-longhand-4.0.11.tgz", "integrity": "sha1-YvSaE+Sg7gTnuY9CuxYGLKJUniQ=", "dev": true, "dependencies": {"css-color-names": "0.0.4", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0", "stylehacks": "^4.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/postcss-merge-rules": {"version": "4.0.3", "resolved": "http://registry.npm.taobao.org/postcss-merge-rules/download/postcss-merge-rules-4.0.3.tgz", "integrity": "sha1-NivqT/Wh+Y5AdacTxsslrv75plA=", "dev": true, "dependencies": {"browserslist": "^4.0.0", "caniuse-api": "^3.0.0", "cssnano-util-same-parent": "^4.0.0", "postcss": "^7.0.0", "postcss-selector-parser": "^3.0.0", "vendors": "^1.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/postcss-merge-rules/node_modules/postcss-selector-parser": {"version": "3.1.1", "resolved": "http://registry.npm.taobao.org/postcss-selector-parser/download/postcss-selector-parser-3.1.1.tgz", "integrity": "sha1-T4dfSvsMllc9XPTXQBGu4lCn6GU=", "dev": true, "dependencies": {"dot-prop": "^4.1.1", "indexes-of": "^1.0.1", "uniq": "^1.0.1"}, "engines": {"node": ">=4"}}, "node_modules/postcss-minify-font-values": {"version": "4.0.2", "resolved": "http://registry.npm.taobao.org/postcss-minify-font-values/download/postcss-minify-font-values-4.0.2.tgz", "integrity": "sha1-zUw0TM5HQ0P6xdgiBqssvLiv1aY=", "dev": true, "dependencies": {"postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/postcss-minify-gradients": {"version": "4.0.2", "resolved": "http://registry.npm.taobao.org/postcss-minify-gradients/download/postcss-minify-gradients-4.0.2.tgz", "integrity": "sha1-k7KcL/UJnFNe7NpWxKpuZlpmNHE=", "dev": true, "dependencies": {"cssnano-util-get-arguments": "^4.0.0", "is-color-stop": "^1.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/postcss-minify-params": {"version": "4.0.2", "resolved": "http://registry.npm.taobao.org/postcss-minify-params/download/postcss-minify-params-4.0.2.tgz", "integrity": "sha1-a5zvAwwR41Jh+V9hjJADbWgNuHQ=", "dev": true, "dependencies": {"alphanum-sort": "^1.0.0", "browserslist": "^4.0.0", "cssnano-util-get-arguments": "^4.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0", "uniqs": "^2.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/postcss-minify-selectors": {"version": "4.0.2", "resolved": "http://registry.npm.taobao.org/postcss-minify-selectors/download/postcss-minify-selectors-4.0.2.tgz", "integrity": "sha1-4uXrQL/uUA0M2SQ1APX46kJi+9g=", "dev": true, "dependencies": {"alphanum-sort": "^1.0.0", "has": "^1.0.0", "postcss": "^7.0.0", "postcss-selector-parser": "^3.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/postcss-minify-selectors/node_modules/postcss-selector-parser": {"version": "3.1.1", "resolved": "http://registry.npm.taobao.org/postcss-selector-parser/download/postcss-selector-parser-3.1.1.tgz", "integrity": "sha1-T4dfSvsMllc9XPTXQBGu4lCn6GU=", "dev": true, "dependencies": {"dot-prop": "^4.1.1", "indexes-of": "^1.0.1", "uniq": "^1.0.1"}, "engines": {"node": ">=4"}}, "node_modules/postcss-modules-extract-imports": {"version": "1.2.1", "resolved": "http://registry.npm.taobao.org/postcss-modules-extract-imports/download/postcss-modules-extract-imports-1.2.1.tgz", "integrity": "sha1-3IfjQUjsfqtfeR981YSYMzdbdBo=", "dev": true, "dependencies": {"postcss": "^6.0.1"}}, "node_modules/postcss-modules-extract-imports/node_modules/postcss": {"version": "6.0.23", "resolved": "http://registry.npm.taobao.org/postcss/download/postcss-6.0.23.tgz", "integrity": "sha1-YcgswyisYOZ3ZF+XkFTrmLwOMyQ=", "dev": true, "dependencies": {"chalk": "^2.4.1", "source-map": "^0.6.1", "supports-color": "^5.4.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/postcss-modules-extract-imports/node_modules/source-map": {"version": "0.6.1", "resolved": "http://registry.npm.taobao.org/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/postcss-modules-local-by-default": {"version": "1.2.0", "resolved": "http://registry.npm.taobao.org/postcss-modules-local-by-default/download/postcss-modules-local-by-default-1.2.0.tgz", "integrity": "sha1-99gMOYxaOT+nlkRmvRlQCn1hwGk=", "dev": true, "dependencies": {"css-selector-tokenizer": "^0.7.0", "postcss": "^6.0.1"}}, "node_modules/postcss-modules-local-by-default/node_modules/postcss": {"version": "6.0.23", "resolved": "http://registry.npm.taobao.org/postcss/download/postcss-6.0.23.tgz", "integrity": "sha1-YcgswyisYOZ3ZF+XkFTrmLwOMyQ=", "dev": true, "dependencies": {"chalk": "^2.4.1", "source-map": "^0.6.1", "supports-color": "^5.4.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/postcss-modules-local-by-default/node_modules/source-map": {"version": "0.6.1", "resolved": "http://registry.npm.taobao.org/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/postcss-modules-scope": {"version": "1.1.0", "resolved": "http://registry.npm.taobao.org/postcss-modules-scope/download/postcss-modules-scope-1.1.0.tgz", "integrity": "sha1-1upkmUx5+XtipytCb75gVqGUu5A=", "dev": true, "dependencies": {"css-selector-tokenizer": "^0.7.0", "postcss": "^6.0.1"}}, "node_modules/postcss-modules-scope/node_modules/postcss": {"version": "6.0.23", "resolved": "http://registry.npm.taobao.org/postcss/download/postcss-6.0.23.tgz", "integrity": "sha1-YcgswyisYOZ3ZF+XkFTrmLwOMyQ=", "dev": true, "dependencies": {"chalk": "^2.4.1", "source-map": "^0.6.1", "supports-color": "^5.4.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/postcss-modules-scope/node_modules/source-map": {"version": "0.6.1", "resolved": "http://registry.npm.taobao.org/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/postcss-modules-values": {"version": "1.3.0", "resolved": "http://registry.npm.taobao.org/postcss-modules-values/download/postcss-modules-values-1.3.0.tgz", "integrity": "sha1-7P+p1+GSUYOJ9CrQ6D9yrsRW6iA=", "dev": true, "dependencies": {"icss-replace-symbols": "^1.1.0", "postcss": "^6.0.1"}}, "node_modules/postcss-modules-values/node_modules/postcss": {"version": "6.0.23", "resolved": "http://registry.npm.taobao.org/postcss/download/postcss-6.0.23.tgz", "integrity": "sha1-YcgswyisYOZ3ZF+XkFTrmLwOMyQ=", "dev": true, "dependencies": {"chalk": "^2.4.1", "source-map": "^0.6.1", "supports-color": "^5.4.0"}, "engines": {"node": ">=4.0.0"}}, "node_modules/postcss-modules-values/node_modules/source-map": {"version": "0.6.1", "resolved": "http://registry.npm.taobao.org/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/postcss-normalize-charset": {"version": "4.0.1", "resolved": "http://registry.npm.taobao.org/postcss-normalize-charset/download/postcss-normalize-charset-4.0.1.tgz", "integrity": "sha1-izWt067oOhNrBHHg1ZvlilAoXdQ=", "dev": true, "dependencies": {"postcss": "^7.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/postcss-normalize-display-values": {"version": "4.0.2", "resolved": "http://registry.npm.taobao.org/postcss-normalize-display-values/download/postcss-normalize-display-values-4.0.2.tgz", "integrity": "sha1-Db4EpM6QY9RmftK+R2u4MMglk1o=", "dev": true, "dependencies": {"cssnano-util-get-match": "^4.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/postcss-normalize-positions": {"version": "4.0.2", "resolved": "http://registry.npm.taobao.org/postcss-normalize-positions/download/postcss-normalize-positions-4.0.2.tgz", "integrity": "sha1-BfdX+E8mBDc3g2ipH4ky1LECkX8=", "dev": true, "dependencies": {"cssnano-util-get-arguments": "^4.0.0", "has": "^1.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/postcss-normalize-repeat-style": {"version": "4.0.2", "resolved": "http://registry.npm.taobao.org/postcss-normalize-repeat-style/download/postcss-normalize-repeat-style-4.0.2.tgz", "integrity": "sha1-xOu8KJ85kaAo1EdRy90RkYsXkQw=", "dev": true, "dependencies": {"cssnano-util-get-arguments": "^4.0.0", "cssnano-util-get-match": "^4.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/postcss-normalize-string": {"version": "4.0.2", "resolved": "http://registry.npm.taobao.org/postcss-normalize-string/download/postcss-normalize-string-4.0.2.tgz", "integrity": "sha1-zUTECrB6DHo23F6Zqs4eyk7CaQw=", "dev": true, "dependencies": {"has": "^1.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/postcss-normalize-timing-functions": {"version": "4.0.2", "resolved": "http://registry.npm.taobao.org/postcss-normalize-timing-functions/download/postcss-normalize-timing-functions-4.0.2.tgz", "integrity": "sha1-jgCcoqOUnNr4rSPmtquZy159KNk=", "dev": true, "dependencies": {"cssnano-util-get-match": "^4.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/postcss-normalize-unicode": {"version": "4.0.1", "resolved": "http://registry.npm.taobao.org/postcss-normalize-unicode/download/postcss-normalize-unicode-4.0.1.tgz", "integrity": "sha1-hBvUj9zzAZrUuqdJOj02O1KuHPs=", "dev": true, "dependencies": {"browserslist": "^4.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/postcss-normalize-url": {"version": "4.0.1", "resolved": "http://registry.npm.taobao.org/postcss-normalize-url/download/postcss-normalize-url-4.0.1.tgz", "integrity": "sha1-EOQ3+GvHx+WPe5ZS7YeNqqlfquE=", "dev": true, "dependencies": {"is-absolute-url": "^2.0.0", "normalize-url": "^3.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/postcss-normalize-whitespace": {"version": "4.0.2", "resolved": "http://registry.npm.taobao.org/postcss-normalize-whitespace/download/postcss-normalize-whitespace-4.0.2.tgz", "integrity": "sha1-vx1AcP5Pzqh9E0joJdjMDF+qfYI=", "dev": true, "dependencies": {"postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/postcss-ordered-values": {"version": "4.1.2", "resolved": "http://registry.npm.taobao.org/postcss-ordered-values/download/postcss-ordered-values-4.1.2.tgz", "integrity": "sha1-DPdcgg7H1cTSgBiVWeC1ceusDu4=", "dev": true, "dependencies": {"cssnano-util-get-arguments": "^4.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/postcss-reduce-initial": {"version": "4.0.3", "resolved": "http://registry.npm.taobao.org/postcss-reduce-initial/download/postcss-reduce-initial-4.0.3.tgz", "integrity": "sha1-f9QuvqXpyBRgljniwuhK4nC6SN8=", "dev": true, "dependencies": {"browserslist": "^4.0.0", "caniuse-api": "^3.0.0", "has": "^1.0.0", "postcss": "^7.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/postcss-reduce-transforms": {"version": "4.0.2", "resolved": "http://registry.npm.taobao.org/postcss-reduce-transforms/download/postcss-reduce-transforms-4.0.2.tgz", "integrity": "sha1-F++kBerMbge+NBSlyi0QdGgdTik=", "dev": true, "dependencies": {"cssnano-util-get-match": "^4.0.0", "has": "^1.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/postcss-selector-parser": {"version": "5.0.0", "resolved": "http://registry.npm.taobao.org/postcss-selector-parser/download/postcss-selector-parser-5.0.0.tgz", "integrity": "sha1-JJBENWaXsztk8aj3yAki3d7nGVw=", "dev": true, "dependencies": {"cssesc": "^2.0.0", "indexes-of": "^1.0.1", "uniq": "^1.0.1"}, "engines": {"node": ">=4"}}, "node_modules/postcss-svgo": {"version": "4.0.2", "resolved": "http://registry.npm.taobao.org/postcss-svgo/download/postcss-svgo-4.0.2.tgz", "integrity": "sha1-F7mXvHEbMzurFDqu07jT1uPTglg=", "dev": true, "dependencies": {"is-svg": "^3.0.0", "postcss": "^7.0.0", "postcss-value-parser": "^3.0.0", "svgo": "^1.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/postcss-unique-selectors": {"version": "4.0.1", "resolved": "http://registry.npm.taobao.org/postcss-unique-selectors/download/postcss-unique-selectors-4.0.1.tgz", "integrity": "sha1-lEaRHzKJv9ZMbWgPBzwDsfnuS6w=", "dev": true, "dependencies": {"alphanum-sort": "^1.0.0", "postcss": "^7.0.0", "uniqs": "^2.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/postcss-value-parser": {"version": "3.3.1", "resolved": "http://registry.npm.taobao.org/postcss-value-parser/download/postcss-value-parser-3.3.1.tgz", "integrity": "sha1-n/giVH4okyE88cMO+lGsX9G6goE=", "dev": true}, "node_modules/postcss/node_modules/source-map": {"version": "0.6.1", "resolved": "http://registry.npm.taobao.org/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/postcss/node_modules/supports-color": {"version": "6.1.0", "resolved": "http://registry.npm.taobao.org/supports-color/download/supports-color-6.1.0.tgz", "integrity": "sha1-B2Srxpxj1ayELdSGfo0CXogN+PM=", "dev": true, "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/prettier": {"version": "1.16.3", "resolved": "http://registry.npm.taobao.org/prettier/download/prettier-1.16.3.tgz", "integrity": "sha1-jGIWhFO63vcC80tFtu6JlXSmpl0=", "dev": true, "bin": {"prettier": "bin-prettier.js"}, "engines": {"node": ">=4"}}, "node_modules/pretty-error": {"version": "2.1.1", "resolved": "http://registry.npm.taobao.org/pretty-error/download/pretty-error-2.1.1.tgz?cache=0&other_urls=http%3A%2F%2Fregistry.npm.taobao.org%2Fpretty-error%2Fdownload%2Fpretty-error-2.1.1.tgz", "integrity": "sha1-X0+HyPkeWuPzuoerTPXgOxoX8aM=", "dev": true, "dependencies": {"renderkid": "^2.0.1", "utila": "~0.4"}}, "node_modules/private": {"version": "0.1.8", "resolved": "http://registry.npm.taobao.org/private/download/private-0.1.8.tgz", "integrity": "sha1-I4Hts2ifelPWUxkAYPz4ItLzaP8=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/process": {"version": "0.11.10", "resolved": "http://registry.npm.taobao.org/process/download/process-0.11.10.tgz", "integrity": "sha1-czIwDoQBYb2j5podHZGn1LwW8YI=", "dev": true, "engines": {"node": ">= 0.6.0"}}, "node_modules/process-nextick-args": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/process-nextick-args/download/process-nextick-args-2.0.0.tgz", "integrity": "sha1-o31zL0JxtKsa0HDTVQjoKQeI/6o=", "dev": true}, "node_modules/promise-inflight": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/promise-inflight/download/promise-inflight-1.0.1.tgz", "integrity": "sha1-mEcocL8igTL8vdhoEputEsPAKeM=", "dev": true}, "node_modules/proxy-addr": {"version": "2.0.4", "resolved": "http://registry.npm.taobao.org/proxy-addr/download/proxy-addr-2.0.4.tgz", "integrity": "sha1-7PxzO/Iv+Mb0B/onUye5q2fki5M=", "dev": true, "dependencies": {"forwarded": "~0.1.2", "ipaddr.js": "1.8.0"}, "engines": {"node": ">= 0.10"}}, "node_modules/prr": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/prr/download/prr-1.0.1.tgz", "integrity": "sha1-0/wRS6BplaRexok/SEzrHXj19HY=", "dev": true}, "node_modules/pseudomap": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/pseudomap/download/pseudomap-1.0.2.tgz", "integrity": "sha1-8FKijacOYYkX7wqKw0wa5aaChrM=", "dev": true}, "node_modules/psl": {"version": "1.1.31", "resolved": "http://registry.npm.taobao.org/psl/download/psl-1.1.31.tgz", "integrity": "sha1-6aqG0BAbWxBcvpOsa3hM1UcnYYQ=", "dev": true}, "node_modules/public-encrypt": {"version": "4.0.3", "resolved": "http://registry.npm.taobao.org/public-encrypt/download/public-encrypt-4.0.3.tgz", "integrity": "sha1-T8ydd6B+SLp1J+fL4N4z0HATMeA=", "dev": true, "dependencies": {"bn.js": "^4.1.0", "browserify-rsa": "^4.0.0", "create-hash": "^1.1.0", "parse-asn1": "^5.0.0", "randombytes": "^2.0.1", "safe-buffer": "^5.1.2"}}, "node_modules/pump": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/pump/download/pump-3.0.0.tgz", "integrity": "sha1-tKIRaBW94vTh6mAjVOjHVWUQemQ=", "dev": true, "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/pumpify": {"version": "1.5.1", "resolved": "http://registry.npm.taobao.org/pumpify/download/pumpify-1.5.1.tgz", "integrity": "sha1-NlE74karJ1cLGjdKXOJ4v9dDcM4=", "dev": true, "dependencies": {"duplexify": "^3.6.0", "inherits": "^2.0.3", "pump": "^2.0.0"}}, "node_modules/pumpify/node_modules/pump": {"version": "2.0.1", "resolved": "http://registry.npm.taobao.org/pump/download/pump-2.0.1.tgz", "integrity": "sha1-Ejma3W5M91Jtlzy8i1zi4pCLOQk=", "dev": true, "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "node_modules/punycode": {"version": "2.1.1", "resolved": "http://registry.npm.taobao.org/punycode/download/punycode-2.1.1.tgz", "integrity": "sha1-tYsBCsQMIsVldhbI0sLALHv0eew=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/q": {"version": "1.5.1", "resolved": "http://registry.npm.taobao.org/q/download/q-1.5.1.tgz", "integrity": "sha1-fjL3W0E4EpHQRhHxvxQQmsAGUdc=", "deprecated": "You or someone you depend on is using Q, the JavaScript Promise library that gave JavaScript developers strong feelings about promises. They can almost certainly migrate to the native JavaScript promise now. Thank you literally everyone for joining me in this bet against the odds. Be excellent to each other.\n\n(For a CapTP with native promises, see @endo/eventual-send and @endo/captp)", "dev": true, "engines": {"node": ">=0.6.0", "teleport": ">=0.2.0"}}, "node_modules/qs": {"version": "6.5.2", "resolved": "http://registry.npm.taobao.org/qs/download/qs-6.5.2.tgz?cache=0&other_urls=http%3A%2F%2Fregistry.npm.taobao.org%2Fqs%2Fdownload%2Fqs-6.5.2.tgz", "integrity": "sha1-yzroBuh0BERYTvFUzo7pjUA/PjY=", "dev": true, "engines": {"node": ">=0.6"}}, "node_modules/querystring": {"version": "0.2.0", "resolved": "http://registry.npm.taobao.org/querystring/download/querystring-0.2.0.tgz", "integrity": "sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA=", "deprecated": "The querystring API is considered Legacy. new code should use the URLSearchParams API instead.", "dev": true, "engines": {"node": ">=0.4.x"}}, "node_modules/querystring-es3": {"version": "0.2.1", "resolved": "http://registry.npm.taobao.org/querystring-es3/download/querystring-es3-0.2.1.tgz", "integrity": "sha1-nsYfeQSYdXB9aUFFlv2Qek1xHnM=", "dev": true, "engines": {"node": ">=0.4.x"}}, "node_modules/querystringify": {"version": "2.1.1", "resolved": "http://registry.npm.taobao.org/querystringify/download/querystringify-2.1.1.tgz", "integrity": "sha1-YOWl/WSn+L+k0qsu1v30yFutFU4=", "dev": true}, "node_modules/raf": {"version": "3.4.1", "resolved": "https://registry.npmjs.org/raf/-/raf-3.4.1.tgz", "integrity": "sha512-Sq4CW4QhwOHE8ucn6J34MqtZCeWFP2aQSmrlroYgqAV1PjStIhJXxYuTgUIfkEk7zTLjmIjLmU5q+fbD1NnOJA==", "optional": true, "dependencies": {"performance-now": "^2.1.0"}}, "node_modules/randombytes": {"version": "2.1.0", "resolved": "http://registry.npm.taobao.org/randombytes/download/randombytes-2.1.0.tgz", "integrity": "sha1-32+ENy8CcNxlzfYpE0mrekc9Tyo=", "dev": true, "dependencies": {"safe-buffer": "^5.1.0"}}, "node_modules/randomfill": {"version": "1.0.4", "resolved": "http://registry.npm.taobao.org/randomfill/download/randomfill-1.0.4.tgz", "integrity": "sha1-ySGW/IarQr6YPxvzF3giSTHWFFg=", "dev": true, "dependencies": {"randombytes": "^2.0.5", "safe-buffer": "^5.1.0"}}, "node_modules/range-parser": {"version": "1.2.0", "resolved": "http://registry.npm.taobao.org/range-parser/download/range-parser-1.2.0.tgz", "integrity": "sha1-9JvmtIeJTdxA3MlKMi9hEJLgDV4=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "2.3.3", "resolved": "http://registry.npm.taobao.org/raw-body/download/raw-body-2.3.3.tgz", "integrity": "sha1-GzJOzmtXBuFThVvBFIxlu39uoMM=", "dev": true, "dependencies": {"bytes": "3.0.0", "http-errors": "1.6.3", "iconv-lite": "0.4.23", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/read-pkg": {"version": "4.0.1", "resolved": "http://registry.npm.taobao.org/read-pkg/download/read-pkg-4.0.1.tgz", "integrity": "sha1-ljYlN48+HE1IyFhytabsfV0JMjc=", "dev": true, "dependencies": {"normalize-package-data": "^2.3.2", "parse-json": "^4.0.0", "pify": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/read-pkg/node_modules/pify": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/pify/download/pify-3.0.0.tgz", "integrity": "sha1-5aSs0sEB/fPZpNB/DbxNtJ3SgXY=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/readable-stream": {"version": "2.3.6", "resolved": "http://registry.npm.taobao.org/readable-stream/download/readable-stream-2.3.6.tgz", "integrity": "sha1-sRwn2IuP8fvgcGQ8+UsMea4bCq8=", "dev": true, "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/readdirp": {"version": "2.2.1", "resolved": "http://registry.npm.taobao.org/readdirp/download/readdirp-2.2.1.tgz", "integrity": "sha1-DodiKjMlqjPokihcr4tOhGUppSU=", "dev": true, "dependencies": {"graceful-fs": "^4.1.11", "micromatch": "^3.1.10", "readable-stream": "^2.0.2"}, "engines": {"node": ">=0.10"}}, "node_modules/regenerate": {"version": "1.4.0", "resolved": "http://registry.npm.taobao.org/regenerate/download/regenerate-1.4.0.tgz", "integrity": "sha1-SoVuxLVuQHfFV1icroXnpMiGmhE=", "dev": true}, "node_modules/regenerate-unicode-properties": {"version": "8.0.2", "resolved": "http://registry.npm.taobao.org/regenerate-unicode-properties/download/regenerate-unicode-properties-8.0.2.tgz", "integrity": "sha1-ezj6opYlI3bTY1WM+9qQyc5wlmI=", "dev": true, "dependencies": {"regenerate": "^1.4.0"}, "engines": {"node": ">=4"}}, "node_modules/regenerator-runtime": {"version": "0.13.11", "resolved": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz", "integrity": "sha512-kY1AZVr2Ra+t+piVaJ4gxaFaReZVH40AKNo7UCX6W+dEwBo/2oZJzqfuN1qLq1oL45o56cPaTXELwrTh8Fpggg==", "devOptional": true}, "node_modules/regenerator-transform": {"version": "0.13.4", "resolved": "http://registry.npm.taobao.org/regenerator-transform/download/regenerator-transform-0.13.4.tgz", "integrity": "sha1-GPZ2PPE4LGnDbfdsbOEizGlChPs=", "dev": true, "dependencies": {"private": "^0.1.6"}}, "node_modules/regex-not": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/regex-not/download/regex-not-1.0.2.tgz", "integrity": "sha1-H07OJ+ALC2XgJHpoEOaoXYOldSw=", "dev": true, "dependencies": {"extend-shallow": "^3.0.2", "safe-regex": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/regexp-tree": {"version": "0.1.5", "resolved": "http://registry.npm.taobao.org/regexp-tree/download/regexp-tree-0.1.5.tgz", "integrity": "sha1-fNcfyhcZjQS0F279eXE/KZgAk5c=", "dev": true, "bin": {"regexp-tree": "bin/regexp-tree"}}, "node_modules/regexpu-core": {"version": "4.5.4", "resolved": "http://registry.npm.taobao.org/regexpu-core/download/regexpu-core-4.5.4.tgz", "integrity": "sha1-CA2dAiiaqH/hZnpPUTa8mKauuq4=", "dev": true, "dependencies": {"regenerate": "^1.4.0", "regenerate-unicode-properties": "^8.0.2", "regjsgen": "^0.5.0", "regjsparser": "^0.6.0", "unicode-match-property-ecmascript": "^1.0.4", "unicode-match-property-value-ecmascript": "^1.1.0"}, "engines": {"node": ">=4"}}, "node_modules/regjsgen": {"version": "0.5.0", "resolved": "http://registry.npm.taobao.org/regjsgen/download/regjsgen-0.5.0.tgz", "integrity": "sha1-p2NNwI+JIJwgSa3aNSVxH7lyZd0=", "dev": true}, "node_modules/regjsparser": {"version": "0.6.0", "resolved": "http://registry.npm.taobao.org/regjsparser/download/regjsparser-0.6.0.tgz", "integrity": "sha1-8eaui32iuulsmTmbhozWyTOiupw=", "dev": true, "dependencies": {"jsesc": "~0.5.0"}, "bin": {"regjsparser": "bin/parser"}}, "node_modules/regjsparser/node_modules/jsesc": {"version": "0.5.0", "resolved": "http://registry.npm.taobao.org/jsesc/download/jsesc-0.5.0.tgz", "integrity": "sha1-597mbjXW/Bb3EP6R1c9p9w8IkR0=", "dev": true, "bin": {"jsesc": "bin/jsesc"}}, "node_modules/relateurl": {"version": "0.2.7", "resolved": "http://registry.npm.taobao.org/relateurl/download/relateurl-0.2.7.tgz", "integrity": "sha1-VNvzd+UUQKypCkzSdGANP/LYiKk=", "dev": true, "engines": {"node": ">= 0.10"}}, "node_modules/remove-trailing-separator": {"version": "1.1.0", "resolved": "http://registry.npm.taobao.org/remove-trailing-separator/download/remove-trailing-separator-1.1.0.tgz", "integrity": "sha1-wkvOKig62tW8P1jg1IJJuSN52O8=", "dev": true}, "node_modules/renderkid": {"version": "2.0.3", "resolved": "http://registry.npm.taobao.org/renderkid/download/renderkid-2.0.3.tgz", "integrity": "sha1-OAF5wv9a4TZcUivy/Pz/AcW3QUk=", "dev": true, "dependencies": {"css-select": "^1.1.0", "dom-converter": "^0.2", "htmlparser2": "^3.3.0", "strip-ansi": "^3.0.0", "utila": "^0.4.0"}}, "node_modules/renderkid/node_modules/ansi-regex": {"version": "2.1.1", "resolved": "http://registry.npm.taobao.org/ansi-regex/download/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/renderkid/node_modules/css-select": {"version": "1.2.0", "resolved": "http://registry.npm.taobao.org/css-select/download/css-select-1.2.0.tgz", "integrity": "sha1-KzoRBTnFNV8c2NMUYj6HCxIeyFg=", "dev": true, "dependencies": {"boolbase": "~1.0.0", "css-what": "2.1", "domutils": "1.5.1", "nth-check": "~1.0.1"}}, "node_modules/renderkid/node_modules/domutils": {"version": "1.5.1", "resolved": "http://registry.npm.taobao.org/domutils/download/domutils-1.5.1.tgz", "integrity": "sha1-3NhIiib1Y9YQeeSMn3t+Mjc2gs8=", "dev": true, "dependencies": {"dom-serializer": "0", "domelementtype": "1"}}, "node_modules/renderkid/node_modules/strip-ansi": {"version": "3.0.1", "resolved": "http://registry.npm.taobao.org/strip-ansi/download/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "dev": true, "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/repeat-element": {"version": "1.1.3", "resolved": "http://registry.npm.taobao.org/repeat-element/download/repeat-element-1.1.3.tgz", "integrity": "sha1-eC4NglwMWjuzlzH4Tv7mt0Lmsc4=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/repeat-string": {"version": "1.6.1", "resolved": "http://registry.npm.taobao.org/repeat-string/download/repeat-string-1.6.1.tgz", "integrity": "sha1-jcrkcOHIirwtYA//Sndihtp15jc=", "dev": true, "engines": {"node": ">=0.10"}}, "node_modules/request": {"version": "2.88.0", "resolved": "http://registry.npm.taobao.org/request/download/request-2.88.0.tgz", "integrity": "sha1-nC/KT301tZLv5Xx/ClXoEFIST+8=", "deprecated": "request has been deprecated, see https://github.com/request/request/issues/3142", "dev": true, "dependencies": {"aws-sign2": "~0.7.0", "aws4": "^1.8.0", "caseless": "~0.12.0", "combined-stream": "~1.0.6", "extend": "~3.0.2", "forever-agent": "~0.6.1", "form-data": "~2.3.2", "har-validator": "~5.1.0", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.19", "oauth-sign": "~0.9.0", "performance-now": "^2.1.0", "qs": "~6.5.2", "safe-buffer": "^5.1.2", "tough-cookie": "~2.4.3", "tunnel-agent": "^0.6.0", "uuid": "^3.3.2"}, "engines": {"node": ">= 4"}}, "node_modules/request-promise-core": {"version": "1.1.2", "resolved": "http://registry.npm.taobao.org/request-promise-core/download/request-promise-core-1.1.2.tgz", "integrity": "sha1-M59qq6vK/bMceZ/xWHADNjAdM0Y=", "dev": true, "dependencies": {"lodash": "^4.17.11"}, "engines": {"node": ">=0.10.0"}, "peerDependencies": {"request": "^2.34"}}, "node_modules/request-promise-native": {"version": "1.0.7", "resolved": "http://registry.npm.taobao.org/request-promise-native/download/request-promise-native-1.0.7.tgz", "integrity": "sha1-pJhopiS96lBp8SUdCoNuDYmqLFk=", "deprecated": "request-promise-native has been deprecated because it extends the now deprecated request package, see https://github.com/request/request/issues/3142", "dev": true, "dependencies": {"request-promise-core": "1.1.2", "stealthy-require": "^1.1.1", "tough-cookie": "^2.3.3"}, "engines": {"node": ">=0.12.0"}, "peerDependencies": {"request": "^2.34"}}, "node_modules/require-directory": {"version": "2.1.1", "resolved": "http://registry.npm.taobao.org/require-directory/download/require-directory-2.1.1.tgz", "integrity": "sha1-jGStX9MNqxyXbiNE/+f3kqam30I=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/require-from-string": {"version": "2.0.2", "resolved": "http://registry.npm.taobao.org/require-from-string/download/require-from-string-2.0.2.tgz", "integrity": "sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/require-main-filename": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/require-main-filename/download/require-main-filename-1.0.1.tgz", "integrity": "sha1-l/cXtp1IeE9fUmpsWqj/3aBVpNE=", "dev": true}, "node_modules/requires-port": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/requires-port/download/requires-port-1.0.0.tgz", "integrity": "sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=", "dev": true}, "node_modules/resize-observer-polyfill": {"version": "1.5.1", "resolved": "https://registry.npmjs.org/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz", "integrity": "sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg=="}, "node_modules/resolve": {"version": "1.10.0", "resolved": "http://registry.npm.taobao.org/resolve/download/resolve-1.10.0.tgz", "integrity": "sha1-O9qur0XMB/N1ZW39LlTtCBCxAbo=", "dev": true, "dependencies": {"path-parse": "^1.0.6"}}, "node_modules/resolve-cwd": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/resolve-cwd/download/resolve-cwd-2.0.0.tgz", "integrity": "sha1-AKn3OHVW4nA46uIyyqNypqWbZlo=", "dev": true, "dependencies": {"resolve-from": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/resolve-from": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/resolve-from/download/resolve-from-3.0.0.tgz", "integrity": "sha1-six699nWiBvItuZTM17rywoYh0g=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/resolve-url": {"version": "0.2.1", "resolved": "http://registry.npm.taobao.org/resolve-url/download/resolve-url-0.2.1.tgz", "integrity": "sha1-LGN/53yJOv0qZj/iGqkIAGjiBSo=", "deprecated": "https://github.com/lydell/resolve-url#deprecated", "dev": true}, "node_modules/restore-cursor": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/restore-cursor/download/restore-cursor-2.0.0.tgz", "integrity": "sha1-n37ih/gv0ybU/RYpI9YhKe7g368=", "dev": true, "dependencies": {"onetime": "^2.0.0", "signal-exit": "^3.0.2"}, "engines": {"node": ">=4"}}, "node_modules/ret": {"version": "0.1.15", "resolved": "http://registry.npm.taobao.org/ret/download/ret-0.1.15.tgz", "integrity": "sha1-uKSCXVvbH8P29Twrwz+BOIaBx7w=", "dev": true, "engines": {"node": ">=0.12"}}, "node_modules/rgb-regex": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/rgb-regex/download/rgb-regex-1.0.1.tgz", "integrity": "sha1-wODWiC3w4jviVKR16O3UGRX+rrE=", "dev": true}, "node_modules/rgba-regex": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/rgba-regex/download/rgba-regex-1.0.0.tgz", "integrity": "sha1-QzdOLiyglosO8VI0YLfXMP8i7rM=", "dev": true}, "node_modules/rgbcolor": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/rgbcolor/-/rgbcolor-1.0.1.tgz", "integrity": "sha512-9aZLIrhRaD97sgVhtJOW6ckOEh6/GnvQtdVNfdZ6s67+3/XwLS9lBcQYzEEhYVeUowN7pRzMLsyGhK2i/xvWbw==", "optional": true, "engines": {"node": ">= 0.8.15"}}, "node_modules/rimraf": {"version": "2.6.3", "resolved": "http://registry.npm.taobao.org/rimraf/download/rimraf-2.6.3.tgz", "integrity": "sha1-stEE/g2Psnz54KHNqCYt04M8bKs=", "deprecated": "Rimraf versions prior to v4 are no longer supported", "dev": true, "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}}, "node_modules/ripemd160": {"version": "2.0.2", "resolved": "http://registry.npm.taobao.org/ripemd160/download/ripemd160-2.0.2.tgz", "integrity": "sha1-ocGm9iR1FXe6XQeRTLyShQWFiQw=", "dev": true, "dependencies": {"hash-base": "^3.0.0", "inherits": "^2.0.1"}}, "node_modules/run-queue": {"version": "1.0.3", "resolved": "http://registry.npm.taobao.org/run-queue/download/run-queue-1.0.3.tgz", "integrity": "sha1-6Eg5bwV9Ij8kOGkkYY4laUFh7Ec=", "dev": true, "dependencies": {"aproba": "^1.1.1"}}, "node_modules/safe-buffer": {"version": "5.1.2", "resolved": "http://registry.npm.taobao.org/safe-buffer/download/safe-buffer-5.1.2.tgz", "integrity": "sha1-mR7GnSluAxN0fVm9/St0XDX4go0=", "dev": true}, "node_modules/safe-regex": {"version": "1.1.0", "resolved": "http://registry.npm.taobao.org/safe-regex/download/safe-regex-1.1.0.tgz", "integrity": "sha1-QKNmnzsHfR6UPURinhV91IAjvy4=", "dev": true, "dependencies": {"ret": "~0.1.10"}}, "node_modules/safer-buffer": {"version": "2.1.2", "resolved": "http://registry.npm.taobao.org/safer-buffer/download/safer-buffer-2.1.2.tgz", "integrity": "sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=", "dev": true}, "node_modules/sax": {"version": "1.2.4", "resolved": "http://registry.npm.taobao.org/sax/download/sax-1.2.4.tgz", "integrity": "sha1-KBYjTiN4vdxOU1T6tcqold9xANk=", "dev": true}, "node_modules/schema-utils": {"version": "0.4.7", "resolved": "http://registry.npm.taobao.org/schema-utils/download/schema-utils-0.4.7.tgz", "integrity": "sha1-unT1l9K+LqiAExdG7hfQoJPGgYc=", "dev": true, "dependencies": {"ajv": "^6.1.0", "ajv-keywords": "^3.1.0"}, "engines": {"node": ">= 4"}}, "node_modules/select-hose": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/select-hose/download/select-hose-2.0.0.tgz", "integrity": "sha1-Yl2GWPhlr0Psliv8N2o3NZpJlMo=", "dev": true}, "node_modules/selfsigned": {"version": "1.10.4", "resolved": "http://registry.npm.taobao.org/selfsigned/download/selfsigned-1.10.4.tgz", "integrity": "sha1-zdfsz8pO12NdR6CL8tXTB0CS4s0=", "dev": true, "dependencies": {"node-forge": "0.7.5"}}, "node_modules/semver": {"version": "5.6.0", "resolved": "http://registry.npm.taobao.org/semver/download/semver-5.6.0.tgz?cache=0&other_urls=http%3A%2F%2Fregistry.npm.taobao.org%2Fsemver%2Fdownload%2Fsemver-5.6.0.tgz", "integrity": "sha1-fnQlb7qknHWqfHogXMInmcrIAAQ=", "dev": true, "bin": {"semver": "bin/semver"}}, "node_modules/send": {"version": "0.16.2", "resolved": "http://registry.npm.taobao.org/send/download/send-0.16.2.tgz", "integrity": "sha1-bsyh4PjBVtFBWXVZhI32RzCmu8E=", "dev": true, "dependencies": {"debug": "2.6.9", "depd": "~1.1.2", "destroy": "~1.0.4", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "~1.6.2", "mime": "1.4.1", "ms": "2.0.0", "on-finished": "~2.3.0", "range-parser": "~1.2.0", "statuses": "~1.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/send/node_modules/debug": {"version": "2.6.9", "resolved": "http://registry.npm.taobao.org/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "dependencies": {"ms": "2.0.0"}}, "node_modules/send/node_modules/mime": {"version": "1.4.1", "resolved": "http://registry.npm.taobao.org/mime/download/mime-1.4.1.tgz", "integrity": "sha1-Eh+evEnjdm8xGnbh+hyAA8SwOqY=", "dev": true, "bin": {"mime": "cli.js"}}, "node_modules/send/node_modules/ms": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}, "node_modules/serialize-javascript": {"version": "1.6.1", "resolved": "http://registry.npm.taobao.org/serialize-javascript/download/serialize-javascript-1.6.1.tgz", "integrity": "sha1-TR9pfsSUKahHym9EKip1USbE2Hk=", "dev": true}, "node_modules/serve-index": {"version": "1.9.1", "resolved": "http://registry.npm.taobao.org/serve-index/download/serve-index-1.9.1.tgz", "integrity": "sha1-03aNabHn2C5c4FD/9bRTvqEqkjk=", "dev": true, "dependencies": {"accepts": "~1.3.4", "batch": "0.6.1", "debug": "2.6.9", "escape-html": "~1.0.3", "http-errors": "~1.6.2", "mime-types": "~2.1.17", "parseurl": "~1.3.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/serve-index/node_modules/debug": {"version": "2.6.9", "resolved": "http://registry.npm.taobao.org/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "dependencies": {"ms": "2.0.0"}}, "node_modules/serve-index/node_modules/ms": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}, "node_modules/serve-static": {"version": "1.13.2", "resolved": "http://registry.npm.taobao.org/serve-static/download/serve-static-1.13.2.tgz", "integrity": "sha1-CV6Ecv1bRiN9tQzkhqQ/S4bGzsE=", "dev": true, "dependencies": {"encodeurl": "~1.0.2", "escape-html": "~1.0.3", "parseurl": "~1.3.2", "send": "0.16.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/set-blocking": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/set-blocking/download/set-blocking-2.0.0.tgz", "integrity": "sha1-BF+XgtARrppoA93TgrJDkrPYkPc=", "dev": true}, "node_modules/set-value": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/set-value/download/set-value-2.0.0.tgz", "integrity": "sha1-ca5KiPD+77v1LR6mBPP7MV67YnQ=", "deprecated": "Critical bug fixed in v3.0.1, please upgrade to the latest version.", "dev": true, "dependencies": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.3", "split-string": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/set-value/node_modules/extend-shallow": {"version": "2.0.1", "resolved": "http://registry.npm.taobao.org/extend-shallow/download/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/setimmediate": {"version": "1.0.5", "resolved": "http://registry.npm.taobao.org/setimmediate/download/setimmediate-1.0.5.tgz", "integrity": "sha1-KQy7Iy4waULX1+qbg3Mqt4VvgoU=", "dev": true}, "node_modules/setprototypeof": {"version": "1.1.0", "resolved": "http://registry.npm.taobao.org/setprototypeof/download/setprototypeof-1.1.0.tgz", "integrity": "sha1-0L2FU2iHtv58DYGMuWLZ2RxU5lY=", "dev": true}, "node_modules/sha.js": {"version": "2.4.11", "resolved": "http://registry.npm.taobao.org/sha.js/download/sha.js-2.4.11.tgz", "integrity": "sha1-N6XPC4HsvGlD3hCbopYNGyZYSuc=", "dev": true, "dependencies": {"inherits": "^2.0.1", "safe-buffer": "^5.0.1"}, "bin": {"sha.js": "bin.js"}}, "node_modules/shebang-command": {"version": "1.2.0", "resolved": "http://registry.npm.taobao.org/shebang-command/download/shebang-command-1.2.0.tgz", "integrity": "sha1-RKrGW2lbAzmJaMOfNj/uXer98eo=", "dev": true, "dependencies": {"shebang-regex": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/shebang-regex": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/shebang-regex/download/shebang-regex-1.0.0.tgz", "integrity": "sha1-2kL0l0DAtC2yypcoVxyxkMmO/qM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/shell-quote": {"version": "1.6.1", "resolved": "http://registry.npm.taobao.org/shell-quote/download/shell-quote-1.6.1.tgz", "integrity": "sha1-9HgZSczkAmlxJ0MOo7PFR29IF2c=", "dev": true, "dependencies": {"array-filter": "~0.0.0", "array-map": "~0.0.0", "array-reduce": "~0.0.0", "jsonify": "~0.0.0"}}, "node_modules/signal-exit": {"version": "3.0.2", "resolved": "http://registry.npm.taobao.org/signal-exit/download/signal-exit-3.0.2.tgz", "integrity": "sha1-tf3AjxKH6hF4Yo5BXiUTK3NkbG0=", "dev": true}, "node_modules/simple-swizzle": {"version": "0.2.2", "resolved": "http://registry.npm.taobao.org/simple-swizzle/download/simple-swizzle-0.2.2.tgz", "integrity": "sha1-pNprY1/8zMoz9w0Xy5JZLeleVXo=", "dev": true, "dependencies": {"is-arrayish": "^0.3.1"}}, "node_modules/simple-swizzle/node_modules/is-arrayish": {"version": "0.3.2", "resolved": "http://registry.npm.taobao.org/is-arrayish/download/is-arrayish-0.3.2.tgz", "integrity": "sha1-RXSirlb3qyBolvtDHq7tBm/fjwM=", "dev": true}, "node_modules/slash": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/slash/download/slash-2.0.0.tgz", "integrity": "sha1-3lUoUaF1nfOo8gZTVEL17E3eq0Q=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/snapdragon": {"version": "0.8.2", "resolved": "http://registry.npm.taobao.org/snapdragon/download/snapdragon-0.8.2.tgz", "integrity": "sha1-ZJIufFZbDhQgS6GqfWlkJ40lGC0=", "dev": true, "dependencies": {"base": "^0.11.1", "debug": "^2.2.0", "define-property": "^0.2.5", "extend-shallow": "^2.0.1", "map-cache": "^0.2.2", "source-map": "^0.5.6", "source-map-resolve": "^0.5.0", "use": "^3.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-node": {"version": "2.1.1", "resolved": "http://registry.npm.taobao.org/snapdragon-node/download/snapdragon-node-2.1.1.tgz", "integrity": "sha1-bBdfhv8UvbByRWPo88GwIaKGhTs=", "dev": true, "dependencies": {"define-property": "^1.0.0", "isobject": "^3.0.0", "snapdragon-util": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-node/node_modules/define-property": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/define-property/download/define-property-1.0.0.tgz", "integrity": "sha1-dp66rz9KY6rTr56NMEybvnm/sOY=", "dev": true, "dependencies": {"is-descriptor": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-node/node_modules/is-accessor-descriptor": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/is-accessor-descriptor/download/is-accessor-descriptor-1.0.0.tgz", "integrity": "sha1-FpwvbT3x+ZJhgHI2XJsOofaHhlY=", "deprecated": "Please upgrade to v1.0.1", "dev": true, "dependencies": {"kind-of": "^6.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-node/node_modules/is-data-descriptor": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/is-data-descriptor/download/is-data-descriptor-1.0.0.tgz", "integrity": "sha1-2Eh2Mh0Oet0DmQQGq7u9NrqSaMc=", "deprecated": "Please upgrade to v1.0.1", "dev": true, "dependencies": {"kind-of": "^6.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-node/node_modules/is-descriptor": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/is-descriptor/download/is-descriptor-1.0.2.tgz", "integrity": "sha1-OxWXRqZmBLBPjIFSS6NlxfFNhuw=", "dev": true, "dependencies": {"is-accessor-descriptor": "^1.0.0", "is-data-descriptor": "^1.0.0", "kind-of": "^6.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-util": {"version": "3.0.1", "resolved": "http://registry.npm.taobao.org/snapdragon-util/download/snapdragon-util-3.0.1.tgz", "integrity": "sha1-+VZHlIbyrNeXAGk/b3uAXkWrVuI=", "dev": true, "dependencies": {"kind-of": "^3.2.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon-util/node_modules/kind-of": {"version": "3.2.2", "resolved": "http://registry.npm.taobao.org/kind-of/download/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/debug": {"version": "2.6.9", "resolved": "http://registry.npm.taobao.org/debug/download/debug-2.6.9.tgz", "integrity": "sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=", "dev": true, "dependencies": {"ms": "2.0.0"}}, "node_modules/snapdragon/node_modules/define-property": {"version": "0.2.5", "resolved": "http://registry.npm.taobao.org/define-property/download/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/extend-shallow": {"version": "2.0.1", "resolved": "http://registry.npm.taobao.org/extend-shallow/download/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/snapdragon/node_modules/ms": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/ms/download/ms-2.0.0.tgz", "integrity": "sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=", "dev": true}, "node_modules/sockjs": {"version": "0.3.19", "resolved": "http://registry.npm.taobao.org/sockjs/download/sockjs-0.3.19.tgz", "integrity": "sha1-2Xa76ACve9IK4IWY1YI5NQiZPA0=", "dev": true, "dependencies": {"faye-websocket": "^0.10.0", "uuid": "^3.0.1"}}, "node_modules/sockjs-client": {"version": "1.3.0", "resolved": "http://registry.npm.taobao.org/sockjs-client/download/sockjs-client-1.3.0.tgz", "integrity": "sha1-EvydbLZj2lc509xftuhofalcsXc=", "dev": true, "dependencies": {"debug": "^3.2.5", "eventsource": "^1.0.7", "faye-websocket": "~0.11.1", "inherits": "^2.0.3", "json3": "^3.3.2", "url-parse": "^1.4.3"}}, "node_modules/sockjs-client/node_modules/debug": {"version": "3.2.6", "resolved": "http://registry.npm.taobao.org/debug/download/debug-3.2.6.tgz", "integrity": "sha1-6D0X3hbYp++3cX7b5fsQE17uYps=", "deprecated": "Debug versions >=3.2.0 <3.2.7 || >=4 <4.3.1 have a low-severity ReDos regression when used in a Node.js environment. It is recommended you upgrade to 3.2.7 or 4.3.1. (https://github.com/visionmedia/debug/issues/797)", "dev": true, "dependencies": {"ms": "^2.1.1"}}, "node_modules/sockjs-client/node_modules/faye-websocket": {"version": "0.11.1", "resolved": "http://registry.npm.taobao.org/faye-websocket/download/faye-websocket-0.11.1.tgz", "integrity": "sha1-8O/hjE9W5PQK/H4Gxxn9XuYYjzg=", "dev": true, "dependencies": {"websocket-driver": ">=0.5.1"}, "engines": {"node": ">=0.8.0"}}, "node_modules/source-list-map": {"version": "2.0.1", "resolved": "http://registry.npm.taobao.org/source-list-map/download/source-list-map-2.0.1.tgz", "integrity": "sha1-OZO9hzv8SEecyp6jpUeDXHwVSzQ=", "dev": true}, "node_modules/source-map": {"version": "0.5.7", "resolved": "http://registry.npm.taobao.org/source-map/download/source-map-0.5.7.tgz", "integrity": "sha1-igOdLRAh0i0eoUyA2OpGi6LvP8w=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-resolve": {"version": "0.5.2", "resolved": "http://registry.npm.taobao.org/source-map-resolve/download/source-map-resolve-0.5.2.tgz", "integrity": "sha1-cuLMNAlVQ+Q7LGKyxMENSpBU8lk=", "deprecated": "See https://github.com/lydell/source-map-resolve#deprecated", "dev": true, "dependencies": {"atob": "^2.1.1", "decode-uri-component": "^0.2.0", "resolve-url": "^0.2.1", "source-map-url": "^0.4.0", "urix": "^0.1.0"}}, "node_modules/source-map-support": {"version": "0.5.11", "resolved": "http://registry.npm.taobao.org/source-map-support/download/source-map-support-0.5.11.tgz", "integrity": "sha1-76ws4IADVdAmMmoMoj4WKurJpOI=", "dev": true, "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/source-map-support/node_modules/source-map": {"version": "0.6.1", "resolved": "http://registry.npm.taobao.org/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-url": {"version": "0.4.0", "resolved": "http://registry.npm.taobao.org/source-map-url/download/source-map-url-0.4.0.tgz", "integrity": "sha1-PpNdfd1zYxuXZZlW1VEo6HtQhKM=", "deprecated": "See https://github.com/lydell/source-map-url#deprecated", "dev": true}, "node_modules/spdx-correct": {"version": "3.1.0", "resolved": "http://registry.npm.taobao.org/spdx-correct/download/spdx-correct-3.1.0.tgz", "integrity": "sha1-+4PlBERSaPFUsHTiGMh8ADzTHfQ=", "dev": true, "dependencies": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/spdx-exceptions": {"version": "2.2.0", "resolved": "http://registry.npm.taobao.org/spdx-exceptions/download/spdx-exceptions-2.2.0.tgz", "integrity": "sha1-LqRQrudPKom/uUUZwH/Nb0EyKXc=", "dev": true}, "node_modules/spdx-expression-parse": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/spdx-expression-parse/download/spdx-expression-parse-3.0.0.tgz", "integrity": "sha1-meEZt6XaAOBUkcn6M4t5BII7QdA=", "dev": true, "dependencies": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/spdx-license-ids": {"version": "3.0.3", "resolved": "http://registry.npm.taobao.org/spdx-license-ids/download/spdx-license-ids-3.0.3.tgz", "integrity": "sha1-gcDOjyFHR1YUi7tfO/wPNr8V124=", "dev": true}, "node_modules/spdy": {"version": "4.0.0", "resolved": "http://registry.npm.taobao.org/spdy/download/spdy-4.0.0.tgz", "integrity": "sha1-gfIitadDoymqEs6mo5DmDpthPFI=", "dev": true, "dependencies": {"debug": "^4.1.0", "handle-thing": "^2.0.0", "http-deceiver": "^1.2.7", "select-hose": "^2.0.0", "spdy-transport": "^3.0.0"}, "engines": {"node": ">=6.0.0", "npm": ">=6.0.0"}}, "node_modules/spdy-transport": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/spdy-transport/download/spdy-transport-3.0.0.tgz", "integrity": "sha1-ANSGOmQArXXfkzYaFghgXl3NzzE=", "dev": true, "dependencies": {"debug": "^4.1.0", "detect-node": "^2.0.4", "hpack.js": "^2.1.6", "obuf": "^1.1.2", "readable-stream": "^3.0.6", "wbuf": "^1.7.3"}}, "node_modules/spdy-transport/node_modules/readable-stream": {"version": "3.2.0", "resolved": "http://registry.npm.taobao.org/readable-stream/download/readable-stream-3.2.0.tgz", "integrity": "sha1-3hfyKYZMEgqfVpRXVuTzLEBFJF0=", "dev": true, "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/split-string": {"version": "3.1.0", "resolved": "http://registry.npm.taobao.org/split-string/download/split-string-3.1.0.tgz", "integrity": "sha1-fLCd2jqGWFcFxks5pkZgOGguj+I=", "dev": true, "dependencies": {"extend-shallow": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/sprintf-js": {"version": "1.0.3", "resolved": "http://registry.npm.taobao.org/sprintf-js/download/sprintf-js-1.0.3.tgz", "integrity": "sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=", "dev": true}, "node_modules/sshpk": {"version": "1.16.1", "resolved": "http://registry.npm.taobao.org/sshpk/download/sshpk-1.16.1.tgz", "integrity": "sha1-+2YcC+8ps520B2nuOfpwCT1vaHc=", "dev": true, "dependencies": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "engines": {"node": ">=0.10.0"}}, "node_modules/ssri": {"version": "6.0.1", "resolved": "http://registry.npm.taobao.org/ssri/download/ssri-6.0.1.tgz", "integrity": "sha1-KjxBso3UW2K2Nnbst0ABJlrp7dg=", "dev": true, "dependencies": {"figgy-pudding": "^3.5.1"}}, "node_modules/stable": {"version": "0.1.8", "resolved": "http://registry.npm.taobao.org/stable/download/stable-0.1.8.tgz", "integrity": "sha1-g26zyDgv4pNv6vVEYxAXzn1Ho88=", "deprecated": "Modern JS already guarantees Array#sort() is a stable sort, so this library is deprecated. See the compatibility table on MDN: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort#browser_compatibility", "dev": true}, "node_modules/stackblur-canvas": {"version": "2.7.0", "resolved": "https://registry.npmjs.org/stackblur-canvas/-/stackblur-canvas-2.7.0.tgz", "integrity": "sha512-yf7OENo23AGJhBriGx0QivY5JP6Y1HbrrDI6WLt6C5auYZXlQrheoY8hD4ibekFKz1HOfE48Ww8kMWMnJD/zcQ==", "optional": true, "engines": {"node": ">=0.1.14"}}, "node_modules/stackframe": {"version": "1.0.4", "resolved": "http://registry.npm.taobao.org/stackframe/download/stackframe-1.0.4.tgz", "integrity": "sha1-NXskqZL5Qny6a1RdlqFO0svKGHs=", "dev": true}, "node_modules/static-extend": {"version": "0.1.2", "resolved": "http://registry.npm.taobao.org/static-extend/download/static-extend-0.1.2.tgz", "integrity": "sha1-YICcOcv/VTNyJv1eC1IPNB8ftcY=", "dev": true, "dependencies": {"define-property": "^0.2.5", "object-copy": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/static-extend/node_modules/define-property": {"version": "0.2.5", "resolved": "http://registry.npm.taobao.org/define-property/download/define-property-0.2.5.tgz", "integrity": "sha1-w1se+RjsPJkPmlvFe+BKrOxcgRY=", "dev": true, "dependencies": {"is-descriptor": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/statuses": {"version": "1.4.0", "resolved": "http://registry.npm.taobao.org/statuses/download/statuses-1.4.0.tgz", "integrity": "sha1-u3PURtonlhBu/MG2AaJT1sRr0Ic=", "dev": true, "engines": {"node": ">= 0.6"}}, "node_modules/stealthy-require": {"version": "1.1.1", "resolved": "http://registry.npm.taobao.org/stealthy-require/download/stealthy-require-1.1.1.tgz", "integrity": "sha1-NbCYdbT/SfJqd35QmzCQoyJr8ks=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/stream-browserify": {"version": "2.0.2", "resolved": "http://registry.npm.taobao.org/stream-browserify/download/stream-browserify-2.0.2.tgz", "integrity": "sha1-h1IdOKRKp+6RzhzSpH3wy0ndZgs=", "dev": true, "dependencies": {"inherits": "~2.0.1", "readable-stream": "^2.0.2"}}, "node_modules/stream-each": {"version": "1.2.3", "resolved": "http://registry.npm.taobao.org/stream-each/download/stream-each-1.2.3.tgz", "integrity": "sha1-6+J6DDibBPvMIzZClS4Qcxr6m64=", "dev": true, "dependencies": {"end-of-stream": "^1.1.0", "stream-shift": "^1.0.0"}}, "node_modules/stream-http": {"version": "2.8.3", "resolved": "http://registry.npm.taobao.org/stream-http/download/stream-http-2.8.3.tgz", "integrity": "sha1-stJCRpKIpaJ+xP6JM6z2I95lFPw=", "dev": true, "dependencies": {"builtin-status-codes": "^3.0.0", "inherits": "^2.0.1", "readable-stream": "^2.3.6", "to-arraybuffer": "^1.0.0", "xtend": "^4.0.0"}}, "node_modules/stream-shift": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/stream-shift/download/stream-shift-1.0.0.tgz", "integrity": "sha1-1cdSgl5TZ+eG944Y5EXqIjoVWVI=", "dev": true}, "node_modules/string_decoder": {"version": "1.1.1", "resolved": "http://registry.npm.taobao.org/string_decoder/download/string_decoder-1.1.1.tgz", "integrity": "sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=", "dev": true, "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/string-width": {"version": "2.1.1", "resolved": "http://registry.npm.taobao.org/string-width/download/string-width-2.1.1.tgz", "integrity": "sha1-q5Pyeo3BPSjKyBXEYhQ6bZASrp4=", "dev": true, "dependencies": {"is-fullwidth-code-point": "^2.0.0", "strip-ansi": "^4.0.0"}, "engines": {"node": ">=4"}}, "node_modules/string-width/node_modules/ansi-regex": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/ansi-regex/download/ansi-regex-3.0.0.tgz", "integrity": "sha1-7QMXwyIGT3lGbAKWa922Bas32Zg=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/string-width/node_modules/strip-ansi": {"version": "4.0.0", "resolved": "http://registry.npm.taobao.org/strip-ansi/download/strip-ansi-4.0.0.tgz", "integrity": "sha1-qEeQIusaw2iocTibY1JixQXuNo8=", "dev": true, "dependencies": {"ansi-regex": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/string.prototype.padend": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/string.prototype.padend/download/string.prototype.padend-3.0.0.tgz", "integrity": "sha1-86rvfBcZ8XDF6rHDK/eA2W4h8vA=", "dev": true, "dependencies": {"define-properties": "^1.1.2", "es-abstract": "^1.4.3", "function-bind": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/string.prototype.padstart": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/string.prototype.padstart/download/string.prototype.padstart-3.0.0.tgz", "integrity": "sha1-W8+tOfRkm7LQMSkuGbzwtRDUskI=", "dev": true, "dependencies": {"define-properties": "^1.1.2", "es-abstract": "^1.4.3", "function-bind": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/strip-ansi": {"version": "5.2.0", "resolved": "http://registry.npm.taobao.org/strip-ansi/download/strip-ansi-5.2.0.tgz", "integrity": "sha1-jJpTb+tq/JYr36WxBKUJHBrZwK4=", "dev": true, "dependencies": {"ansi-regex": "^4.1.0"}, "engines": {"node": ">=6"}}, "node_modules/strip-eof": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/strip-eof/download/strip-eof-1.0.0.tgz", "integrity": "sha1-u0P/VZim6wXYm1n80SnJgzE2Br8=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/strip-indent": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/strip-indent/download/strip-indent-2.0.0.tgz", "integrity": "sha1-XvjbKV0B5u1sv3qrlpmNeCJSe2g=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/stylehacks": {"version": "4.0.3", "resolved": "http://registry.npm.taobao.org/stylehacks/download/stylehacks-4.0.3.tgz", "integrity": "sha1-Zxj8r00eB9ihMYaQiB6NlnJqcdU=", "dev": true, "dependencies": {"browserslist": "^4.0.0", "postcss": "^7.0.0", "postcss-selector-parser": "^3.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/stylehacks/node_modules/postcss-selector-parser": {"version": "3.1.1", "resolved": "http://registry.npm.taobao.org/postcss-selector-parser/download/postcss-selector-parser-3.1.1.tgz", "integrity": "sha1-T4dfSvsMllc9XPTXQBGu4lCn6GU=", "dev": true, "dependencies": {"dot-prop": "^4.1.1", "indexes-of": "^1.0.1", "uniq": "^1.0.1"}, "engines": {"node": ">=4"}}, "node_modules/supports-color": {"version": "5.5.0", "resolved": "http://registry.npm.taobao.org/supports-color/download/supports-color-5.5.0.tgz", "integrity": "sha1-4uaaRKyHcveKHsCzW2id9lMO/I8=", "dev": true, "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=4"}}, "node_modules/svg-pathdata": {"version": "6.0.3", "resolved": "https://registry.npmjs.org/svg-pathdata/-/svg-pathdata-6.0.3.tgz", "integrity": "sha512-qsjeeq5YjBZ5eMdFuUa4ZosMLxgr5RZ+F+Y1OrDhuOCEInRMA3x74XdBtggJcj9kOeInz0WE+LgCPDkZFlBYJw==", "optional": true, "engines": {"node": ">=12.0.0"}}, "node_modules/svg-tags": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/svg-tags/download/svg-tags-1.0.0.tgz", "integrity": "sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q=", "dev": true}, "node_modules/svgo": {"version": "1.2.0", "resolved": "http://registry.npm.taobao.org/svgo/download/svgo-1.2.0.tgz", "integrity": "sha1-MFqPwPT5cQgoxlA5u5PVeTIl/8M=", "deprecated": "This SVGO version is no longer supported. Upgrade to v2.x.x.", "dev": true, "dependencies": {"chalk": "^2.4.1", "coa": "^2.0.2", "css-select": "^2.0.0", "css-select-base-adapter": "^0.1.1", "css-tree": "1.0.0-alpha.28", "css-url-regex": "^1.1.0", "csso": "^3.5.1", "js-yaml": "^3.12.0", "mkdirp": "~0.5.1", "object.values": "^1.1.0", "sax": "~1.2.4", "stable": "^0.1.8", "unquote": "~1.1.1", "util.promisify": "~1.0.0"}, "bin": {"svgo": "bin/svgo"}, "engines": {"node": ">=4.0.0"}}, "node_modules/tapable": {"version": "1.1.1", "resolved": "http://registry.npm.taobao.org/tapable/download/tapable-1.1.1.tgz", "integrity": "sha1-TSl5I8WnKkI2DeKrUtrfquwAAY4=", "dev": true, "engines": {"node": ">=6"}}, "node_modules/terser": {"version": "3.17.0", "resolved": "http://registry.npm.taobao.org/terser/download/terser-3.17.0.tgz", "integrity": "sha1-+I/77aDetWN/nSSw2mb04VqxDLI=", "dev": true, "dependencies": {"commander": "^2.19.0", "source-map": "~0.6.1", "source-map-support": "~0.5.10"}, "bin": {"terser": "bin/uglifyjs"}, "engines": {"node": ">=6.0.0"}}, "node_modules/terser-webpack-plugin": {"version": "1.2.3", "resolved": "http://registry.npm.taobao.org/terser-webpack-plugin/download/terser-webpack-plugin-1.2.3.tgz", "integrity": "sha1-P5i8kC+sPl0N5zCGn1BmhWEmLsg=", "dev": true, "dependencies": {"cacache": "^11.0.2", "find-cache-dir": "^2.0.0", "schema-utils": "^1.0.0", "serialize-javascript": "^1.4.0", "source-map": "^0.6.1", "terser": "^3.16.1", "webpack-sources": "^1.1.0", "worker-farm": "^1.5.2"}, "engines": {"node": ">= 6.9.0"}, "peerDependencies": {"webpack": "^4.0.0"}}, "node_modules/terser-webpack-plugin/node_modules/schema-utils": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/schema-utils/download/schema-utils-1.0.0.tgz", "integrity": "sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A=", "dev": true, "dependencies": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}, "engines": {"node": ">= 4"}}, "node_modules/terser-webpack-plugin/node_modules/source-map": {"version": "0.6.1", "resolved": "http://registry.npm.taobao.org/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/terser/node_modules/source-map": {"version": "0.6.1", "resolved": "http://registry.npm.taobao.org/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/text-segmentation": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/text-segmentation/-/text-segmentation-1.0.3.tgz", "integrity": "sha512-iOiPUo/BGnZ6+54OsWxZidGCsdU8YbE4PSpdPinp7DeMtUJNJBoJ/ouUSTJjHkh1KntHaltHl/gDs2FC4i5+Nw==", "dependencies": {"utrie": "^1.0.2"}}, "node_modules/thread-loader": {"version": "2.1.2", "resolved": "http://registry.npm.taobao.org/thread-loader/download/thread-loader-2.1.2.tgz", "integrity": "sha1-9YXdOOhSx/nN7V0JKZIQgUj16zA=", "dev": true, "dependencies": {"loader-runner": "^2.3.1", "loader-utils": "^1.1.0", "neo-async": "^2.6.0"}, "engines": {"node": ">= 6.9.0 <7.0.0 || >= 8.9.0"}, "peerDependencies": {"webpack": "^2.0.0 || ^3.0.0 || ^4.0.0"}}, "node_modules/throttle-debounce": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/throttle-debounce/-/throttle-debounce-1.1.0.tgz", "integrity": "sha512-XH8UiPCQcWNuk2LYePibW/4qL97+ZQ1AN3FNXwZRBNPPowo/NRU5fAlDCSNBJIYCKbioZfuYtMhG4quqoJhVzg==", "engines": {"node": ">=4"}}, "node_modules/through2": {"version": "2.0.5", "resolved": "http://registry.npm.taobao.org/through2/download/through2-2.0.5.tgz", "integrity": "sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=", "dev": true, "dependencies": {"readable-stream": "~2.3.6", "xtend": "~4.0.1"}}, "node_modules/thunky": {"version": "1.0.3", "resolved": "http://registry.npm.taobao.org/thunky/download/thunky-1.0.3.tgz", "integrity": "sha1-9d9zJFNAewkZHa5z4qjMc/OBqCY=", "dev": true}, "node_modules/timers-browserify": {"version": "2.0.10", "resolved": "http://registry.npm.taobao.org/timers-browserify/download/timers-browserify-2.0.10.tgz", "integrity": "sha1-HSjj0qrfHVpZlsTp+VYBzQU0gK4=", "dev": true, "dependencies": {"setimmediate": "^1.0.4"}, "engines": {"node": ">=0.6.0"}}, "node_modules/timsort": {"version": "0.3.0", "resolved": "http://registry.npm.taobao.org/timsort/download/timsort-0.3.0.tgz", "integrity": "sha1-QFQRqOfmM5/mTbmiNN4R3DHgK9Q=", "dev": true}, "node_modules/to-arraybuffer": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/to-arraybuffer/download/to-arraybuffer-1.0.1.tgz", "integrity": "sha1-fSKbH8xjfkZsoIEYCDanqr/4P0M=", "dev": true}, "node_modules/to-fast-properties": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/to-fast-properties/download/to-fast-properties-2.0.0.tgz", "integrity": "sha1-3F5pjL0HkmW8c+A3doGk5Og/YW4=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/to-object-path": {"version": "0.3.0", "resolved": "http://registry.npm.taobao.org/to-object-path/download/to-object-path-0.3.0.tgz", "integrity": "sha1-KXWIt7Dn4KwI4E5nL4XB9JmeF68=", "dev": true, "dependencies": {"kind-of": "^3.0.2"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-object-path/node_modules/kind-of": {"version": "3.2.2", "resolved": "http://registry.npm.taobao.org/kind-of/download/kind-of-3.2.2.tgz", "integrity": "sha1-MeohpzS6ubuw8yRm2JOupR5KPGQ=", "dev": true, "dependencies": {"is-buffer": "^1.1.5"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-regex": {"version": "3.0.2", "resolved": "http://registry.npm.taobao.org/to-regex/download/to-regex-3.0.2.tgz", "integrity": "sha1-E8/dmzNlUvMLUfM6iuG0Knp1mc4=", "dev": true, "dependencies": {"define-property": "^2.0.2", "extend-shallow": "^3.0.2", "regex-not": "^1.0.2", "safe-regex": "^1.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/to-regex-range": {"version": "2.1.1", "resolved": "http://registry.npm.taobao.org/to-regex-range/download/to-regex-range-2.1.1.tgz", "integrity": "sha1-fIDBe53+vlmeJzZ+DU3VWQFB2zg=", "dev": true, "dependencies": {"is-number": "^3.0.0", "repeat-string": "^1.6.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/topo": {"version": "3.0.3", "resolved": "http://registry.npm.taobao.org/topo/download/topo-3.0.3.tgz", "integrity": "sha1-1aZ/suaTB+vusIQC7Coqb1962Vw=", "deprecated": "This module has moved and is now available at @hapi/topo. Please update your dependencies as this version is no longer maintained an may contain bugs and security issues.", "dev": true, "dependencies": {"hoek": "6.x.x"}}, "node_modules/toposort": {"version": "1.0.7", "resolved": "http://registry.npm.taobao.org/toposort/download/toposort-1.0.7.tgz", "integrity": "sha1-LmhELZ9k7HILjMieZEOsbKqVACk=", "dev": true}, "node_modules/tough-cookie": {"version": "2.4.3", "resolved": "http://registry.npm.taobao.org/tough-cookie/download/tough-cookie-2.4.3.tgz", "integrity": "sha1-U/Nto/R3g7CSWvoG/587FlKA94E=", "dev": true, "dependencies": {"psl": "^1.1.24", "punycode": "^1.4.1"}, "engines": {"node": ">=0.8"}}, "node_modules/tough-cookie/node_modules/punycode": {"version": "1.4.1", "resolved": "http://registry.npm.taobao.org/punycode/download/punycode-1.4.1.tgz", "integrity": "sha1-wNWmOycYgArY4esPpSachN1BhF4=", "dev": true}, "node_modules/trim-right": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/trim-right/download/trim-right-1.0.1.tgz", "integrity": "sha1-yy4SAwZ+DI3h9hQJS5/kVwTqYAM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/tryer": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/tryer/download/tryer-1.0.1.tgz", "integrity": "sha1-8shUBoALmw90yfdGW4HqrSQSUvg=", "dev": true}, "node_modules/tslib": {"version": "1.9.3", "resolved": "http://registry.npm.taobao.org/tslib/download/tslib-1.9.3.tgz", "integrity": "sha1-1+TdeSRdhUKMTX5IIqeZF5VMooY=", "dev": true}, "node_modules/tty-browserify": {"version": "0.0.0", "resolved": "http://registry.npm.taobao.org/tty-browserify/download/tty-browserify-0.0.0.tgz", "integrity": "sha1-oVe6QC2iTpv5V/mqadUk7tQpAaY=", "dev": true}, "node_modules/tunnel-agent": {"version": "0.6.0", "resolved": "http://registry.npm.taobao.org/tunnel-agent/download/tunnel-agent-0.6.0.tgz", "integrity": "sha1-J6XeoGs2sEoKmWZ3SykIaPD8QP0=", "dev": true, "dependencies": {"safe-buffer": "^5.0.1"}, "engines": {"node": "*"}}, "node_modules/tweetnacl": {"version": "0.14.5", "resolved": "http://registry.npm.taobao.org/tweetnacl/download/tweetnacl-0.14.5.tgz", "integrity": "sha1-WuaBd/GS1EViadEIr6k/+HQ/T2Q=", "dev": true}, "node_modules/type-is": {"version": "1.6.16", "resolved": "http://registry.npm.taobao.org/type-is/download/type-is-1.6.16.tgz", "integrity": "sha1-+JzjQVQcZysl7nrjxz3uOyvlAZQ=", "dev": true, "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.18"}, "engines": {"node": ">= 0.6"}}, "node_modules/typedarray": {"version": "0.0.6", "resolved": "http://registry.npm.taobao.org/typedarray/download/typedarray-0.0.6.tgz", "integrity": "sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=", "dev": true}, "node_modules/uglify-js": {"version": "3.4.10", "resolved": "http://registry.npm.taobao.org/uglify-js/download/uglify-js-3.4.10.tgz", "integrity": "sha1-mtlWPY6zrN+404WX0q8dgV9qdV8=", "dev": true, "dependencies": {"commander": "~2.19.0", "source-map": "~0.6.1"}, "bin": {"uglifyjs": "bin/uglifyjs"}, "engines": {"node": ">=0.8.0"}}, "node_modules/uglify-js/node_modules/source-map": {"version": "0.6.1", "resolved": "http://registry.npm.taobao.org/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/unicode-canonical-property-names-ecmascript": {"version": "1.0.4", "resolved": "http://registry.npm.taobao.org/unicode-canonical-property-names-ecmascript/download/unicode-canonical-property-names-ecmascript-1.0.4.tgz", "integrity": "sha1-JhmADEyCWADv3YNDr33Zkzy+KBg=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/unicode-match-property-ecmascript": {"version": "1.0.4", "resolved": "http://registry.npm.taobao.org/unicode-match-property-ecmascript/download/unicode-match-property-ecmascript-1.0.4.tgz", "integrity": "sha1-jtKjJWmWG86SJ9Cc0/+7j+1fAgw=", "dev": true, "dependencies": {"unicode-canonical-property-names-ecmascript": "^1.0.4", "unicode-property-aliases-ecmascript": "^1.0.4"}, "engines": {"node": ">=4"}}, "node_modules/unicode-match-property-value-ecmascript": {"version": "1.1.0", "resolved": "http://registry.npm.taobao.org/unicode-match-property-value-ecmascript/download/unicode-match-property-value-ecmascript-1.1.0.tgz", "integrity": "sha1-W0tCbgjROoA2Xg1lesemwexGonc=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/unicode-property-aliases-ecmascript": {"version": "1.0.5", "resolved": "http://registry.npm.taobao.org/unicode-property-aliases-ecmascript/download/unicode-property-aliases-ecmascript-1.0.5.tgz", "integrity": "sha1-qcxsx85joKMCP8meNBuUQx1AWlc=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/union-value": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/union-value/download/union-value-1.0.0.tgz", "integrity": "sha1-XHHDTLW61dzr4+oM0IIHulqhrqQ=", "dev": true, "dependencies": {"arr-union": "^3.1.0", "get-value": "^2.0.6", "is-extendable": "^0.1.1", "set-value": "^0.4.3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/union-value/node_modules/extend-shallow": {"version": "2.0.1", "resolved": "http://registry.npm.taobao.org/extend-shallow/download/extend-shallow-2.0.1.tgz", "integrity": "sha1-Ua99YUrZqfYQ6huvu5idaxxWiQ8=", "dev": true, "dependencies": {"is-extendable": "^0.1.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/union-value/node_modules/set-value": {"version": "0.4.3", "resolved": "http://registry.npm.taobao.org/set-value/download/set-value-0.4.3.tgz", "integrity": "sha1-fbCPnT0i3H945Trzw79GZuzfzPE=", "deprecated": "Critical bug fixed in v3.0.1, please upgrade to the latest version.", "dev": true, "dependencies": {"extend-shallow": "^2.0.1", "is-extendable": "^0.1.1", "is-plain-object": "^2.0.1", "to-object-path": "^0.3.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/uniq": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/uniq/download/uniq-1.0.1.tgz", "integrity": "sha1-sxxa6CVIRKOoKBVBzisEuGWnNP8=", "dev": true}, "node_modules/uniqs": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/uniqs/download/uniqs-2.0.0.tgz", "integrity": "sha1-/+3ks2slKQaW5uFl1KWe25mOawI=", "dev": true}, "node_modules/unique-filename": {"version": "1.1.1", "resolved": "http://registry.npm.taobao.org/unique-filename/download/unique-filename-1.1.1.tgz", "integrity": "sha1-HWl2k2mtoFgxA6HmrodoG1ZXMjA=", "dev": true, "dependencies": {"unique-slug": "^2.0.0"}}, "node_modules/unique-slug": {"version": "2.0.1", "resolved": "http://registry.npm.taobao.org/unique-slug/download/unique-slug-2.0.1.tgz", "integrity": "sha1-Xp7cbRzo+yZNsYpQfvm9hURFHKY=", "dev": true, "dependencies": {"imurmurhash": "^0.1.4"}}, "node_modules/universalify": {"version": "0.1.2", "resolved": "http://registry.npm.taobao.org/universalify/download/universalify-0.1.2.tgz", "integrity": "sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY=", "dev": true, "engines": {"node": ">= 4.0.0"}}, "node_modules/unpipe": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/unpipe/download/unpipe-1.0.0.tgz", "integrity": "sha1-sr9O6FFKrmFltIF4KdIbLvSZBOw=", "dev": true, "engines": {"node": ">= 0.8"}}, "node_modules/unquote": {"version": "1.1.1", "resolved": "http://registry.npm.taobao.org/unquote/download/unquote-1.1.1.tgz", "integrity": "sha1-j97XMk7G6IoP+LkF58CYzcCG1UQ=", "dev": true}, "node_modules/unset-value": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/unset-value/download/unset-value-1.0.0.tgz", "integrity": "sha1-g3aHP30jNRef+x5vw6jtDfyKtVk=", "dev": true, "dependencies": {"has-value": "^0.3.1", "isobject": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unset-value/node_modules/has-value": {"version": "0.3.1", "resolved": "http://registry.npm.taobao.org/has-value/download/has-value-0.3.1.tgz", "integrity": "sha1-ex9YutpiyoJ+wKIHgCVlSEWZXh8=", "dev": true, "dependencies": {"get-value": "^2.0.3", "has-values": "^0.1.4", "isobject": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unset-value/node_modules/has-value/node_modules/isobject": {"version": "2.1.0", "resolved": "http://registry.npm.taobao.org/isobject/download/isobject-2.1.0.tgz", "integrity": "sha1-8GVWEJaj8dou9GJy+BXIQNh+DIk=", "dev": true, "dependencies": {"isarray": "1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/unset-value/node_modules/has-values": {"version": "0.1.4", "resolved": "http://registry.npm.taobao.org/has-values/download/has-values-0.1.4.tgz", "integrity": "sha1-bWHeldkd/Km5oCCJrThL/49it3E=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/upath": {"version": "1.1.2", "resolved": "http://registry.npm.taobao.org/upath/download/upath-1.1.2.tgz", "integrity": "sha1-PbZYYA7a7sy+bbXmhNZ+6MKs0Gg=", "dev": true, "engines": {"node": ">=4", "yarn": "*"}}, "node_modules/upper-case": {"version": "1.1.3", "resolved": "http://registry.npm.taobao.org/upper-case/download/upper-case-1.1.3.tgz", "integrity": "sha1-9rRQHC7EzdJrp4vnIilh3ndiFZg=", "dev": true}, "node_modules/uri-js": {"version": "4.2.2", "resolved": "http://registry.npm.taobao.org/uri-js/download/uri-js-4.2.2.tgz", "integrity": "sha1-lMVA4f93KVbiKZUHwBCupsiDjrA=", "dev": true, "dependencies": {"punycode": "^2.1.0"}}, "node_modules/urix": {"version": "0.1.0", "resolved": "http://registry.npm.taobao.org/urix/download/urix-0.1.0.tgz", "integrity": "sha1-2pN/emLiH+wf0Y1Js1wpNQZ6bHI=", "deprecated": "Please see https://github.com/lydell/urix#deprecated", "dev": true}, "node_modules/url": {"version": "0.11.0", "resolved": "http://registry.npm.taobao.org/url/download/url-0.11.0.tgz?cache=0&other_urls=http%3A%2F%2Fregistry.npm.taobao.org%2Furl%2Fdownload%2Furl-0.11.0.tgz", "integrity": "sha1-ODjpfPxgUh63PFJajlW/3Z4uKPE=", "dev": true, "dependencies": {"punycode": "1.3.2", "querystring": "0.2.0"}}, "node_modules/url-loader": {"version": "1.1.2", "resolved": "http://registry.npm.taobao.org/url-loader/download/url-loader-1.1.2.tgz", "integrity": "sha1-uXHRkbg69pPF4/6kBkvp4fLX+Ng=", "dev": true, "dependencies": {"loader-utils": "^1.1.0", "mime": "^2.0.3", "schema-utils": "^1.0.0"}, "engines": {"node": ">= 6.9.0"}, "peerDependencies": {"webpack": "^3.0.0 || ^4.0.0"}}, "node_modules/url-loader/node_modules/schema-utils": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/schema-utils/download/schema-utils-1.0.0.tgz", "integrity": "sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A=", "dev": true, "dependencies": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}, "engines": {"node": ">= 4"}}, "node_modules/url-parse": {"version": "1.4.4", "resolved": "http://registry.npm.taobao.org/url-parse/download/url-parse-1.4.4.tgz", "integrity": "sha1-ysFVbpX6oDA2kf7Fz51aG8NGSPg=", "dev": true, "dependencies": {"querystringify": "^2.0.0", "requires-port": "^1.0.0"}}, "node_modules/url/node_modules/punycode": {"version": "1.3.2", "resolved": "http://registry.npm.taobao.org/punycode/download/punycode-1.3.2.tgz", "integrity": "sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0=", "dev": true}, "node_modules/use": {"version": "3.1.1", "resolved": "http://registry.npm.taobao.org/use/download/use-3.1.1.tgz", "integrity": "sha1-1QyMrHmhn7wg8pEfVuuXP04QBw8=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/util": {"version": "0.11.1", "resolved": "http://registry.npm.taobao.org/util/download/util-0.11.1.tgz", "integrity": "sha1-MjZzNyDsZLsn9uJvQhqqLhtYjWE=", "dev": true, "dependencies": {"inherits": "2.0.3"}}, "node_modules/util-deprecate": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/util-deprecate/download/util-deprecate-1.0.2.tgz", "integrity": "sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=", "dev": true}, "node_modules/util.promisify": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/util.promisify/download/util.promisify-1.0.0.tgz", "integrity": "sha1-RA9xZaRZyaFtwUXrjnLzVocJcDA=", "dev": true, "dependencies": {"define-properties": "^1.1.2", "object.getownpropertydescriptors": "^2.0.3"}}, "node_modules/utila": {"version": "0.4.0", "resolved": "http://registry.npm.taobao.org/utila/download/utila-0.4.0.tgz", "integrity": "sha1-ihagXURWV6Oupe7MWxKk+lN5dyw=", "dev": true}, "node_modules/utils-merge": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/utils-merge/download/utils-merge-1.0.1.tgz", "integrity": "sha1-n5VxD1CiZ5R7LMwSR0HBAoQn5xM=", "dev": true, "engines": {"node": ">= 0.4.0"}}, "node_modules/utrie": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/utrie/-/utrie-1.0.2.tgz", "integrity": "sha512-1MLa5ouZiOmQzUbjbu9VmjLzn1QLXBhwpUa7kdLUQK+KQ5KA9I1vk5U4YHe/X2Ch7PYnJfWuWT+VbuxbGwljhw==", "dependencies": {"base64-arraybuffer": "^1.0.2"}}, "node_modules/uuid": {"version": "3.3.2", "resolved": "http://registry.npm.taobao.org/uuid/download/uuid-3.3.2.tgz", "integrity": "sha1-G0r0lV6zB3xQHCOHL8ZROBFYcTE=", "deprecated": "Please upgrade  to version 7 or higher.  Older versions may use Math.random() in certain circumstances, which is known to be problematic.  See https://v8.dev/blog/math-random for details.", "dev": true, "bin": {"uuid": "bin/uuid"}}, "node_modules/validate-npm-package-license": {"version": "3.0.4", "resolved": "http://registry.npm.taobao.org/validate-npm-package-license/download/validate-npm-package-license-3.0.4.tgz", "integrity": "sha1-/JH2uce6FchX9MssXe/uw51PQQo=", "dev": true, "dependencies": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "node_modules/vary": {"version": "1.1.2", "resolved": "http://registry.npm.taobao.org/vary/download/vary-1.1.2.tgz", "integrity": "sha1-IpnwLG3tMNSllhsLn3RSShj2NPw=", "dev": true, "engines": {"node": ">= 0.8"}}, "node_modules/vendors": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/vendors/download/vendors-1.0.2.tgz", "integrity": "sha1-f8te759WI7FWvOqJ7DfWNnbyGAE=", "dev": true}, "node_modules/verror": {"version": "1.10.0", "resolved": "http://registry.npm.taobao.org/verror/download/verror-1.10.0.tgz", "integrity": "sha1-OhBcoXBTr1XW4nDB+CiGguGNpAA=", "dev": true, "engines": ["node >=0.6.0"], "dependencies": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "node_modules/vm-browserify": {"version": "0.0.4", "resolved": "http://registry.npm.taobao.org/vm-browserify/download/vm-browserify-0.0.4.tgz", "integrity": "sha1-XX6kW7755Kb/ZflUOOCofDV9WnM=", "dev": true, "dependencies": {"indexof": "0.0.1"}}, "node_modules/vue": {"version": "2.6.10", "resolved": "http://registry.npm.taobao.org/vue/download/vue-2.6.10.tgz?cache=0&other_urls=http%3A%2F%2Fregistry.npm.taobao.org%2Fvue%2Fdownload%2Fvue-2.6.10.tgz", "integrity": "sha1-pysaQqTYKnIepDjRtr9V5mGVxjc=", "deprecated": "Vue 2 has reached EOL and is no longer actively maintained. See https://v2.vuejs.org/eol/ for more details."}, "node_modules/vue-hot-reload-api": {"version": "2.3.3", "resolved": "http://registry.npm.taobao.org/vue-hot-reload-api/download/vue-hot-reload-api-2.3.3.tgz", "integrity": "sha1-J1b0bLMlgFTF9HI96K5+hzAqHM8=", "dev": true}, "node_modules/vue-loader": {"version": "15.7.0", "resolved": "http://registry.npm.taobao.org/vue-loader/download/vue-loader-15.7.0.tgz", "integrity": "sha1-JydapaPvSVjFN5wAbdFDatBLJbM=", "dev": true, "dependencies": {"@vue/component-compiler-utils": "^2.5.1", "hash-sum": "^1.0.2", "loader-utils": "^1.1.0", "vue-hot-reload-api": "^2.3.0", "vue-style-loader": "^4.1.0"}, "peerDependencies": {"css-loader": "*", "webpack": "^4.1.0 || ^5.0.0-0"}}, "node_modules/vue-router": {"version": "3.0.2", "resolved": "http://registry.npm.taobao.org/vue-router/download/vue-router-3.0.2.tgz", "integrity": "sha1-3txnr+bE4rwlaCyLHCqMDXx+Vr4="}, "node_modules/vue-style-loader": {"version": "4.1.2", "resolved": "http://registry.npm.taobao.org/vue-style-loader/download/vue-style-loader-4.1.2.tgz", "integrity": "sha1-3t80mAbyXOtOZPOtfApE+6c1/Pg=", "dev": true, "dependencies": {"hash-sum": "^1.0.2", "loader-utils": "^1.0.2"}}, "node_modules/vue-template-compiler": {"version": "2.6.10", "resolved": "http://registry.npm.taobao.org/vue-template-compiler/download/vue-template-compiler-2.6.10.tgz", "integrity": "sha1-MjtPNJXwT6o1AzN6gvXWUHeZycw=", "dev": true, "dependencies": {"de-indent": "^1.0.2", "he": "^1.1.0"}}, "node_modules/vue-template-es2015-compiler": {"version": "1.9.1", "resolved": "http://registry.npm.taobao.org/vue-template-es2015-compiler/download/vue-template-es2015-compiler-1.9.1.tgz", "integrity": "sha1-HuO8mhbsv1EYvjNLsV+cRvgvWCU=", "dev": true}, "node_modules/vuex": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/vuex/-/vuex-3.1.0.tgz", "integrity": "sha512-mdHeHT/7u4BncpUZMlxNaIdcN/HIt1GsGG5LKByArvYG/v6DvHcOxvDCts+7SRdCoIRGllK8IMZvQtQXLppDYg=="}, "node_modules/watchpack": {"version": "1.6.0", "resolved": "http://registry.npm.taobao.org/watchpack/download/watchpack-1.6.0.tgz", "integrity": "sha1-S8EsLr6KonenHx0/FNaFx7RGzQA=", "dev": true, "dependencies": {"chokidar": "^2.0.2", "graceful-fs": "^4.1.2", "neo-async": "^2.5.0"}}, "node_modules/wbuf": {"version": "1.7.3", "resolved": "http://registry.npm.taobao.org/wbuf/download/wbuf-1.7.3.tgz", "integrity": "sha1-wdjRSTFtPqhShIiVy2oL/oh7h98=", "dev": true, "dependencies": {"minimalistic-assert": "^1.0.0"}}, "node_modules/wcwidth": {"version": "1.0.1", "resolved": "http://registry.npm.taobao.org/wcwidth/download/wcwidth-1.0.1.tgz", "integrity": "sha1-8LDc+RW8X/FSivrbLA4XtTLaL+g=", "dev": true, "dependencies": {"defaults": "^1.0.3"}}, "node_modules/webpack": {"version": "4.28.4", "resolved": "http://registry.npm.taobao.org/webpack/download/webpack-4.28.4.tgz?cache=0&other_urls=http%3A%2F%2Fregistry.npm.taobao.org%2Fwebpack%2Fdownload%2Fwebpack-4.28.4.tgz", "integrity": "sha1-HdrmyJiH1++3Uq3ww80yubB+rNA=", "dev": true, "dependencies": {"@webassemblyjs/ast": "1.7.11", "@webassemblyjs/helper-module-context": "1.7.11", "@webassemblyjs/wasm-edit": "1.7.11", "@webassemblyjs/wasm-parser": "1.7.11", "acorn": "^5.6.2", "acorn-dynamic-import": "^3.0.0", "ajv": "^6.1.0", "ajv-keywords": "^3.1.0", "chrome-trace-event": "^1.0.0", "enhanced-resolve": "^4.1.0", "eslint-scope": "^4.0.0", "json-parse-better-errors": "^1.0.2", "loader-runner": "^2.3.0", "loader-utils": "^1.1.0", "memory-fs": "~0.4.1", "micromatch": "^3.1.8", "mkdirp": "~0.5.0", "neo-async": "^2.5.0", "node-libs-browser": "^2.0.0", "schema-utils": "^0.4.4", "tapable": "^1.1.0", "terser-webpack-plugin": "^1.1.0", "watchpack": "^1.5.0", "webpack-sources": "^1.3.0"}, "bin": {"webpack": "bin/webpack.js"}, "engines": {"node": ">=6.11.5"}}, "node_modules/webpack-bundle-analyzer": {"version": "3.1.0", "resolved": "http://registry.npm.taobao.org/webpack-bundle-analyzer/download/webpack-bundle-analyzer-3.1.0.tgz", "integrity": "sha1-LxnLuHu21PPLTlnLZ8g3vZQ26J0=", "dev": true, "dependencies": {"acorn": "^6.0.7", "acorn-walk": "^6.1.1", "bfj": "^6.1.1", "chalk": "^2.4.1", "commander": "^2.18.0", "ejs": "^2.6.1", "express": "^4.16.3", "filesize": "^3.6.1", "gzip-size": "^5.0.0", "lodash": "^4.17.10", "mkdirp": "^0.5.1", "opener": "^1.5.1", "ws": "^6.0.0"}, "bin": {"webpack-bundle-analyzer": "lib/bin/analyzer.js"}, "engines": {"node": ">= 6.14.4"}}, "node_modules/webpack-bundle-analyzer/node_modules/acorn": {"version": "6.1.1", "resolved": "http://registry.npm.taobao.org/acorn/download/acorn-6.1.1.tgz", "integrity": "sha1-fSWuBbuK0fm2mRCOEJTs14hK3B8=", "dev": true, "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/webpack-chain": {"version": "4.12.1", "resolved": "http://registry.npm.taobao.org/webpack-chain/download/webpack-chain-4.12.1.tgz", "integrity": "sha1-bIQ5u7KrVQlS1g4eqTGRQZBsAqY=", "deprecated": "Package no longer supported. Contact Support at https://www.npmjs.com/support for more info.", "dev": true, "dependencies": {"deepmerge": "^1.5.2", "javascript-stringify": "^1.6.0"}}, "node_modules/webpack-dev-middleware": {"version": "3.6.1", "resolved": "http://registry.npm.taobao.org/webpack-dev-middleware/download/webpack-dev-middleware-3.6.1.tgz", "integrity": "sha1-kfJTEhimM6mRiffeNgRaMxpLnNQ=", "dev": true, "dependencies": {"memory-fs": "^0.4.1", "mime": "^2.3.1", "range-parser": "^1.0.3", "webpack-log": "^2.0.0"}, "engines": {"node": ">= 6"}, "peerDependencies": {"webpack": "^4.0.0"}}, "node_modules/webpack-dev-server": {"version": "3.2.1", "resolved": "http://registry.npm.taobao.org/webpack-dev-server/download/webpack-dev-server-3.2.1.tgz", "integrity": "sha1-G0XOPs/FW26+Xjbasnd8ArxQjE4=", "dev": true, "dependencies": {"ansi-html": "0.0.7", "bonjour": "^3.5.0", "chokidar": "^2.0.0", "compression": "^1.5.2", "connect-history-api-fallback": "^1.3.0", "debug": "^4.1.1", "del": "^3.0.0", "express": "^4.16.2", "html-entities": "^1.2.0", "http-proxy-middleware": "^0.19.1", "import-local": "^2.0.0", "internal-ip": "^4.2.0", "ip": "^1.1.5", "killable": "^1.0.0", "loglevel": "^1.4.1", "opn": "^5.1.0", "portfinder": "^1.0.9", "schema-utils": "^1.0.0", "selfsigned": "^1.9.1", "semver": "^5.6.0", "serve-index": "^1.7.2", "sockjs": "0.3.19", "sockjs-client": "1.3.0", "spdy": "^4.0.0", "strip-ansi": "^3.0.0", "supports-color": "^6.1.0", "url": "^0.11.0", "webpack-dev-middleware": "^3.5.1", "webpack-log": "^2.0.0", "yargs": "12.0.2"}, "bin": {"webpack-dev-server": "bin/webpack-dev-server.js"}, "engines": {"node": ">= 6.11.5"}, "peerDependencies": {"webpack": "^4.0.0"}}, "node_modules/webpack-dev-server/node_modules/ansi-regex": {"version": "2.1.1", "resolved": "http://registry.npm.taobao.org/ansi-regex/download/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/webpack-dev-server/node_modules/schema-utils": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/schema-utils/download/schema-utils-1.0.0.tgz", "integrity": "sha1-C3mpMgTXtgDUsoUNH2bCo0lRx3A=", "dev": true, "dependencies": {"ajv": "^6.1.0", "ajv-errors": "^1.0.0", "ajv-keywords": "^3.1.0"}, "engines": {"node": ">= 4"}}, "node_modules/webpack-dev-server/node_modules/strip-ansi": {"version": "3.0.1", "resolved": "http://registry.npm.taobao.org/strip-ansi/download/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "dev": true, "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/webpack-dev-server/node_modules/supports-color": {"version": "6.1.0", "resolved": "http://registry.npm.taobao.org/supports-color/download/supports-color-6.1.0.tgz", "integrity": "sha1-B2Srxpxj1ayELdSGfo0CXogN+PM=", "dev": true, "dependencies": {"has-flag": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/webpack-log": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/webpack-log/download/webpack-log-2.0.0.tgz", "integrity": "sha1-W3ko4GN1k/EZ0y9iJ8HgrDHhtH8=", "dev": true, "dependencies": {"ansi-colors": "^3.0.0", "uuid": "^3.3.2"}, "engines": {"node": ">= 6"}}, "node_modules/webpack-merge": {"version": "4.2.1", "resolved": "http://registry.npm.taobao.org/webpack-merge/download/webpack-merge-4.2.1.tgz", "integrity": "sha1-XpI8+ALqKs5P1a8dMkc2imM0ibQ=", "dev": true, "dependencies": {"lodash": "^4.17.5"}}, "node_modules/webpack-sources": {"version": "1.3.0", "resolved": "http://registry.npm.taobao.org/webpack-sources/download/webpack-sources-1.3.0.tgz", "integrity": "sha1-KijcufH0X+lg2PFJMlK17mUw+oU=", "dev": true, "dependencies": {"source-list-map": "^2.0.0", "source-map": "~0.6.1"}}, "node_modules/webpack-sources/node_modules/source-map": {"version": "0.6.1", "resolved": "http://registry.npm.taobao.org/source-map/download/source-map-0.6.1.tgz", "integrity": "sha1-dHIq8y6WFOnCh6jQu95IteLxomM=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/websocket-driver": {"version": "0.7.0", "resolved": "http://registry.npm.taobao.org/websocket-driver/download/websocket-driver-0.7.0.tgz", "integrity": "sha1-DK+dLXVdk67gSdS90NP+LMoqJOs=", "dev": true, "dependencies": {"http-parser-js": ">=0.4.0", "websocket-extensions": ">=0.1.1"}, "engines": {"node": ">=0.8.0"}}, "node_modules/websocket-extensions": {"version": "0.1.3", "resolved": "http://registry.npm.taobao.org/websocket-extensions/download/websocket-extensions-0.1.3.tgz", "integrity": "sha1-XS/yKXcAPsaHpLhwc9+7rBRszyk=", "dev": true, "engines": {"node": ">=0.8.0"}}, "node_modules/which": {"version": "1.3.1", "resolved": "http://registry.npm.taobao.org/which/download/which-1.3.1.tgz", "integrity": "sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=", "dev": true, "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/which-module": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/which-module/download/which-module-2.0.0.tgz", "integrity": "sha1-2e8H3Od7mQK4o6j6SzHD4/fm6Ho=", "dev": true}, "node_modules/worker-farm": {"version": "1.6.0", "resolved": "http://registry.npm.taobao.org/worker-farm/download/worker-farm-1.6.0.tgz", "integrity": "sha1-rsxAWXb6talVJhgIRvDboojzpKA=", "dev": true, "dependencies": {"errno": "~0.1.7"}}, "node_modules/wrap-ansi": {"version": "2.1.0", "resolved": "http://registry.npm.taobao.org/wrap-ansi/download/wrap-ansi-2.1.0.tgz", "integrity": "sha1-2Pw9KE3QV5T+hJc8rs3Rz4JP3YU=", "dev": true, "dependencies": {"string-width": "^1.0.1", "strip-ansi": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/wrap-ansi/node_modules/ansi-regex": {"version": "2.1.1", "resolved": "http://registry.npm.taobao.org/ansi-regex/download/ansi-regex-2.1.1.tgz", "integrity": "sha1-w7M6te42DYbg5ijwRorn7yfWVN8=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/wrap-ansi/node_modules/is-fullwidth-code-point": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/is-fullwidth-code-point/download/is-fullwidth-code-point-1.0.0.tgz", "integrity": "sha1-754xOG8DGn8NZDr4L95QxFfvAMs=", "dev": true, "dependencies": {"number-is-nan": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/wrap-ansi/node_modules/string-width": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/string-width/download/string-width-1.0.2.tgz", "integrity": "sha1-EYvfW4zcUaKn5w0hHgfisLmxB9M=", "dev": true, "dependencies": {"code-point-at": "^1.0.0", "is-fullwidth-code-point": "^1.0.0", "strip-ansi": "^3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/wrap-ansi/node_modules/strip-ansi": {"version": "3.0.1", "resolved": "http://registry.npm.taobao.org/strip-ansi/download/strip-ansi-3.0.1.tgz", "integrity": "sha1-ajhfuIU9lS1f8F0Oiq+UJ43GPc8=", "dev": true, "dependencies": {"ansi-regex": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "http://registry.npm.taobao.org/wrappy/download/wrappy-1.0.2.tgz", "integrity": "sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=", "dev": true}, "node_modules/ws": {"version": "6.2.0", "resolved": "http://registry.npm.taobao.org/ws/download/ws-6.2.0.tgz", "integrity": "sha1-E4BtmROypfPLubpHtWPAAsvHxSY=", "dev": true, "dependencies": {"async-limiter": "~1.0.0"}}, "node_modules/xregexp": {"version": "4.0.0", "resolved": "http://registry.npm.taobao.org/xregexp/download/xregexp-4.0.0.tgz", "integrity": "sha1-5pgYneSd0qGMxWh7BeF8jkOUMCA=", "dev": true}, "node_modules/xtend": {"version": "4.0.1", "resolved": "http://registry.npm.taobao.org/xtend/download/xtend-4.0.1.tgz", "integrity": "sha1-pcbVMr5lbiPbgg77lDofBJmNY68=", "dev": true, "engines": {"node": ">=0.4"}}, "node_modules/y18n": {"version": "4.0.0", "resolved": "http://registry.npm.taobao.org/y18n/download/y18n-4.0.0.tgz", "integrity": "sha1-le+U+F7MgdAHwmThkKEg8KPIVms=", "dev": true}, "node_modules/yallist": {"version": "3.0.3", "resolved": "http://registry.npm.taobao.org/yallist/download/yallist-3.0.3.tgz", "integrity": "sha1-tLBJ4xS+VF486AIjbWzSLNkcPek=", "dev": true}, "node_modules/yargs": {"version": "12.0.2", "resolved": "http://registry.npm.taobao.org/yargs/download/yargs-12.0.2.tgz?cache=0&other_urls=http%3A%2F%2Fregistry.npm.taobao.org%2Fyargs%2Fdownload%2Fyargs-12.0.2.tgz", "integrity": "sha1-/lgjQ2k5KvM+y+9TgZFx7/D1qtw=", "dev": true, "dependencies": {"cliui": "^4.0.0", "decamelize": "^2.0.0", "find-up": "^3.0.0", "get-caller-file": "^1.0.1", "os-locale": "^3.0.0", "require-directory": "^2.1.1", "require-main-filename": "^1.0.1", "set-blocking": "^2.0.0", "string-width": "^2.0.0", "which-module": "^2.0.0", "y18n": "^3.2.1 || ^4.0.0", "yargs-parser": "^10.1.0"}}, "node_modules/yargs-parser": {"version": "10.1.0", "resolved": "http://registry.npm.taobao.org/yargs-parser/download/yargs-parser-10.1.0.tgz", "integrity": "sha1-cgImW4n36eny5XZeD+c1qQXtuqg=", "dev": true, "dependencies": {"camelcase": "^4.1.0"}}, "node_modules/yargs-parser/node_modules/camelcase": {"version": "4.1.0", "resolved": "http://registry.npm.taobao.org/camelcase/download/camelcase-4.1.0.tgz", "integrity": "sha1-1UVjW+HjPFQmScaRc+Xeas+uNN0=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/yorkie": {"version": "2.0.0", "resolved": "http://registry.npm.taobao.org/yorkie/download/yorkie-2.0.0.tgz", "integrity": "sha1-kkEZEtQ1IU4SxRwq4Qk+VLa7g9k=", "dev": true, "hasInstallScript": true, "dependencies": {"execa": "^0.8.0", "is-ci": "^1.0.10", "normalize-path": "^1.0.0", "strip-indent": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/yorkie/node_modules/cross-spawn": {"version": "5.1.0", "resolved": "http://registry.npm.taobao.org/cross-spawn/download/cross-spawn-5.1.0.tgz", "integrity": "sha1-6L0O/uWPz/b4+UUQoKVUu/ojVEk=", "dev": true, "dependencies": {"lru-cache": "^4.0.1", "shebang-command": "^1.2.0", "which": "^1.2.9"}}, "node_modules/yorkie/node_modules/execa": {"version": "0.8.0", "resolved": "http://registry.npm.taobao.org/execa/download/execa-0.8.0.tgz", "integrity": "sha1-2NdrvBtVIX7RkP1t1J08d07PyNo=", "dev": true, "dependencies": {"cross-spawn": "^5.0.1", "get-stream": "^3.0.0", "is-stream": "^1.1.0", "npm-run-path": "^2.0.0", "p-finally": "^1.0.0", "signal-exit": "^3.0.0", "strip-eof": "^1.0.0"}, "engines": {"node": ">=4"}}, "node_modules/yorkie/node_modules/get-stream": {"version": "3.0.0", "resolved": "http://registry.npm.taobao.org/get-stream/download/get-stream-3.0.0.tgz", "integrity": "sha1-jpQ9E1jcN1VQVOy+LtsFqhdO3hQ=", "dev": true, "engines": {"node": ">=4"}}, "node_modules/yorkie/node_modules/lru-cache": {"version": "4.1.5", "resolved": "http://registry.npm.taobao.org/lru-cache/download/lru-cache-4.1.5.tgz", "integrity": "sha1-i75Q6oW+1ZvJ4z3KuCNe6bz0Q80=", "dev": true, "dependencies": {"pseudomap": "^1.0.2", "yallist": "^2.1.2"}}, "node_modules/yorkie/node_modules/normalize-path": {"version": "1.0.0", "resolved": "http://registry.npm.taobao.org/normalize-path/download/normalize-path-1.0.0.tgz", "integrity": "sha1-MtDkcvkf80VwHBWoMRAY07CpA3k=", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/yorkie/node_modules/yallist": {"version": "2.1.2", "resolved": "http://registry.npm.taobao.org/yallist/download/yallist-2.1.2.tgz", "integrity": "sha1-HBH5IY8HYImkfdUS+TxmmaaoHVI=", "dev": true}, "node_modules/zrender": {"version": "5.6.1", "resolved": "https://registry.npmjs.org/zrender/-/zrender-5.6.1.tgz", "integrity": "sha512-OFXkDJKcrlx5su2XbzJvj/34Q3m6PvyCZkVPHGYpcCJ52ek4U/ymZyfuV1nKE23AyBJ51E/6Yr0mhZ7xGTO4ag==", "dependencies": {"tslib": "2.3.0"}}, "node_modules/zrender/node_modules/tslib": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.3.0.tgz", "integrity": "sha512-N82ooyxVNm6h1riLCoyS9e3fuJ3AMG2zIZs2Gd1ATcSFjSA23Q0fzjjZeh0jbJvWVDZ0cJT8yaNNaaXHzueNjg=="}}}