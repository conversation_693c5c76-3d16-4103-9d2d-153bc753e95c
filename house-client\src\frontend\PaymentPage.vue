<template>
  <div class="payment-page-container page-container has-fixed-header">
    <!-- 使用Header组件 -->
    <frontend-header :active-index="activeIndex"></frontend-header>

    <div class="page-wrapper">
      <!-- 面包屑导航 -->
      <div class="breadcrumb-container">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/frontend/orderlist' }">我的订单</el-breadcrumb-item>
          <el-breadcrumb-item>支付订单</el-breadcrumb-item>
        </el-breadcrumb>
      </div>

      <div class="payment-content" v-loading="loading">
        <el-card class="payment-card" v-if="order">
          <div slot="header" class="clearfix">
            <span>{{ getPaymentTitle() }}</span>
            <el-tag :type="getStatusType(order.status)" style="float: right">{{ getStatusText(order.status) }}</el-tag>
          </div>
          
          <div class="order-info">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="订单编号">{{ order.orderNo }}</el-descriptions-item>
              <el-descriptions-item label="创建时间">{{ formatDateTime(order.createTime) }}</el-descriptions-item>
              <el-descriptions-item label="房源信息">{{ order.house ? order.house.address : '加载中...' }}</el-descriptions-item>
              <el-descriptions-item label="租期">{{ order.duration }}个月</el-descriptions-item>
              <el-descriptions-item label="开始日期">{{ order.startDate }}</el-descriptions-item>
              <el-descriptions-item label="结束日期">{{ order.endDate }}</el-descriptions-item>
            </el-descriptions>
          </div>
          
          <div class="payment-info">
            <h3>支付信息</h3>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="月租金">¥{{ order.monthlyPrice }}</el-descriptions-item>
              <el-descriptions-item label="押金" v-if="!isRentPayment">¥{{ order.deposit }}</el-descriptions-item>
              <el-descriptions-item label="服务费" v-if="!isRentPayment">¥{{ order.serviceFee }}</el-descriptions-item>
              <el-descriptions-item label="支付总额">
                <span class="price-highlight">¥{{ getPaymentAmount() }}</span>
              </el-descriptions-item>
            </el-descriptions>
          </div>
          
          <div class="payment-methods">
            <h3>选择支付方式</h3>
            <el-radio-group v-model="paymentMethod">
              <el-radio label="alipay">
                <i class="el-icon-money payment-icon-placeholder" style="color: #1296db;"></i>
                支付宝
              </el-radio>
              <el-radio label="wechat">
                <i class="el-icon-chat-dot-round payment-icon-placeholder" style="color: #07c160;"></i>
                微信支付
              </el-radio>
              <el-radio label="bank">
                <i class="el-icon-bank-card payment-icon-placeholder"></i>
                银行卡支付
              </el-radio>
            </el-radio-group>
          </div>
          
          <div class="payment-action">
            <el-button type="primary" :disabled="!paymentMethod || paying" @click="handlePay" :loading="paying">
              立即支付 ¥{{ getPaymentAmount() }}
            </el-button>
            <el-button @click="goBack">返回</el-button>
          </div>
        </el-card>
        
        <el-empty v-else description="未找到订单信息" :image-size="200"></el-empty>
      </div>
    </div>
    
    <!-- 支付确认对话框 -->
    <el-dialog
      title="支付确认"
      :visible.sync="paymentDialogVisible"
      width="30%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :show-close="false"
    >
      <div class="payment-dialog-content">
        <div v-if="paymentStep === 'confirm'" class="payment-confirm">
          <p>您即将支付：<span class="price-highlight">¥{{ getPaymentAmount() }}</span></p>
          <p>支付类型：{{ getPaymentTitle() }}</p>
          <p>支付方式：{{ getPaymentMethodText() }}</p>
          <el-alert
            title="模拟支付环境，点击确认即视为支付成功"
            type="info"
            :closable="false"
            show-icon
          ></el-alert>
        </div>
        <div v-else-if="paymentStep === 'processing'" class="payment-processing">
          <el-progress type="circle" :percentage="paymentProgress" status="success"></el-progress>
          <p>支付处理中，请稍候...</p>
        </div>
        <div v-else-if="paymentStep === 'success'" class="payment-success">
          <i class="el-icon-success success-icon"></i>
          <h3>支付成功</h3>
          <p>订单已支付完成，您可以在"我的订单"中查看详情</p>
        </div>
        <div v-else-if="paymentStep === 'error'" class="payment-error">
          <i class="el-icon-error error-icon"></i>
          <h3>支付失败</h3>
          <p>{{ paymentError }}</p>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelPayment" v-if="paymentStep === 'confirm'">取消</el-button>
        <el-button type="primary" @click="confirmPayment" v-if="paymentStep === 'confirm'">确认支付</el-button>
        <el-button @click="closePaymentDialog" v-if="paymentStep === 'success' || paymentStep === 'error'">关闭</el-button>
      </span>
    </el-dialog>

    <!-- 使用Footer组件 -->
    <frontend-footer></frontend-footer>
  </div>
</template>

<script>
import FrontendHeader from './components/Header.vue';
import FrontendFooter from './components/Footer.vue';
import { getOrderByOrderNo } from './api/order';
import moment from 'moment';

export default {
  name: "PaymentPage",
  components: {
    FrontendHeader,
    FrontendFooter
  },
  data() {
    return {
      activeIndex: '/frontend/orderlist',
      orderNo: '',
      order: null,
      loading: false,
      paymentMethod: '',
      paying: false,
      paymentDialogVisible: false,
      paymentStep: 'confirm', // confirm, processing, success, error
      paymentProgress: 0,
      paymentError: '',
      paymentTimer: null,
      paymentType: 'deposit' // deposit: 押金, rent: 租金
    };
  },
  created() {
    // 从路由参数获取订单编号
    this.orderNo = this.$route.params.orderNo;
    if (!this.orderNo) {
      this.$message.error('订单编号不能为空');
      this.$router.push('/frontend/orderlist');
      return;
    }

    // 从查询参数获取支付类型
    this.paymentType = this.$route.query.type || 'deposit';

    // 获取订单详情
    this.fetchOrderDetail();
  },
  methods: {
    // 获取订单详情
    async fetchOrderDetail() {
      this.loading = true;
      try {
        const response = await getOrderByOrderNo(this.orderNo);
        if (response.data && response.data.flag) {
          this.order = response.data.data;
          
          // 获取房源信息
          if (this.order.houseId) {
            try {
              const houseRes = await this.$http.get(`/houses/${this.order.houseId}`);
              if (houseRes.data && houseRes.data.flag) {
                this.$set(this.order, 'house', houseRes.data.data);
              }
            } catch (error) {
              console.error('获取房源信息失败:', error);
            }
          }
        } else {
          this.$message.error('获取订单信息失败');
        }
      } catch (error) {
        console.error('获取订单详情失败:', error);
        this.$message.error('获取订单详情失败');
      } finally {
        this.loading = false;
      }
    },
    
    // 处理支付
    handlePay() {
      if (!this.paymentMethod) {
        this.$message.warning('请选择支付方式');
        return;
      }
      
      this.paymentStep = 'confirm';
      this.paymentDialogVisible = true;
    },
    
    // 取消支付
    cancelPayment() {
      this.paymentDialogVisible = false;
    },
    
    // 确认支付
    confirmPayment() {
      this.paymentStep = 'processing';
      this.paying = true;
      this.paymentProgress = 0;
      
      // 模拟支付进度
      this.paymentTimer = setInterval(() => {
        this.paymentProgress += 10;
        if (this.paymentProgress >= 100) {
          clearInterval(this.paymentTimer);
          this.processPayment();
        }
      }, 300);
    },
    
    // 处理支付请求
    async processPayment() {
      try {
        let response;

        if (this.isRentPayment) {
          // 支付租金
          response = await this.$http.put(`/orders/${this.orderNo}/pay-rent`, null, {
            params: { paymentMethod: this.paymentMethod }
          });
        } else {
          // 支付押金
          response = await this.$http.put(`/orders/${this.orderNo}/pay-deposit`, null, {
            params: { paymentMethod: this.paymentMethod }
          });
        }

        if (response.data && response.data.flag) {
          // 支付成功
          this.paymentStep = 'success';

          // 更新订单状态
          if (this.isRentPayment) {
            // 租金支付成功，状态保持为租赁中
            this.order.status = 'renting';
          } else {
            // 押金支付成功，状态更新为待入住
            this.order.status = 'pending';
          }

          // 延迟2秒后自动关闭对话框
          setTimeout(() => {
            this.closePaymentDialog();
            this.$router.push('/frontend/orderlist');
          }, 2000);
        } else {
          // 支付失败
          this.paymentStep = 'error';
          this.paymentError = response.data.message || '支付处理失败，请稍后再试';
        }
      } catch (error) {
        console.error('支付处理失败:', error);
        this.paymentStep = 'error';
        this.paymentError = '支付处理失败，请稍后再试';
      } finally {
        this.paying = false;
      }
    },
    
    // 关闭支付对话框
    closePaymentDialog() {
      this.paymentDialogVisible = false;
      if (this.paymentStep === 'success') {
        this.$router.push('/frontend/orderlist');
      }
    },
    
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    
    // 格式化日期时间
    formatDateTime(dateStr) {
      return moment(dateStr).format('YYYY-MM-DD HH:mm');
    },
    
    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        'application': 'info',
        'unpaid': 'warning',
        'paid': 'success',
        'renting': 'primary',
        'completed': 'info',
        'cancelled': 'danger'
      };
      return statusMap[status] || 'info';
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'application': '待审核',
        'unpaid': '待支付',
        'paid': '已支付',
        'renting': '租赁中',
        'completed': '已完成',
        'cancelled': '已取消'
      };
      return statusMap[status] || status;
    },
    
    // 获取支付方式文本
    getPaymentMethodText() {
      const methodMap = {
        'alipay': '支付宝',
        'wechat': '微信支付',
        'bank': '银行卡支付'
      };
      return methodMap[this.paymentMethod] || this.paymentMethod;
    },

    // 获取支付标题
    getPaymentTitle() {
      return this.isRentPayment ? '租金支付' : '押金支付';
    },

    // 获取支付金额
    getPaymentAmount() {
      if (!this.order) return 0;
      if (this.isRentPayment) {
        // 租金支付：只支付月租金
        return this.order.monthlyPrice;
      } else {
        // 押金支付：首月租金 + 押金 + 服务费
        return parseFloat(this.order.monthlyPrice) + parseFloat(this.order.deposit) + parseFloat(this.order.serviceFee);
      }
    }
  },

  computed: {
    // 是否是租金支付
    isRentPayment() {
      return this.paymentType === 'rent';
    }
  },
  beforeDestroy() {
    // 清除定时器
    if (this.paymentTimer) {
      clearInterval(this.paymentTimer);
    }
  }
};
</script>

<style scoped>
.page-wrapper {
  background-color: #f7f8fa;
  min-height: calc(100vh - 60px);
  padding: 20px;
}

.breadcrumb-container {
  margin-bottom: 20px;
  padding: 0 20px;
}

.payment-content {
  max-width: 900px;
  margin: 0 auto;
}

.payment-card {
  margin-bottom: 20px;
}

.order-info {
  margin-bottom: 20px;
}

.payment-info {
  margin: 30px 0;
}

.payment-methods {
  margin: 30px 0;
}

.payment-icon {
  width: 24px;
  height: 24px;
  margin-right: 8px;
  vertical-align: middle;
}

.payment-icon-placeholder {
  font-size: 24px;
  margin-right: 8px;
  vertical-align: middle;
}

.payment-action {
  margin-top: 30px;
  text-align: center;
}

.price-highlight {
  color: #f56c6c;
  font-weight: bold;
  font-size: 18px;
}

.payment-dialog-content {
  text-align: center;
  padding: 20px 0;
}

.payment-processing {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 20px 0;
}

.payment-success,
.payment-error {
  padding: 20px 0;
}

.success-icon {
  font-size: 60px;
  color: #67c23a;
  margin-bottom: 20px;
}

.error-icon {
  font-size: 60px;
  color: #f56c6c;
  margin-bottom: 20px;
}

h3 {
  margin-top: 10px;
  margin-bottom: 15px;
}
</style> 