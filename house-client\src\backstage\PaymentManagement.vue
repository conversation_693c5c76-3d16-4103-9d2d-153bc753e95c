<template>
  <div class="payment-management-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>支付管理</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          @click="handleRefresh"
        >
          <i class="el-icon-refresh"></i> 刷新
        </el-button>
      </div>

      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-form :inline="true" :model="searchForm" class="demo-form-inline">
          <el-form-item label="订单编号">
            <el-input v-model="searchForm.orderNo" placeholder="订单编号" clearable></el-input>
          </el-form-item>
          <el-form-item label="支付类型">
            <el-select v-model="searchForm.paymentType" placeholder="支付类型" clearable>
              <el-option label="押金" value="deposit"></el-option>
              <el-option label="首月租金" value="first_rent"></el-option>
              <el-option label="月租金" value="monthly_rent"></el-option>
              <el-option label="服务费" value="service_fee"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="状态" clearable>
              <el-option label="待支付" value="pending"></el-option>
              <el-option label="已支付" value="paid"></el-option>
              <el-option label="已退款" value="refunded"></el-option>
              <el-option label="已取消" value="cancelled"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格 -->
      <el-table
        v-loading="loading"
        :data="paymentList"
        border
        style="width: 100%"
      >
        <el-table-column prop="paymentNo" label="支付编号" width="180"></el-table-column>
        <el-table-column prop="orderNo" label="订单编号" width="180"></el-table-column>
        <el-table-column prop="houseTitle" label="房源信息" min-width="200">
          <template slot-scope="scope">
            <div class="house-info">
              <el-image
                style="width: 60px; height: 60px"
                :src="getHouseImage(scope.row.house)"
                :preview-src-list="[getHouseImage(scope.row.house)]"
                fit="cover"
              ></el-image>
              <div class="house-details">
                <div class="house-title">{{ scope.row.house ? scope.row.house.title : '房源信息获取失败' }}</div>
                <div class="house-address">{{ scope.row.house ? scope.row.house.address : '' }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="paymentType" label="支付类型" width="120">
          <template slot-scope="scope">
            <el-tag :type="getPaymentTypeTagType(scope.row.paymentType)">{{ getPaymentTypeText(scope.row.paymentType) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="金额" width="120">
          <template slot-scope="scope">¥{{ scope.row.amount }}</template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getPaymentStatusType(scope.row.status)">{{ getPaymentStatusText(scope.row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="dueDate" label="到期日" width="120">
          <template slot-scope="scope">{{ formatDate(scope.row.dueDate) }}</template>
        </el-table-column>
        <el-table-column prop="payTime" label="支付时间" width="160">
          <template slot-scope="scope">{{ scope.row.payTime ? formatDateTime(scope.row.payTime) : '未支付' }}</template>
        </el-table-column>
        <el-table-column label="操作" width="220">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleView(scope.row)"
              icon="el-icon-view"
            >查看</el-button>
            <el-button
              v-if="scope.row.status === 'pending' && canPay(scope.row)"
              size="mini"
              type="success"
              @click="handlePay(scope.row)"
              icon="el-icon-check"
            >支付</el-button>
            <el-button
              v-if="scope.row.status === 'paid' && isAdmin"
              size="mini"
              type="warning"
              @click="handleRefund(scope.row)"
              icon="el-icon-back"
            >退款</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
      </div>
    </el-card>

    <!-- 支付详情对话框 -->
    <el-dialog title="支付详情" :visible.sync="dialogVisible" width="50%">
      <div v-if="currentPayment" class="payment-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="支付编号">{{ currentPayment.paymentNo }}</el-descriptions-item>
          <el-descriptions-item label="订单编号">{{ currentPayment.orderNo }}</el-descriptions-item>
          <el-descriptions-item label="支付类型">
            <el-tag :type="getPaymentTypeTagType(currentPayment.paymentType)">{{ getPaymentTypeText(currentPayment.paymentType) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="支付状态">
            <el-tag :type="getPaymentStatusType(currentPayment.status)">{{ getPaymentStatusText(currentPayment.status) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="金额">¥{{ currentPayment.amount }}</el-descriptions-item>
          <el-descriptions-item label="到期日">{{ formatDate(currentPayment.dueDate) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(currentPayment.createTime) }}</el-descriptions-item>
          <el-descriptions-item label="支付时间">{{ currentPayment.payTime ? formatDateTime(currentPayment.payTime) : '未支付' }}</el-descriptions-item>
          <el-descriptions-item label="支付方式" v-if="currentPayment.paymentMethod">{{ getPaymentMethodText(currentPayment.paymentMethod) }}</el-descriptions-item>
          <el-descriptions-item label="交易号" v-if="currentPayment.transactionNo">{{ currentPayment.transactionNo }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ currentPayment.remark || '无' }}</el-descriptions-item>
        </el-descriptions>

        <div class="dialog-footer" slot="footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button
            v-if="currentPayment.status === 'pending' && canPay(currentPayment)"
            type="success"
            @click="handlePay(currentPayment)"
          >支付</el-button>
          <el-button
            v-if="currentPayment.status === 'paid' && isAdmin"
            type="warning"
            @click="handleRefund(currentPayment)"
          >退款</el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 支付对话框 -->
    <el-dialog title="支付" :visible.sync="payDialogVisible" width="40%">
      <div v-if="currentPayment" class="payment-form">
        <el-form :model="paymentForm" ref="paymentForm" label-width="100px" :rules="paymentRules">
          <el-form-item label="支付金额">
            <el-input v-model="paymentAmount" disabled></el-input>
          </el-form-item>
          <el-form-item label="支付方式" prop="paymentMethod">
            <el-select v-model="paymentForm.paymentMethod" placeholder="请选择支付方式">
              <el-option label="支付宝" value="alipay"></el-option>
              <el-option label="微信支付" value="wechat"></el-option>
              <el-option label="银行转账" value="bank"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="交易号" prop="transactionNo">
            <el-input v-model="paymentForm.transactionNo" placeholder="请输入交易号"></el-input>
          </el-form-item>
          <el-form-item label="备注">
            <el-input type="textarea" v-model="paymentForm.remark" placeholder="请输入备注信息"></el-input>
          </el-form-item>
        </el-form>

        <div class="pay-footer">
          <el-button @click="payDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitPayment">确认支付</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getBackstageUser, isAdmin, isOwner } from '../utils/auth'
import { getImageUrl } from '../utils/imageUtils'
import moment from 'moment'

export default {
  name: 'PaymentManagement',
  data() {
    return {
      loading: false,
      paymentList: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      searchForm: {
        orderNo: '',
        paymentType: '',
        status: ''
      },
      dialogVisible: false,
      payDialogVisible: false,
      currentPayment: null,
      userInfo: null,
      userId: null,
      isAdmin: false,
      paymentForm: {
        paymentMethod: '',
        transactionNo: '',
        remark: ''
      },
      paymentRules: {
        paymentMethod: [
          { required: true, message: '请选择支付方式', trigger: 'change' }
        ],
        transactionNo: [
          { required: true, message: '请输入交易号', trigger: 'blur' },
          { min: 6, message: '交易号长度不能小于6位', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    paymentAmount() {
      return this.currentPayment ? `¥${this.currentPayment.amount}` : '¥0'
    }
  },
  created() {
    this.userInfo = getBackstageUser()
    if (this.userInfo && this.userInfo.userInfo) {
      this.userId = this.userInfo.userInfo.id
    }
    this.isAdmin = isAdmin(false)
    this.fetchPaymentList()
  },
  methods: {
    // 获取支付列表
    async fetchPaymentList() {
      this.loading = true
      try {
        // 构建查询参数
        const params = {
          page: this.currentPage,
          limit: this.pageSize
        }

        // 添加搜索条件
        if (this.searchForm.orderNo) {
          params.orderNo = this.searchForm.orderNo
        }
        if (this.searchForm.paymentType) {
          params.paymentType = this.searchForm.paymentType
        }
        if (this.searchForm.status) {
          params.status = this.searchForm.status
        }

        // 根据角色添加不同的参数
        if (isAdmin(false)) {
          // 管理员可以查看所有支付
        } else if (isOwner(false)) {
          // 房东只能查看自己的房源的支付
          params.ownerId = this.userId
        } else {
          // 租客只能查看自己的支付
          params.tenantId = this.userId
        }

        // 调用API获取支付列表
        const res = await this.$http.get('/payments', { params })
        
        if (res.data && res.data.flag) {
          this.paymentList = res.data.data.list || []
          this.total = res.data.data.total || 0
          
          // 获取关联信息
          await this.fetchRelatedInfo()
        } else {
          this.$message.error('获取支付列表失败')
        }
      } catch (error) {
        console.error('获取支付列表失败:', error)
        this.$message.error('获取支付列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 获取关联信息（房源信息）
    async fetchRelatedInfo() {
      try {
        if (!this.paymentList || this.paymentList.length === 0) {
          return
        }
        
        // 遍历支付列表
        for (let i = 0; i < this.paymentList.length; i++) {
          const payment = this.paymentList[i]
          
          // 获取订单信息
          if (payment.orderNo && !payment.houseId) {
            try {
              const orderRes = await this.$http.get(`/orders/${payment.orderNo}`)
              if (orderRes.data && orderRes.data.flag) {
                const order = orderRes.data.data
                this.$set(this.paymentList[i], 'houseId', order.houseId)
                
                // 获取房源信息
                if (order.houseId) {
                  const houseRes = await this.$http.get(`/houses/${order.houseId}`)
                  if (houseRes.data && houseRes.data.flag) {
                    this.$set(this.paymentList[i], 'house', houseRes.data.data)
                  }
                }
              }
            } catch (error) {
              console.error(`获取订单信息失败:`, error)
            }
          }
          
          // 已经有houseId的情况
          if (payment.houseId && !payment.house) {
            try {
              const houseRes = await this.$http.get(`/houses/${payment.houseId}`)
              if (houseRes.data && houseRes.data.flag) {
                this.$set(this.paymentList[i], 'house', houseRes.data.data)
              }
            } catch (error) {
              console.error(`获取房源信息失败:`, error)
            }
          }
        }
      } catch (error) {
        console.error('获取关联信息失败:', error)
      }
    },
    
    // 处理查询
    handleSearch() {
      this.currentPage = 1
      this.fetchPaymentList()
    },
    
    // 重置查询
    resetSearch() {
      this.searchForm = {
        orderNo: '',
        paymentType: '',
        status: ''
      }
      this.handleSearch()
    },
    
    // 刷新
    handleRefresh() {
      this.fetchPaymentList()
    },
    
    // 查看详情
    handleView(row) {
      this.currentPayment = { ...row }
      this.dialogVisible = true
    },
    
    // 支付
    handlePay(row) {
      this.currentPayment = { ...row }
      this.paymentForm = {
        paymentMethod: '',
        transactionNo: '',
        remark: ''
      }
      this.payDialogVisible = true
    },
    
    // 提交支付
    submitPayment() {
      this.$refs.paymentForm.validate(async (valid) => {
        if (valid) {
          try {
            // 调用API
            const res = await this.$http.put(`/payments/${this.currentPayment.paymentNo}/pay`, {
              paymentMethod: this.paymentForm.paymentMethod,
              transactionNo: this.paymentForm.transactionNo,
              payTime: new Date(),
              remark: this.paymentForm.remark,
              status: 'paid'
            })
            
            if (res.data && res.data.flag) {
              this.$message.success('支付成功')
              this.payDialogVisible = false
              this.dialogVisible = false
              this.fetchPaymentList()
            } else {
              this.$message.error(res.data.message || '支付失败')
            }
          } catch (error) {
            console.error('支付失败:', error)
            this.$message.error('支付失败')
          }
        } else {
          return false
        }
      })
    },
    
    // 退款
    async handleRefund(row) {
      try {
        const { value } = await this.$prompt('请输入退款原因', '退款', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPlaceholder: '请输入退款原因'
        })
        
        // 调用API
        const res = await this.$http.put(`/payments/${row.paymentNo}/refund`, {
          status: 'refunded',
          refundReason: value || '系统退款',
          refundTime: new Date()
        })
        
        if (res.data && res.data.flag) {
          this.$message.success('退款成功')
          this.dialogVisible = false
          this.fetchPaymentList()
        } else {
          this.$message.error(res.data.message || '退款失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('退款失败:', error)
          this.$message.error('退款失败')
        }
      }
    },
    
    // 判断是否可以支付
    canPay(payment) {
      // 租客才能支付
      return !isOwner() && !isAdmin()
    },
    
    // 获取房屋图片
    getHouseImage(house) {
      if (!house) return require('../assets/showcase.jpg')
      
      if (house.imageUrl) {
        return getImageUrl(house.imageUrl)
      }
      
      return getImageUrl('/img/showcase.jpg')
    },
    
    // 格式化日期
    formatDate(dateStr) {
      return moment(dateStr).format('YYYY-MM-DD')
    },
    
    // 格式化日期时间
    formatDateTime(dateStr) {
      return moment(dateStr).format('YYYY-MM-DD HH:mm')
    },
    
    // 获取支付类型标签类型
    getPaymentTypeTagType(type) {
      const typeMap = {
        'deposit': 'warning',
        'first_rent': 'primary',
        'monthly_rent': 'success',
        'service_fee': 'info'
      }
      return typeMap[type] || 'info'
    },
    
    // 获取支付类型文本
    getPaymentTypeText(type) {
      const typeMap = {
        'deposit': '押金',
        'first_rent': '首月租金',
        'monthly_rent': '月租金',
        'service_fee': '服务费'
      }
      return typeMap[type] || type
    },
    
    // 获取支付状态类型
    getPaymentStatusType(status) {
      const statusMap = {
        'pending': 'info',
        'paid': 'success',
        'refunded': 'warning',
        'cancelled': 'danger'
      }
      return statusMap[status] || 'info'
    },
    
    // 获取支付状态文本
    getPaymentStatusText(status) {
      const statusMap = {
        'pending': '待支付',
        'paid': '已支付',
        'refunded': '已退款',
        'cancelled': '已取消'
      }
      return statusMap[status] || status
    },
    
    // 获取支付方式文本
    getPaymentMethodText(method) {
      const methodMap = {
        'alipay': '支付宝',
        'wechat': '微信支付',
        'bank': '银行转账'
      }
      return methodMap[method] || method
    },
    
    // 处理页大小变化
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchPaymentList()
    },
    
    // 处理页码变化
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchPaymentList()
    }
  }
}
</script>

<style scoped>
.payment-management-container {
  padding: 20px;
}

.search-bar {
  margin-bottom: 20px;
}

.house-info {
  display: flex;
  align-items: center;
}

.house-details {
  margin-left: 10px;
  overflow: hidden;
}

.house-title {
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.house-address {
  color: #909399;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.payment-detail {
  padding: 20px 0;
}

.payment-form {
  padding: 20px;
}

.pay-footer {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  margin-top: 20px;
  text-align: right;
}
</style> 