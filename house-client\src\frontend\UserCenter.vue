<template>
  <div class="user-center-page content-scroll has-fixed-header">
    <Header />
    <div class="user-container">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="user-menu">
            <div class="user-info">
              <div class="avatar">
                <div class="user-avatar" :style="{ backgroundImage: `url(${processedAvatar})` }"></div>
              </div>
              <div class="name">{{userInfo.name}}</div>
              <div class="role">{{userInfo.type === 3 ? '房东' : '租客'}}</div>
            </div>
            <el-menu
              default-active="1"
              class="el-menu-vertical"
              @select="handleSelect">
              <el-menu-item index="1">
                <i class="el-icon-user"></i>
                <span slot="title">个人资料</span>
              </el-menu-item>
              <el-menu-item v-if="userInfo.type === 3" index="2">
                <i class="el-icon-house"></i>
                <span slot="title">我的房源</span>
              </el-menu-item>
              <el-menu-item index="3">
                <i class="el-icon-document"></i>
                <span slot="title">我的订单</span>
              </el-menu-item>
              <el-menu-item index="4">
                <i class="el-icon-chat-dot-square"></i>
                <span slot="title">消息中心</span>
              </el-menu-item>
            </el-menu>
          </div>
        </el-col>
        <el-col :span="18">
          <div class="content-area">
            <!-- 个人资料 -->
            <div v-if="activeIndex === '1'" class="profile-section">
              <h2>个人资料</h2>
              <el-form :model="userForm" label-width="80px" ref="userForm">
                <el-form-item label="用户名">
                  <el-input v-model="userForm.name"></el-input>
                </el-form-item>
                <el-form-item label="电话">
                  <el-input v-model="userForm.phone"></el-input>
                </el-form-item>
                <el-form-item label="邮箱">
                  <el-input v-model="userForm.email"></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="updateUserInfo">保存修改</el-button>
                </el-form-item>
              </el-form>
              <el-divider></el-divider>
              <h2>修改密码</h2>
              <el-form :model="passwordForm" label-width="120px" ref="passwordForm">
                <el-form-item label="原密码">
                  <el-input type="password" v-model="passwordForm.oldPassword"></el-input>
                </el-form-item>
                <el-form-item label="新密码">
                  <el-input type="password" v-model="passwordForm.newPassword"></el-input>
                </el-form-item>
                <el-form-item label="确认新密码">
                  <el-input type="password" v-model="passwordForm.confirmPassword"></el-input>
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="updatePassword">修改密码</el-button>
                </el-form-item>
              </el-form>
            </div>
            
            <!-- 我的房源 (仅房东) -->
            <div v-if="activeIndex === '2'" class="house-section">
              <h2>我的房源</h2>
              <el-button type="primary" @click="goToAddHouse">发布新房源</el-button>
              <el-table :data="myHouses" style="width: 100%; margin-top: 20px;">
                <el-table-column prop="title" label="房源标题"></el-table-column>
                <el-table-column prop="address" label="地址"></el-table-column>
                <el-table-column prop="price" label="价格/月"></el-table-column>
                <el-table-column prop="status" label="状态">
                  <template slot-scope="scope">
                    <el-tag :type="scope.row.status === '已租' ? 'success' : 'primary'">
                      {{scope.row.status}}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作">
                  <template slot-scope="scope">
                    <el-button size="mini" @click="editHouse(scope.row)">编辑</el-button>
                    <el-button size="mini" type="danger" @click="deleteHouse(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            
            <!-- 我的订单 -->
            <div v-if="activeIndex === '3'" class="order-section">
              <h2>我的订单</h2>
              <el-tabs v-model="orderActiveTab">
                <el-tab-pane label="全部订单" name="all"></el-tab-pane>
                <el-tab-pane label="待付款" name="pending"></el-tab-pane>
                <el-tab-pane label="进行中" name="ongoing"></el-tab-pane>
                <el-tab-pane label="已完成" name="completed"></el-tab-pane>
              </el-tabs>
              
              <el-table :data="filteredOrders" style="width: 100%;">
                <el-table-column prop="orderNo" label="订单编号" width="180"></el-table-column>
                <el-table-column prop="houseTitle" label="房源信息"></el-table-column>
                <el-table-column prop="createTime" label="创建时间" width="180"></el-table-column>
                <el-table-column prop="status" label="状态" width="100">
                  <template slot-scope="scope">
                    <el-tag :type="getOrderStatusType(scope.row.status)">{{scope.row.status}}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="220">
                  <template slot-scope="scope">
                    <el-button size="mini" @click="viewOrder(scope.row)">查看</el-button>
                    <el-button size="mini" type="primary" 
                      v-if="scope.row.status === '待付款'" 
                      @click="payOrder(scope.row)">付款</el-button>
                    <el-button size="mini" type="success" 
                      v-if="scope.row.status === '进行中'" 
                      @click="viewContract(scope.row)">查看合同</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            
            <!-- 消息中心 -->
            <div v-if="activeIndex === '4'" class="message-section">
              <h2>消息中心</h2>
              <el-tabs v-model="messageActiveTab">
                <el-tab-pane label="系统通知" name="system"></el-tab-pane>
                <el-tab-pane label="订单消息" name="order"></el-tab-pane>
                <el-tab-pane label="互动消息" name="interaction"></el-tab-pane>
              </el-tabs>
              
              <div class="message-list">
                <div v-if="messages.length === 0" class="empty-message">
                  暂无消息
                </div>
                <div v-for="(msg, index) in filteredMessages" :key="index" 
                  class="message-item" :class="{'unread': !msg.isRead}">
                  <div class="message-header">
                    <span class="message-title">{{msg.title}}</span>
                    <span class="message-time">{{msg.time}}</span>
                  </div>
                  <div class="message-content">{{msg.content}}</div>
                  <div class="message-actions">
                    <a href="javascript:;" @click="markAsRead(msg)" v-if="!msg.isRead">标为已读</a>
                    <a href="javascript:;" @click="deleteMessage(msg)">删除</a>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <Footer />
  </div>
</template>

<script>
import Header from './components/Header.vue'
import Footer from './components/Footer.vue'
import { getUser } from '../utils/auth'
import axios from 'axios'
import { getImageUrl } from '../utils/imageUtils'

export default {
  name: 'UserCenter',
  components: {
    Header,
    Footer
  },
  data() {
    return {
      activeIndex: '1',
      userInfo: {},
      defaultAvatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
      userForm: {
        name: '',
        phone: '',
        email: ''
      },
      passwordForm: {
        oldPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      myHouses: [],
      orders: [],
      orderActiveTab: 'all',
      messages: [],
      messageActiveTab: 'system'
    }
  },
  computed: {
    processedAvatar() {
      return this.userInfo.avatar ? getImageUrl(this.userInfo.avatar) : this.defaultAvatar;
    },
    filteredOrders() {
      if (this.orderActiveTab === 'all') {
        return this.orders
      } else {
        const statusMap = {
          'pending': '待付款',
          'ongoing': '进行中',
          'completed': '已完成'
        }
        return this.orders.filter(order => order.status === statusMap[this.orderActiveTab])
      }
    },
    filteredMessages() {
      if (this.messageActiveTab === 'all') {
        return this.messages
      } else {
        return this.messages.filter(msg => msg.type === this.messageActiveTab)
      }
    }
  },
  created() {
    // 获取用户信息
    const userData = getUser()
    if (userData && userData.userInfo) {
      this.userInfo = userData.userInfo
      this.userForm = {
        name: this.userInfo.name,
        phone: this.userInfo.phone,
        email: this.userInfo.email
      }
      
      // 加载用户数据
      this.loadUserData()
      
      // 监听新消息事件
      this.$root.$on('new-message', this.handleNewMessage)
    } else {
      this.$router.push('/frontend/login')
    }
  },
  methods: {
    handleSelect(key) {
      this.activeIndex = key
    },
    loadUserData() {
      // 加载我的房源（如果是房东）
      if (this.userInfo.type === 3) {
        axios.post('/house/gethouselistbycondition', {
          userlist_Id: this.userInfo.id
        })
        .then(response => {
          if (response.data.flag) {
            this.myHouses = response.data.data || []
          }
        })
        .catch(error => {
          console.error('获取房源失败:', error)
        })
      }
      
      // 加载我的订单
      this.loadOrders()
      
      // 加载消息
      this.loadMessages()
    },
    updateUserInfo() {
      axios.post('/user/updateuser', {
        id: this.userInfo.id,
        name: this.userForm.name,
        phone: this.userForm.phone,
        email: this.userForm.email
      })
      .then(response => {
        if (response.data.flag) {
          this.$message.success('个人资料修改成功')
          // 更新本地存储的用户信息
          let userData = getUser()
          userData.userInfo.name = this.userForm.name
          userData.userInfo.phone = this.userForm.phone
          userData.userInfo.email = this.userForm.email
          localStorage.setItem('user', JSON.stringify(userData))
        } else {
          this.$message.error(response.data.message || '修改失败')
        }
      })
      .catch(error => {
        console.error('修改失败:', error)
        this.$message.error('服务器错误，请稍后再试')
      })
    },
    updatePassword() {
      if (this.passwordForm.newPassword !== this.passwordForm.confirmPassword) {
        this.$message.error('两次输入的密码不一致')
        return
      }
      
      axios.post('/user/editpassword', {
        userId: this.userInfo.userId,
        oldPassword: this.passwordForm.oldPassword,
        newPassword: this.passwordForm.newPassword
      })
      .then(response => {
        if (response.data.flag) {
          this.$message.success('密码修改成功')
          this.passwordForm = {
            oldPassword: '',
            newPassword: '',
            confirmPassword: ''
          }
        } else {
          this.$message.error(response.data.message || '修改失败')
        }
      })
      .catch(error => {
        console.error('修改失败:', error)
        this.$message.error('服务器错误，请稍后再试')
      })
    },
    goToAddHouse() {
      this.$router.push('/frontend/house/add')
    },
    editHouse(house) {
      this.$router.push(`/frontend/house/edit/${house.id}`)
    },
    deleteHouse(house) {
      this.$confirm('确定要删除该房源吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        axios.delete(`/house/deletehouse?houseId=${house.id}`)
          .then(response => {
            if (response.data.flag) {
              this.$message.success('删除成功')
              // 重新加载房源列表
              this.loadUserData()
            } else {
              this.$message.error(response.data.message || '删除失败')
            }
          })
          .catch(error => {
            console.error('删除失败:', error)
            this.$message.error('服务器错误，请稍后再试')
          })
      }).catch(() => {})
    },
    loadOrders() {
      // 实现加载订单的代码
      // TODO: 实现订单API后补充
      this.orders = [
        {
          orderNo: 'DD20230001',
          houseTitle: '示例房源1',
          createTime: '2023-01-01',
          status: '待付款'
        },
        {
          orderNo: 'DD20230002',
          houseTitle: '示例房源2',
          createTime: '2023-01-02',
          status: '进行中'
        },
        {
          orderNo: 'DD20230003',
          houseTitle: '示例房源3',
          createTime: '2023-01-03',
          status: '已完成'
        }
      ]
    },
    getOrderStatusType(status) {
      const statusMap = {
        '待付款': 'warning',
        '进行中': 'primary',
        '已完成': 'success',
        '已取消': 'info'
      }
      return statusMap[status] || 'info'
    },
    viewOrder(order) {
      this.$router.push(`/frontend/order/${order.orderNo}`)
    },
    payOrder(order) {
      this.$router.push(`/frontend/order/pay/${order.orderNo}`)
    },
    viewContract(order) {
      this.$router.push(`/frontend/order/contract/${order.orderNo}`)
    },
    loadMessages() {
      // 实现加载消息的代码
      // TODO: 实现消息API后补充
      this.messages = [
        {
          id: 1,
          title: '系统通知',
          content: '欢迎使用租房系统',
          time: '2023-01-01 10:00',
          isRead: true,
          type: 'system'
        },
        {
          id: 2,
          title: '订单通知',
          content: '您有新的订单需要处理',
          time: '2023-01-02 11:00',
          isRead: false,
          type: 'order'
        }
      ]
    },
    markAsRead(message) {
      // 标记消息为已读
      // TODO: 实现消息API后补充
      message.isRead = true
    },
    deleteMessage(message) {
      // 删除消息
      // TODO: 实现消息API后补充
      this.messages = this.messages.filter(m => m.id !== message.id)
    },
    handleNewMessage(message) {
      // 处理新接收到的消息
      this.messages.unshift({
        id: Date.now(),
        title: message.title,
        content: message.content,
        time: new Date().toLocaleString(),
        isRead: false,
        type: message.type || 'system'
      })
    }
  }
}
</script>

<style scoped>
.user-center {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.user-container {
  flex: 1;
  max-width: 1200px;
  margin: 20px auto;
  padding: 0 20px;
}

.user-menu {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding-bottom: 20px;
}

.user-info {
  padding: 20px 0;
  text-align: center;
  border-bottom: 1px solid #eee;
}

.avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin: 0 auto 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-avatar {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 50%;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  border: 3px solid #fff;
  transition: all 0.3s ease;
}

.user-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.name {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 5px;
}

.role {
  font-size: 14px;
  color: #999;
}

.content-area {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 20px;
  min-height: 500px;
}

.profile-section h2,
.house-section h2,
.order-section h2,
.message-section h2 {
  margin-top: 0;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.message-item {
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.message-item.unread {
  background-color: #f8f8f8;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.message-title {
  font-weight: bold;
}

.message-time {
  color: #999;
  font-size: 12px;
}

.message-content {
  margin-bottom: 10px;
  line-height: 1.5;
}

.message-actions {
  text-align: right;
}

.message-actions a {
  color: #409EFF;
  margin-left: 10px;
  text-decoration: none;
}

.empty-message {
  text-align: center;
  padding: 20px;
  color: #999;
}
</style> 