package com.house.controller;

import java.util.HashMap;
import java.util.Map;

import com.house.dto.LoginUser;
import com.house.common.Result;
import com.house.common.StatusCode;
import com.house.dto.UserExecution;
import com.house.pojo.User;
import com.house.pojo.UserList;
import com.house.service.UserService;
import com.house.utils.JwtUtil;
import com.house.vo.PasswordVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

@Slf4j
@RestController
@CrossOrigin(origins = "http://localhost:8080", allowCredentials = "true", maxAge = 3600)
@RequestMapping(value="/user")
public class UserController {


	@Autowired
	private UserService userService;
	
	@Autowired
	private JwtUtil jwtUtil;

	
    /**
     * 登录
     */
    @PostMapping("/login")
    public Result login(@RequestBody Map<String, String> loginMap) {
        String username = loginMap.get("username");
        String password = loginMap.get("password");
        String system = loginMap.get("system"); // 获取系统参数，用于区分前台和后台
        boolean isBackstage = "backstage".equals(system); // 判断是否为后台登录
        
        if (StringUtils.isEmpty(username) || StringUtils.isEmpty(password)) {
            return new Result(false, StatusCode.LOGINERROR, "用户名或密码不能为空");
        }

        User user = userService.findByUsername(username);
        if (user == null) {
            return new Result(false, StatusCode.LOGINERROR, "用户不存在");
        }

        if (!user.getPassword().equals(password)) {
            return new Result(false, StatusCode.LOGINERROR, "密码错误");
        }

        // 判断用户类型和登录系统是否匹配
        if (isBackstage) {
            // 后台登录，只有管理员和房东可以登录
            if (user.getType() != 1 && user.getType() != 3) {
                return new Result(false, StatusCode.LOGINERROR, "您不是管理员或房东，无法登录后台");
            }
        }

        // 获取用户信息
		System.out.println(user.getId());
        UserList userInfo = userService.findUserInfoById(user.getId());
        if (userInfo == null) {
            return new Result(false, StatusCode.LOGINERROR, "用户信息不存在");
        }

        // 生成token
        String token = jwtUtil.createJWT(
                userInfo.getId().toString(), 
                user.getUsername(), 
                user.getType() == 1 ? "admin" : (user.getType() == 3 ? "owner" : "user"),
                loginMap.get("rememberMe") != null && Boolean.parseBoolean(loginMap.get("rememberMe")),
                isBackstage // 传入是否为后台token的标识
        );

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("token", token);
        resultMap.put("userInfo", userInfo);
        resultMap.put("roles", user.getType() == 1 ? "admin" : (user.getType() == 3 ? "owner" : "user"));

        return new Result(true, StatusCode.SUCCESS, "登录成功", resultMap);
    }

	@RequestMapping(value = "/getalluserlist",method = RequestMethod.GET)
	public Result getAllUserList(){
		return new Result(true, StatusCode.SUCCESS,"查找用户列表成功",userService.findUserListByCondition(null,null));
	}

	@RequestMapping(value = "/getuserlistbycondition",method = RequestMethod.POST)
	public Result getuUerListByCondition(@RequestBody UserList userList){
		return new Result(true, StatusCode.SUCCESS,"按条件查找用户列表成功",userService.findUserListByCondition(userList.getName(),userList.getId()));
	}

	@RequestMapping(value = "/getuserinfobycondition",method = RequestMethod.POST)
	public Result getUserInfoByCondition(@RequestBody UserList userList){
		return new Result(true, StatusCode.SUCCESS,"按条件查找用户列表成功",userService.findUserInfoByCondition(userList.getName(),userList.getUserId(),userList.getId()));

	}

	@RequestMapping(value="/adduser",method = RequestMethod.POST)
	public Result addUser(@RequestBody UserList userList){
		UserExecution ue;
		try{
			ue = userService.addUserListAndUserAccount(userList);
			if(ue.isFlag()){
				return new Result(true,StatusCode.SUCCESS,"添加用户成功");
			}else {
				return new Result(false,StatusCode.ERROR,"添加用户失败：" + ue.getReason());
			}
		}catch (Exception e){
			return new Result(false,StatusCode.ERROR,"添加用户失败：" + e.toString());
		}
	}

	@RequestMapping(value="/updateuser",method = RequestMethod.POST)
	public Result updateUser(@RequestBody UserList userList){
		UserExecution ue;
		try{
			ue = userService.updateUserList(userList);
			if(ue.isFlag()){
				return new Result(true,StatusCode.SUCCESS,"更新用户成功");
			}else {
				return new Result(false,StatusCode.ERROR,"更新用户失败：" + ue.getReason());
			}
		}catch (Exception e){
			return new Result(false,StatusCode.ERROR,"更新用户失败：" + e.toString());
		}
	}

	@RequestMapping(value="/deleteuser",method = RequestMethod.DELETE)
	public Result deleteUser(@RequestParam("userListid")Integer userListid){
		UserExecution ue;
		try{
			ue = userService.deleteUser(userListid);
			if(ue.isFlag()){
				return new Result(true,StatusCode.SUCCESS,"删除用户成功");
			}else {
				return new Result(false,StatusCode.ERROR,"删除用户失败：" + ue.getReason());
			}
		}catch (Exception e){
			return new Result(false,StatusCode.ERROR,"删除用户失败：" + e.toString());
		}
	}

	@RequestMapping(value="/editpassword",method = RequestMethod.POST)
	public Result updateUser(@RequestBody PasswordVO passwordVO){
		UserExecution ue;
		try{
			ue = userService.updatePassword(passwordVO);
			if(ue.isFlag()){
				return new Result(true,StatusCode.SUCCESS,"修改密码成功");
			}else {
				return new Result(false,StatusCode.ERROR,ue.getReason());
			}
		}catch (Exception e){
			return new Result(false,StatusCode.ERROR,"修改密码失败：" + e.toString());
		}
	}


}
