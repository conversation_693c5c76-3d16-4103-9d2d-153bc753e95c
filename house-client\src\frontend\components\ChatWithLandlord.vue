<template>
  <div class="chat-container" :class="{ 'chat-expanded': isExpanded }">
    <div class="chat-header" @click="toggleChat">
      <i class="el-icon-chat-line-round"></i>
      <span>咨询房东</span>
      <span class="unread-badge" v-if="unreadCount > 0">{{ unreadCount }}</span>
      <i :class="isExpanded ? 'el-icon-caret-bottom' : 'el-icon-caret-right'" class="toggle-icon"></i>
    </div>
    
    <div class="chat-body" v-show="isExpanded">
      <div class="chat-messages" ref="messageContainer">
        <div v-if="messages.length === 0" class="no-messages">
          <i class="el-icon-message"></i>
          <p>暂无消息，向房东咨询问题吧！</p>
        </div>
        <div 
          v-for="(message, index) in messages" 
          :key="index" 
          :class="['message', message.isSelf ? 'message-self' : 'message-other']">
          <div class="message-avatar">
            <div class="avatar-img" :style="{ backgroundImage: `url(${getMessageAvatar(message)})` }"></div>
          </div>
          <div class="message-content">
            <div class="message-sender">{{ message.isSelf ? '我' : '房东' }}</div>
            <div class="message-bubble">{{ message.content }}</div>
            <div class="message-time">{{ formatTime(message.timestamp) }}</div>
          </div>
        </div>
      </div>
      
      <div class="chat-input">
        <el-input
          type="textarea"
          :rows="2"
          placeholder="请输入您想咨询的问题..."
          v-model="inputMessage"
          @keyup.enter.native="sendMessage"
          :disabled="!isLoggedIn"
        ></el-input>
        <div class="input-actions">
          <span v-if="!isLoggedIn" class="login-tip">请先<el-button type="text" @click="goToLogin">登录</el-button>后发送消息</span>
          <div class="right-actions">
            <el-button type="danger" size="small" @click="clearHistory" v-if="messages.length > 0">
              清空记录 <i class="el-icon-delete"></i>
            </el-button>
            <el-button type="primary" size="small" @click="sendMessage" :disabled="!isLoggedIn || !inputMessage.trim()">
              发送 <i class="el-icon-position"></i>
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getUser, removeUser } from '../../utils/auth'
import { getImageUrl } from '../../utils/imageUtils'
import chatApi from '../api/chat'

export default {
  name: 'ChatWithLandlord',
  props: {
    houseId: {
      type: [Number, String],
      required: true
    },
    landlordId: {
      type: [Number, String],
      required: true
    },
    landlordAvatar: {
      type: String,
      default: ''
    }
  },
  computed: {
    processedUserAvatar() {
      return this.userAvatar ? this.getImageUrl(this.userAvatar) : require('../../assets/houselogo.png');
    },
    processedLandlordAvatar() {
      return this.landlordAvatar ? this.getImageUrl(this.landlordAvatar) : require('../../assets/houselogo.png');
    }
  },
  data() {
    return {
      isExpanded: false,
      inputMessage: '',
      messages: [],
      socket: null,
      connected: false,
      unreadCount: 0,
      reconnectTimer: null,
      heartbeatTimer: null,
      userAvatar: '',
      isLoggedIn: false,
      userId: null,
      isLoading: false
    }
  },
  created() {
    // 检查用户是否登录
    const userData = getUser();
    if (userData && userData.userInfo && userData.token) {
      this.isLoggedIn = true;
      this.userId = userData.userInfo.id;
      
      // 设置用户头像
      this.userAvatar = userData.userInfo.avatar ? getImageUrl(userData.userInfo.avatar) : require('../../assets/houselogo.png');
      
      // 初始化WebSocket连接
      this.initWebSocket();
      
      // 加载历史消息
      this.loadHistoryMessages();
    }
  },
  mounted() {
    // 添加页面可见性变化监听
    document.addEventListener('visibilitychange', this.handleVisibilityChange);
  },
  beforeDestroy() {
    // 移除事件监听
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    
    // 关闭WebSocket连接
    this.closeWebSocket();
  },
  methods: {
    getImageUrl(url) {
      if (!url) return require('../../assets/houselogo.png');
      
      // 如果是完整URL（以http开头），直接返回
      if (url.startsWith('http://') || url.startsWith('https://')) {
        return url;
      }
      
      // 处理以/api/images/开头的路径 - 直接拼接主机名
      if (url.startsWith('/api/images/')) {
        return `http://localhost:9002${url}`;
      }
      
      // 如果是相对路径，拼接后端地址
      if (url.startsWith('/images/')) {
        // 修改为访问后端的/api/images路径
        return `http://localhost:9002/api/images${url.substring(8)}`;
      }
      
      // 如果只有文件名，拼接完整路径
      if (!url.includes('/')) {
        return `http://localhost:9002/api/images/${url}`;
      }
      
      // 其他情况，尝试作为相对路径处理
      return `http://localhost:9002${url}`;
    },
    toggleChat() {
      this.isExpanded = !this.isExpanded;
      if (this.isExpanded) {
        this.unreadCount = 0;
        // 滚动到底部
        this.$nextTick(() => {
          this.scrollToBottom();
        });
        
        // 如果有未读消息，标记为已读
        if (this.isLoggedIn && this.userId) {
          this.markMessagesAsRead();
        }
      }
    },
    sendMessage() {
      if (!this.isLoggedIn || !this.inputMessage.trim()) {
        return;
      }
      
      const message = {
        type: 'chat',
        houseId: this.houseId,
        fromId: this.userId,
        toId: this.landlordId,
        content: this.inputMessage.trim(),
        timestamp: Date.now()
      };
      
      // 清空输入框
      const sentMessage = this.inputMessage.trim();
      this.inputMessage = '';
      
      // 发送WebSocket消息
      if (this.socket && this.socket.readyState === WebSocket.OPEN) {
        this.socket.send(JSON.stringify(message));
        
        // 发送后重新加载消息，而不是本地添加
        setTimeout(() => this.loadHistoryMessages(), 300);
      } else {
        // 如果连接不可用，尝试重连
        this.initWebSocket(() => {
          if (this.socket && this.socket.readyState === WebSocket.OPEN) {
            this.socket.send(JSON.stringify(message));
            setTimeout(() => this.loadHistoryMessages(), 300);
          } else {
            this.$message.error('WebSocket连接失败，尝试通过API发送消息');
            
            // 如果WebSocket连接失败，尝试通过API发送
            const apiMessage = {
              houseId: this.houseId,
              fromUserId: parseInt(this.userId),
              toUserId: parseInt(this.landlordId),
              content: sentMessage,
              messageType: 'text',
              readStatus: 0
            };
            
            chatApi.sendMessage(apiMessage)
              .then(response => {
                if (response.data && response.data.code === 20000) {
                  // 发送成功，重新加载消息
                  this.loadHistoryMessages();
                } else {
                  this.$message.error('消息发送失败，请稍后重试');
                }
              })
              .catch(error => {
                console.error('发送消息失败:', error);
                this.$message.error('消息发送失败，请稍后重试');
              });
          }
        });
      }
    },
    addMessage(message) {
      this.messages.push(message);
      
      // 如果聊天窗口是展开的，滚动到底部
      if (this.isExpanded) {
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      } else {
        // 如果聊天窗口是折叠的，增加未读消息计数
        if (!message.isSelf) {
          this.unreadCount++;
        }
      }
    },
    scrollToBottom() {
      const container = this.$refs.messageContainer;
      if (container) {
        container.scrollTop = container.scrollHeight;
      }
    },
    formatTime(timestamp) {
      if (!timestamp) return '';
      
      const date = new Date(timestamp);
      const now = new Date();
      
      // 如果是当天的消息，只显示时间
      if (date.toDateString() === now.toDateString()) {
        return date.getHours().toString().padStart(2, '0') + ':' +
               date.getMinutes().toString().padStart(2, '0');
      } else {
        // 否则显示月/日 时:分
        return (date.getMonth() + 1) + '/' + date.getDate() + ' ' +
               date.getHours().toString().padStart(2, '0') + ':' +
               date.getMinutes().toString().padStart(2, '0');
      }
    },
    goToLogin() {
      // 保存当前页面URL，登录后可以跳回来
      localStorage.setItem('redirectUrl', window.location.href);
      this.$router.push('/frontend/login');
    },
    loadHistoryMessages() {
      if (!this.isLoggedIn || !this.userId || this.isLoading) {
        return;
      }
      
      this.isLoading = true;
      
      // 调用API获取历史消息
      chatApi.getChatHistory(this.houseId, this.userId, this.landlordId)
        .then(response => {
          if (response.data && response.data.code === 20000) {
            const history = response.data.data || [];
            
            // 清空现有消息
            this.messages = [];
            
            // 添加历史消息
            history.forEach(msg => {
              this.addMessage({
                id: msg.id,
                content: msg.content,
                timestamp: new Date(msg.createTime).getTime(),
                isSelf: msg.fromUserId == this.userId,
                fromUserAvatar: msg.fromUserAvatar,
                toUserAvatar: msg.toUserAvatar
              });
            });
            
            // 如果有未读消息，更新未读计数
            const unreadMessages = history.filter(msg => msg.readStatus === 0 && msg.toUserId == this.userId);
            this.unreadCount = unreadMessages.length;
            
            // 如果聊天窗口是展开的，滚动到底部
            if (this.isExpanded) {
              this.$nextTick(() => {
                this.scrollToBottom();
              });
            }
          }
        })
        .catch(error => {
          console.error('获取历史消息失败:', error);
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    markMessagesAsRead() {
      if (!this.isLoggedIn || !this.userId) {
        return;
      }
      
      // 调用API将消息标记为已读
      chatApi.markMessagesAsRead(this.userId, this.landlordId)
        .then(response => {
          if (response.data && response.data.code === 20000) {
            // 标记成功，清除未读计数
            this.unreadCount = 0;
          }
        })
        .catch(error => {
          console.error('标记消息为已读失败:', error);
        });
    },
    clearHistory() {
      if (!this.isLoggedIn || !this.userId) {
        return;
      }
      
      // 确认是否清空聊天记录
      this.$confirm('确定要清空所有聊天记录吗？此操作不可恢复', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用API删除聊天记录
        chatApi.deleteChatHistory(this.houseId, this.userId, this.landlordId)
          .then(response => {
            if (response.data && response.data.code === 20000) {
              // 清空成功
              this.messages = [];
              this.unreadCount = 0;
              this.$message.success('聊天记录已清空');
            }
          })
          .catch(error => {
            console.error('清空聊天记录失败:', error);
            this.$message.error('清空聊天记录失败，请稍后重试');
          });
      }).catch(() => {
        // 取消清空
      });
    },
    initWebSocket(callback) {
      // 如果用户未登录，不初始化WebSocket
      if (!this.isLoggedIn || !this.userId) {
        return;
      }
      
      // 获取token
      const userData = getUser();
      const token = userData.token;
      
      // 如果token为空，不初始化WebSocket
      if (!token) {
        console.error('无法初始化WebSocket：token为空');
        this.$message.error('登录信息已失效，请重新登录');
        return;
      }
      
      try {
        // 检查是否已经有连接，如果有则关闭
        if (this.socket && this.socket.readyState !== WebSocket.CLOSED) {
          // 如果已经连接，不重复连接
          if (this.socket.readyState === WebSocket.OPEN) {
            if (callback) callback();
            return;
          }
          // 关闭现有连接
          this.closeWebSocket();
        }
        
        // WebSocket连接地址，指定为房源聊天
        // 确保token正确编码
        const encodedToken = encodeURIComponent(token);
        const wsUrl = `ws://localhost:9002/ws/chat/${this.userId}?houseId=${this.houseId}&landlordId=${this.landlordId}&token=${encodedToken}`;
        
        console.log('初始化WebSocket连接:', wsUrl);
        
        this.socket = new WebSocket(wsUrl);
        
        // WebSocket连接成功
        this.socket.onopen = () => {
          console.log('房源聊天WebSocket连接成功');
          this.connected = true;
          
          // 连接成功后清除重连计时器
          if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
          }
          
          // 发送心跳
          this.startHeartbeat();
          
          // 如果有回调函数，执行
          if (callback) callback();
        };
        
        // 接收消息
        this.socket.onmessage = (event) => {
          console.log('收到房源聊天消息:', event.data);
          this.handleMessage(event.data);
        };
        
        // 连接关闭
        this.socket.onclose = () => {
          console.log('房源聊天WebSocket连接关闭');
          this.connected = false;
          this.stopHeartbeat();
          
          // 尝试重连
          this.reconnect();
        };
        
        // 连接错误
        this.socket.onerror = (error) => {
          console.error('房源聊天WebSocket连接错误:', error);
          this.connected = false;
          
          // 提示用户可能需要重新登录
          this.$message({
            message: '连接失败，您的登录可能已过期，请重新登录',
            type: 'error',
            duration: 5000,
            showClose: true,
            onClose: () => {
              // 清除登录信息并跳转到登录页
              removeUser();
              this.$router.push('/frontend/login');
            }
          });
          
          // 连接错误时也尝试重连
          this.reconnect();
        };
      } catch (error) {
        console.error('初始化房源聊天WebSocket失败:', error);
        // 初始化失败时也尝试重连
        this.reconnect();
      }
    },
    reconnect() {
      // 如果已经设置了重连定时器，不重复设置
      if (this.reconnectTimer) {
        return;
      }
      
      // 3秒后重连
      this.reconnectTimer = setTimeout(() => {
        console.log('尝试重新连接房源聊天WebSocket...');
        this.initWebSocket();
        this.reconnectTimer = null;
      }, 3000);
    },
    startHeartbeat() {
      // 每15秒发送一次心跳，确保连接保持活跃
      this.heartbeatTimer = setInterval(() => {
        if (this.socket && this.socket.readyState === WebSocket.OPEN) {
          // 发送心跳消息
          this.socket.send(JSON.stringify({
            type: 'heartbeat',
            timestamp: Date.now()
          }));
        } else if (this.socket && this.socket.readyState !== WebSocket.CONNECTING) {
          // 如果连接已断开，尝试重新连接
          this.initWebSocket();
        }
      }, 15000);
    },
    stopHeartbeat() {
      if (this.heartbeatTimer) {
        clearInterval(this.heartbeatTimer);
        this.heartbeatTimer = null;
      }
    },
    closeWebSocket() {
      this.stopHeartbeat();
      
      // 清除重连定时器
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
      }
      
      // 关闭连接
      if (this.socket) {
        try {
          this.socket.close();
        } catch (e) {
          console.error("关闭房源聊天WebSocket连接时出错:", e);
        }
        this.socket = null;
        this.connected = false;
      }
    },
    handleMessage(message) {
      try {
        const msg = JSON.parse(message);
        
        // 处理心跳响应
        if (msg.type === 'heartbeat') {
          console.log('收到心跳响应');
          return;
        }
        
        // 处理聊天消息
        if (msg.type === 'chat') {
          // 如果是发给当前用户的消息，添加到消息列表
          if (msg.toId == this.userId) {
            // 不再在本地添加消息，而是重新加载所有消息
            this.loadHistoryMessages();
            
            // 如果聊天窗口没有展开，增加未读计数
            if (!this.isExpanded) {
              this.unreadCount++;
            } else {
              // 如果聊天窗口已展开，标记为已读
              this.markMessagesAsRead();
            }
          }
        }
      } catch (error) {
        console.error('解析消息失败:', error);
      }
    },
    // 处理页面可见性变化
    handleVisibilityChange() {
      if (document.visibilityState === 'visible') {
        // 页面变为可见时，检查连接状态并确保连接
        if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
          this.initWebSocket();
        }
        
        // 重新加载历史消息
        this.loadHistoryMessages();
      }
    },
    getMessageAvatar(message) {
      if (message.isSelf) {
        // 如果是自己发送的消息，使用用户头像
        return this.getImageUrl(this.userAvatar);
      } else {
        // 如果是对方发送的消息，优先使用消息中的fromUserAvatar，其次是landlordAvatar
        if (message.fromUserAvatar) {
          return this.getImageUrl(message.fromUserAvatar);
        } else {
          return this.getImageUrl(this.landlordAvatar);
        }
      }
    }
  }
}
</script>

<style scoped>
.chat-container {
  position: fixed;
  bottom: 0;
  right: 20px;
  width: 300px;
  background-color: #fff;
  border-radius: 8px 8px 0 0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: height 0.3s;
  overflow: hidden;
}

.chat-header {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  background-color: #409EFF;
  color: white;
  cursor: pointer;
}

.chat-header i {
  margin-right: 8px;
}

.chat-header .toggle-icon {
  margin-left: auto;
}

.unread-badge {
  background-color: #F56C6C;
  color: white;
  border-radius: 50%;
  min-width: 18px;
  height: 18px;
  line-height: 18px;
  text-align: center;
  font-size: 12px;
  margin-left: 8px;
  padding: 0 5px;
}

.chat-body {
  height: 400px;
  display: flex;
  flex-direction: column;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.message {
  display: flex;
  margin-bottom: 15px;
}

.message-self {
  flex-direction: row-reverse;
}

.message-avatar {
  margin: 0 8px;
}

.avatar-img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.message-content {
  max-width: 70%;
}

.message-self .message-content {
  text-align: right;
}

.message-sender {
  font-size: 12px;
  color: #999;
  margin-bottom: 4px;
}

.message-bubble {
  background-color: #f5f5f5;
  padding: 8px 12px;
  border-radius: 12px;
  word-break: break-word;
}

.message-self .message-bubble {
  background-color: #e1f3ff;
}

.message-other .message-bubble {
  background-color: #fff;
  color: #303133;
  border-radius: 0 4px 4px 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.message-time {
  font-size: 10px;
  color: #999;
  margin-top: 4px;
}

.chat-input {
  padding: 10px;
  border-top: 1px solid #eee;
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.login-tip {
  font-size: 12px;
  color: #999;
}

.right-actions {
  display: flex;
  gap: 8px;
}

.no-messages {
  text-align: center;
  color: #999;
  padding: 20px 0;
}

.no-messages i {
  font-size: 40px;
  margin-bottom: 10px;
}

.chat-expanded {
  height: 500px;
}
</style> 