<template>
  <el-container class="backstage-container">
    <!-- 侧边栏组件 -->
    <sidebar :is-collapsed="isCollapse" />
    
    <!-- 主容器 -->
    <el-container class="main-container">
      <!-- 顶部导航栏组件 -->
      <app-header @toggle-sidebar="toggleSidebar" />
      
      <!-- 主内容区域 -->
      <el-main>
        <router-view></router-view>
      </el-main>
      
      <!-- WebSocket通知组件 -->
      <backstage-websocket-notification />
    </el-container>
  </el-container>
</template>

<script>
import Sidebar from './components/Sidebar'
import AppHeader from './components/Header'
import BackstageWebsocketNotification from './components/WebSocketNotification'
import { getBackstageUser, isAdmin, isOwner } from '../utils/auth'

export default {
  name: 'BackstageLayout',
  components: {
    Sidebar,
    AppHeader,
    BackstageWebsocketNotification
  },
  data() {
    return {
      isCollapse: false,
      wsCheckInterval: null
    }
  },
  created() {
    // 检查用户权限，如果既不是管理员也不是房东，则跳转到前台首页
    this.checkPermission();
    
    // 检查并确保WebSocket连接
    this.ensureWebSocketConnection();
    
    // 设置定时器，定期检查WebSocket连接
    this.wsCheckInterval = setInterval(this.ensureWebSocketConnection, 30000);
  },
  beforeDestroy() {
    // 清除定时器
    if (this.wsCheckInterval) {
      clearInterval(this.wsCheckInterval);
      this.wsCheckInterval = null;
    }
  },
  methods: {
    // 检查用户权限
    checkPermission() {
      const isAdminUser = isAdmin(false);
      const isOwnerUser = isOwner(false);
      const userInfo = getBackstageUser();

      console.log('Layout权限检查:');
      console.log('isAdmin(false):', isAdminUser);
      console.log('isOwner(false):', isOwnerUser);
      console.log('后台用户信息:', userInfo);

      // 检查是否是管理员或房东
      if (!isAdminUser && !isOwnerUser) {
        // 既不是管理员也不是房东，跳转到前台首页
        console.log('Layout: 用户没有权限访问后台管理系统');
        this.$message.error('您没有权限访问后台管理系统');
        this.$router.push('/frontend/home');
      }
    },
    
    // 切换侧边栏折叠状态
    toggleSidebar() {
      this.isCollapse = !this.isCollapse;
    },
    
    // 确保WebSocket连接
    ensureWebSocketConnection() {
      const userInfo = getBackstageUser();
      if (!userInfo || !userInfo.token || !userInfo.userInfo || !userInfo.userInfo.id) {
        return;
      }
      
      const userId = userInfo.userInfo.id;
      
      // 检查WebSocket实例是否存在
      if (!this.$root.$data.backstageWebSocket || this.$root.$data.backstageWebSocket.readyState !== WebSocket.OPEN) {
        // 如果不存在或未连接，则初始化WebSocket连接
        this.initBackstageWebSocket(userId);
      }
    },
    
    // 初始化WebSocket连接
    initBackstageWebSocket(userId) {
      // 获取token
      const userInfo = getBackstageUser();
      const token = userInfo ? userInfo.token : '';
      
      // 如果token为空，不初始化WebSocket
      if (!token) {
        console.error('无法初始化WebSocket：token为空');
        return;
      }
      
      // 使用相对路径，而不是硬编码的localhost
      const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
      const host = window.location.host;
      const encodedToken = encodeURIComponent(token);
      
      // 使用前台相同的WebSocket连接方式
      const wsUrl = `${wsProtocol}//${host}/ws/chat/${userId}?houseId=0&landlordId=${userId}&token=${encodedToken}`;
      
      console.log('初始化后台WebSocket连接:', wsUrl);
      
      // 将WebSocket实例存储在全局Vue实例中，方便其他组件访问
      this.$root.$data.backstageWebSocket = new WebSocket(wsUrl);
      
      // 连接成功回调
      this.$root.$data.backstageWebSocket.onopen = () => {
        console.log('后台WebSocket连接已建立');
      };
      
      // 连接错误回调
      this.$root.$data.backstageWebSocket.onerror = (error) => {
        console.error('后台WebSocket连接错误:', error);
      };
      
      // 连接关闭回调
      this.$root.$data.backstageWebSocket.onclose = () => {
        console.log('后台WebSocket连接已关闭');
        
        // 连接关闭后，延迟5秒尝试重新连接
        setTimeout(() => {
          this.ensureWebSocketConnection();
        }, 5000);
      };
    }
  }
}
</script>

<style scoped>
.backstage-container {
  height: 100vh;
  width: 100%;
}

.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.el-main {
  padding: 20px;
  overflow-y: auto;
  background-color: #f0f2f5;
  height: calc(100vh - 60px); /* 减去顶部导航栏高度 */
}
</style> 