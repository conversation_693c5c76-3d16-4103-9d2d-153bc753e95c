package com.house.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.house.dto.HousePageQueryDTO;
import com.house.dto.HouseQueryDTO;
import com.house.mapper.HouseMapper;
import com.house.pojo.House;
import com.house.service.HouseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 房源服务实现类
 */
@Service
public class HouseServiceImpl implements HouseService {
    
    @Autowired
    private HouseMapper houseMapper;
    
    @Override
    public List<House> getAllHouseList() {
        return houseMapper.getAllHouseList();
    }
    
    @Override
    public List<House> getHotHouseList(int limit) {
        return houseMapper.getHotHouseList(limit);
    }
    
    @Override
    public List<House> getHouseListByCondition(HouseQueryDTO queryDTO) {
        // 处理价格范围
        if (queryDTO.getMinPrice() == null && queryDTO.getMaxPrice() == null && queryDTO.getStatus() == null) {
            if (queryDTO.getKeyword() != null && queryDTO.getKeyword().contains("-")) {
                String[] priceRange = queryDTO.getKeyword().split("-");
                if (priceRange.length == 2) {
                    try {
                        double minPrice = Double.parseDouble(priceRange[0]);
                        double maxPrice = Double.parseDouble(priceRange[1]);
                        queryDTO.setMinPrice(minPrice);
                        queryDTO.setMaxPrice(maxPrice);
                    } catch (NumberFormatException e) {
                        // 忽略转换异常
                    }
                }
            }
        }
        return houseMapper.getHouseListByCondition(queryDTO);
    }
    
    @Override
    public House getHouseDetail(Integer houseId) {
        // 获取房源详情时不增加点击量，需要单独调用 increaseViewCount 方法
        return houseMapper.getHouseDetail(houseId);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean increaseViewCount(Integer houseId) {
        return houseMapper.increaseViewCount(houseId) > 0;
    }
    
    @Override
    public List<House> getHouseListByUserId(Integer userId) {
        return houseMapper.getHouseListByUserId(userId);
    }
    
    @Override
    public PageInfo<House> getHouseListByOwnerIdPage(HousePageQueryDTO queryDTO) {
        // 设置分页参数
        PageHelper.startPage(queryDTO.getPageNum(), queryDTO.getPageSize());
        
        // 构建查询条件
        HouseQueryDTO condition = new HouseQueryDTO();
        condition.setOwnerId(queryDTO.getOwnerId());
        condition.setStatus(queryDTO.getStatus());
        
        // 如果地址不为空，添加到查询条件
        if (queryDTO.getAddress() != null && !queryDTO.getAddress().isEmpty()) {
            condition.setKeyword(queryDTO.getAddress());
        }
        
        // 查询数据
        List<House> houseList = houseMapper.getHouseListByCondition(condition);
        
        // 封装分页信息
        return new PageInfo<>(houseList);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addHouse(House house) {
        return houseMapper.addHouse(house) > 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateHouse(House house) {
        return houseMapper.updateHouse(house) > 0;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteHouse(Integer houseId) {
        return houseMapper.deleteHouse(houseId) > 0;
    }
} 