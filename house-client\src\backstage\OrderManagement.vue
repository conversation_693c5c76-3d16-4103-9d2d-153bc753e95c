<template>
  <div class="order-management">
    <div class="search-form">
      <el-form :inline="true" :model="searchForm">
        <el-form-item label="订单号">
          <el-input v-model="searchForm.orderNo" placeholder="请输入订单号"></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态">
            <el-option label="全部" value=""></el-option>
            <el-option label="待审核" :value="ORDER_STATUS.APPLICATION"></el-option>
            <el-option label="申请已批准" :value="ORDER_STATUS.APPROVED"></el-option>
            <el-option label="待签署合同" :value="ORDER_STATUS.CONTRACT_PENDING"></el-option>
            <el-option label="待支付押金" :value="ORDER_STATUS.UNPAID"></el-option>
            <el-option label="待入住" :value="ORDER_STATUS.PENDING"></el-option>
            <el-option label="租赁中" :value="ORDER_STATUS.RENTING"></el-option>
            <el-option label="已完成" :value="ORDER_STATUS.COMPLETED"></el-option>
            <el-option label="已取消" :value="ORDER_STATUS.CANCELLED"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%">
      <el-table-column prop="orderNo" label="订单号" width="180"></el-table-column>
      <el-table-column prop="houseAddress" label="房源地址" min-width="200"></el-table-column>
      <el-table-column prop="tenantName" label="租客" width="100"></el-table-column>
      <el-table-column prop="totalAmount" label="总金额" width="120">
        <template slot-scope="scope">
          ¥{{ scope.row.totalAmount }}
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="创建时间" width="160">
        <template slot-scope="scope">
          {{ formatDate(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.status)">
            {{ getStatusText(scope.row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="250" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="primary" @click="handleDetail(scope.row)">详情</el-button>
          <el-button
            size="mini"
            type="success"
            v-if="scope.row.status === ORDER_STATUS.COMPLETED && !scope.row.hasOwnerReview"
            @click="handleReview(scope.row)">评价租客</el-button>
          <el-button
            size="mini"
            type="danger"
            v-if="scope.row.status === ORDER_STATUS.UNPAID"
            @click="handleCancel(scope.row)">取消订单</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import orderApi from '../api/order.js'
import { getBackstageUser } from '../utils/auth'
import { OrderStatusUtils, ORDER_STATUS } from '../constants/orderStatus'

export default {
  name: 'OrderManagement',
  data() {
    return {
      loading: false,
      searchForm: {
        orderNo: '',
        status: ''
      },
      tableData: [],
      allData: [],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      userId: '',
      ORDER_STATUS // 添加状态常量供模板使用
    }
  },
  created() {
    const userInfo = getBackstageUser()
    if (userInfo && userInfo.id) {
      this.userId = userInfo.id
      this.fetchData()
    }
  },
  methods: {
    formatDate(timestamp) {
      if (!timestamp) return ''
      const date = new Date(timestamp)
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    },
    getStatusType(status) {
      return OrderStatusUtils.getStatusType(status)
    },

    getStatusText(status) {
      return OrderStatusUtils.getStatusText(status)
    },
    fetchData() {
      this.loading = true
      
      // 获取当前房东的订单列表
      orderApi.getOrderListByOwnerId(this.userId).then(res => {
        if (res.data.flag) {
          this.allData = res.data.data
          this.pagination.total = this.allData.length
          this.handleCurrentChange(1)
        } else {
          this.$message.error('获取订单列表失败')
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
        this.$message.error('获取订单列表失败')
      })
    },
    handleSearch() {
      this.loading = true
      const params = {
        orderNo: this.searchForm.orderNo,
        status: this.searchForm.status,
        ownerId: this.userId
      }
      
      orderApi.getOrderListByCondition(params).then(res => {
        if (res.data.flag) {
          this.allData = res.data.data
          this.pagination.total = this.allData.length
          this.handleCurrentChange(1)
          this.$message.success('筛选成功')
        } else {
          this.$message.error('筛选失败')
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
        this.$message.error('筛选失败')
      })
    },
    resetForm() {
      this.searchForm = {
        orderNo: '',
        status: ''
      }
      this.fetchData()
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.handleCurrentChange(this.pagination.currentPage)
    },
    handleCurrentChange(val) {
      this.pagination.currentPage = val
      const start = (val - 1) * this.pagination.pageSize
      const end = start + this.pagination.pageSize
      this.tableData = this.allData.slice(start, end)
    },
    handleDetail(row) {
      this.$router.push(`/frontend/orderdetail/${row.orderNo}`)
    },
    handleReview(row) {
      this.$router.push(`/backstage/tenant-review/${row.orderId}`)
    },
    handleCancel(row) {
      this.$confirm('确认取消该订单?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        orderApi.cancelOrder(row.orderNo).then(res => {
          if (res.data.flag) {
            this.$message.success('订单取消成功')
            this.fetchData()
          } else {
            this.$message.error('订单取消失败')
          }
        }).catch(() => {
          this.$message.error('订单取消失败')
        })
      }).catch(() => {})
    }
  }
}
</script>

<style scoped>
.order-management {
  padding: 20px;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 