<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.house.mapper.HouseMapper">
    
    <!-- 房源结果映射 -->
    <resultMap id="HouseResultMap" type="com.house.pojo.House">
        <id column="house_id" property="houseId"/>
        <result column="address" property="address"/>
        <result column="price" property="price"/>
        <result column="status" property="status"/>
        <result column="detail" property="detail"/>
        <result column="image_url" property="imageUrl"/>
        <result column="owner_id" property="ownerId"/>
        <result column="owner_name" property="ownerName"/>
        <result column="area" property="area"/>
        <result column="room_num" property="roomNum"/>
        <result column="house_area" property="houseArea"/>
        <result column="decoration" property="decoration"/>
        <result column="view_count" property="viewCount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>
    
    <!-- 基础列 -->
    <sql id="Base_Column_List">
        house_id, address, price, status, detail, image_url, owner_id, owner_name, 
        area, room_num, house_area, decoration, view_count, create_time, update_time
    </sql>
    
    <!-- 获取所有房源列表 -->
    <select id="getAllHouseList" resultMap="HouseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM house
        ORDER BY create_time DESC
    </select>
    
    <!-- 获取热门房源列表 -->
    <select id="getHotHouseList" resultMap="HouseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM house
        WHERE status = '可租'
        ORDER BY view_count DESC, create_time DESC
        LIMIT #{limit}
    </select>
    
    <!-- 根据条件查询房源 -->
    <select id="getHouseListByCondition" parameterType="com.house.dto.HouseQueryDTO" resultMap="HouseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM house
        <where>
            <if test="keyword != null and keyword != ''">
                AND (
                    address LIKE CONCAT('%', #{keyword}, '%')
                    OR detail LIKE CONCAT('%', #{keyword}, '%')
                )
            </if>
            <if test="minPrice != null">
                AND price >= #{minPrice}
            </if>
            <if test="maxPrice != null">
                AND price &lt;= #{maxPrice}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="area != null and area != ''">
                AND area = #{area}
            </if>
            <if test="ownerId != null">
                AND owner_id = #{ownerId}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
    
    <!-- 获取房源详情 -->
    <select id="getHouseDetail" parameterType="java.lang.Integer" resultMap="HouseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM house
        WHERE house_id = #{houseId}
    </select>
    
    <!-- 获取用户发布的房源 -->
    <select id="getHouseListByUserId" parameterType="java.lang.Integer" resultMap="HouseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM house
        WHERE owner_id = #{userId}
        ORDER BY create_time DESC
    </select>
    
    <!-- 增加点击量 -->
    <update id="increaseViewCount" parameterType="java.lang.Integer">
        UPDATE house
        SET view_count = IFNULL(view_count, 0) + 1
        WHERE house_id = #{houseId}
    </update>
    
    <!-- 添加房源 -->
    <insert id="addHouse" parameterType="com.house.pojo.House" useGeneratedKeys="true" keyProperty="houseId">
        INSERT INTO house (
            address, price, status, detail, image_url, owner_id, owner_name,
            area, room_num, house_area, decoration, view_count, create_time, update_time
        )
        VALUES (
            #{address}, #{price}, #{status}, #{detail}, #{imageUrl}, #{ownerId}, #{ownerName},
            #{area}, #{roomNum}, #{houseArea}, #{decoration}, 0, NOW(), NOW()
        )
    </insert>
    
    <!-- 更新房源信息 -->
    <update id="updateHouse" parameterType="com.house.pojo.House">
        UPDATE house
        <set>
            <if test="address != null">address = #{address},</if>
            <if test="price != null">price = #{price},</if>
            <if test="status != null">status = #{status},</if>
            <if test="detail != null">detail = #{detail},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="area != null">area = #{area},</if>
            <if test="roomNum != null">room_num = #{roomNum},</if>
            <if test="houseArea != null">house_area = #{houseArea},</if>
            <if test="decoration != null">decoration = #{decoration},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            update_time = NOW()
        </set>
        WHERE house_id = #{houseId}
    </update>
    
    <!-- 删除房源 -->
    <delete id="deleteHouse" parameterType="java.lang.Integer">
        DELETE FROM house
        WHERE house_id = #{houseId}
    </delete>
</mapper> 