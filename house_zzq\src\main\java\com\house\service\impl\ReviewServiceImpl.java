package com.house.service.impl;

import com.house.mapper.OrderMapper;
import com.house.mapper.ReviewMapper;
import com.house.pojo.Review;
import com.house.service.ReviewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 评价服务实现类
 */
@Service
public class ReviewServiceImpl implements ReviewService {

    @Autowired
    private ReviewMapper reviewMapper;

    @Autowired
    private OrderMapper orderMapper;

    /**
     * 提交评价
     * @param review 评价对象
     * @return 评价对象
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Review addReview(Review review) {
        // 1. 插入评价
        reviewMapper.insertReview(review);

        // 2. 更新评价统计信息
        reviewMapper.updateReviewStats(review);

        // 3. 更新标签统计
        if (!StringUtils.isEmpty(review.getTags())) {
            String[] tags = review.getTags().split(",");
            for (String tag : tags) {
                if (!StringUtils.isEmpty(tag)) {
                    reviewMapper.updateTagStats(review.getHouseId(), tag);
                }
            }
        }

        // 4. 标记订单为已评价
        orderMapper.markAsReviewed(review.getOrderId());

        return review;
    }

    /**
     * 获取房屋评价统计信息
     * @param houseId 房屋ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> getReviewStats(Integer houseId) {
        // 获取评价统计信息
        Map<String, Object> stats = reviewMapper.selectReviewStats(houseId);
        
        if (stats == null) {
            // 如果没有评价，返回默认值
            stats = new HashMap<>();
            stats.put("houseId", houseId);
            stats.put("totalReviews", 0);
            stats.put("averageRating", 5.0);
            stats.put("locationRating", 5.0);
            stats.put("facilityRating", 5.0);
            stats.put("serviceRating", 5.0);
            stats.put("valueRating", 5.0);
            stats.put("environmentRating", 5.0);
            stats.put("cleanlinessRating", 5.0);
        }
        
        // 获取标签统计
        List<Map<String, Object>> tags = reviewMapper.selectTagStats(houseId);
        stats.put("tags", tags != null ? tags : Collections.emptyList());
        
        return stats;
    }

    /**
     * 获取房屋所有评价
     * @param houseId 房屋ID
     * @return 评价列表
     */
    @Override
    public List<Review> getReviewsByHouseId(Integer houseId) {
        return reviewMapper.selectReviewsByHouseId(houseId);
    }

    /**
     * 获取房东的所有评价
     * @param ownerId 房东ID
     * @return 评价列表
     */
    @Override
    public List<Review> getReviewsByOwnerId(Integer ownerId) {
        return reviewMapper.selectReviewsByOwnerId(ownerId);
    }

    /**
     * 房东回复评价
     * @param reviewId 评价ID
     * @param reply 回复内容
     * @return 是否成功
     */
    @Override
    public boolean replyReview(Integer reviewId, String reply) {
        return reviewMapper.replyReview(reviewId, reply) > 0;
    }

    /**
     * 删除评价
     * @param reviewId 评价ID
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteReview(Integer reviewId) {
        // TODO: 删除评价时需要更新统计信息，这里简化处理，只删除评价
        return reviewMapper.deleteReview(reviewId) > 0;
    }

    /**
     * 检查用户是否已评价订单
     * @param orderId 订单ID
     * @return 是否已评价
     */
    @Override
    public boolean hasReviewed(Integer orderId) {
        Review review = reviewMapper.selectReviewByOrderId(orderId);
        return review != null;
    }
} 