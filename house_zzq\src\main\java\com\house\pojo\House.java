package com.house.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 房源实体类
 */
@Data
public class House implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 房源ID
     */
    private Integer houseId;
    
    /**
     * 房源地址
     */
    private String address;
    
    /**
     * 租金价格
     */
    private Double price;
    
    /**
     * 房源状态（可租、已租）
     */
    private String status;
    
    /**
     * 房源详情
     */
    private String detail;
    
    /**
     * 房源图片URL
     */
    private String imageUrl;
    
    /**
     * 房东ID
     */
    private Integer ownerId;
    
    /**
     * 房东名称
     */
    private String ownerName;
    
    /**
     * 房源区域
     */
    private String area;
    
    /**
     * 房间数量
     */
    private Integer roomNum;
    
    /**
     * 面积
     */
    private Double houseArea;
    
    /**
     * 装修情况
     */
    private String decoration;
    
    /**
     * 点击量
     */
    private Integer viewCount;
    
    /**
     * 发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
} 