package com.house.dao;

import com.house.pojo.ChatMessage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 聊天消息DAO接口
 */
@Mapper
public interface ChatMessageDao {
    /**
     * 保存聊天消息
     * @param chatMessage 聊天消息
     * @return 影响行数
     */
    int saveChatMessage(ChatMessage chatMessage);
    
    /**
     * 根据房源ID和用户ID获取聊天记录
     * @param houseId 房源ID
     * @param userId 用户ID
     * @param landlordId 房东ID
     * @return 聊天记录列表
     */
    List<ChatMessage> getChatHistory(@Param("houseId") Long houseId, 
                                    @Param("userId") Long userId, 
                                    @Param("landlordId") Long landlordId);
    
    /**
     * 将消息标记为已读
     * @param toUserId 接收者ID
     * @param fromUserId 发送者ID
     * @return 影响行数
     */
    int markMessagesAsRead(@Param("toUserId") Long toUserId, @Param("fromUserId") Long fromUserId);
    
    /**
     * 获取用户未读消息数量
     * @param userId 用户ID
     * @return 未读消息数量
     */
    int getUnreadMessageCount(@Param("userId") Long userId);
    
    /**
     * 获取房东的所有聊天消息
     * @param landlordId 房东ID
     * @return 聊天消息列表
     */
    List<ChatMessage> getLandlordMessages(@Param("landlordId") Long landlordId);
    
    /**
     * 删除聊天记录
     * @param houseId 房源ID
     * @param userId 用户ID
     * @param landlordId 房东ID
     * @return 影响行数
     */
    int deleteChatHistory(@Param("houseId") Long houseId, 
                         @Param("userId") Long userId, 
                         @Param("landlordId") Long landlordId);
} 