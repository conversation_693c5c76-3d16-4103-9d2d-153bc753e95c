package com.house.controller;

import com.house.pojo.Payment;
import com.house.service.PaymentService;
import com.house.common.Result;
import com.house.common.StatusCode;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 支付记录控制器
 */
@RestController
@RequestMapping("/payments")
@CrossOrigin(origins = "http://localhost:8080", allowCredentials = "true", maxAge = 3600)
@Slf4j
public class PaymentController {

    @Autowired
    private PaymentService paymentService;

    /**
     * 分页查询支付记录列表
     */
    @GetMapping
    public Result getPaymentList(@RequestParam Map<String, Object> params) {
        try {
            PageInfo<Payment> pageInfo = paymentService.getPaymentList(params);
            return new Result(true, StatusCode.SUCCESS, "获取支付记录列表成功", pageInfo);
        } catch (Exception e) {
            log.error("获取支付记录列表失败", e);
            return new Result(false, StatusCode.ERROR, "获取支付记录列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据订单ID获取支付记录列表
     */
    @GetMapping("/order/{orderId}")
    public Result getPaymentsByOrderId(@PathVariable Integer orderId) {
        try {
            List<Payment> payments = paymentService.getPaymentsByOrderId(orderId);
            return new Result(true, StatusCode.SUCCESS, "获取订单支付记录成功", payments);
        } catch (Exception e) {
            log.error("获取订单支付记录失败，订单ID: {}", orderId, e);
            return new Result(false, StatusCode.ERROR, "获取订单支付记录失败: " + e.getMessage());
        }
    }

    /**
     * 根据用户ID获取支付记录列表
     */
    @GetMapping("/user/{userId}")
    public Result getPaymentsByUserId(@PathVariable Integer userId) {
        try {
            List<Payment> payments = paymentService.getPaymentsByUserId(userId);
            return new Result(true, StatusCode.SUCCESS, "获取用户支付记录成功", payments);
        } catch (Exception e) {
            log.error("获取用户支付记录失败，用户ID: {}", userId, e);
            return new Result(false, StatusCode.ERROR, "获取用户支付记录失败: " + e.getMessage());
        }
    }

    /**
     * 根据支付单号获取支付记录
     */
    @GetMapping("/{paymentNo}")
    public Result getPaymentByPaymentNo(@PathVariable String paymentNo) {
        try {
            Payment payment = paymentService.getPaymentByPaymentNo(paymentNo);
            if (payment != null) {
                return new Result(true, StatusCode.SUCCESS, "获取支付记录成功", payment);
            } else {
                return new Result(false, StatusCode.ERROR, "支付记录不存在");
            }
        } catch (Exception e) {
            log.error("获取支付记录失败，支付单号: {}", paymentNo, e);
            return new Result(false, StatusCode.ERROR, "获取支付记录失败: " + e.getMessage());
        }
    }

    /**
     * 更新支付状态
     */
    @PutMapping("/{paymentNo}/status")
    public Result updatePaymentStatus(@PathVariable String paymentNo, 
                                    @RequestParam String status,
                                    @RequestParam(required = false) String transactionId) {
        try {
            boolean result = paymentService.updatePaymentStatus(paymentNo, status, transactionId);
            if (result) {
                return new Result(true, StatusCode.SUCCESS, "更新支付状态成功");
            } else {
                return new Result(false, StatusCode.ERROR, "更新支付状态失败");
            }
        } catch (Exception e) {
            log.error("更新支付状态失败，支付单号: {}", paymentNo, e);
            return new Result(false, StatusCode.ERROR, "更新支付状态失败: " + e.getMessage());
        }
    }

    /**
     * 退款处理
     */
    @PostMapping("/{paymentNo}/refund")
    public Result refundPayment(@PathVariable String paymentNo, 
                              @RequestParam String reason) {
        try {
            boolean result = paymentService.refundPayment(paymentNo, reason);
            if (result) {
                return new Result(true, StatusCode.SUCCESS, "退款处理成功");
            } else {
                return new Result(false, StatusCode.ERROR, "退款处理失败");
            }
        } catch (Exception e) {
            log.error("退款处理失败，支付单号: {}", paymentNo, e);
            return new Result(false, StatusCode.ERROR, "退款处理失败: " + e.getMessage());
        }
    }

    /**
     * 支付回调处理
     */
    @PostMapping("/callback")
    public Result handlePaymentCallback(@RequestParam String paymentNo,
                                      @RequestParam String transactionId,
                                      @RequestParam String status) {
        try {
            boolean result = paymentService.handlePaymentCallback(paymentNo, transactionId, status);
            if (result) {
                return new Result(true, StatusCode.SUCCESS, "支付回调处理成功");
            } else {
                return new Result(false, StatusCode.ERROR, "支付回调处理失败");
            }
        } catch (Exception e) {
            log.error("支付回调处理失败，支付单号: {}", paymentNo, e);
            return new Result(false, StatusCode.ERROR, "支付回调处理失败: " + e.getMessage());
        }
    }
}
