<template>
  <div class="user-reviews-container">
    <frontend-header></frontend-header>
    
    <div class="main-content">
      <div class="container">
        <el-card class="reviews-card">
          <div slot="header" class="clearfix">
            <span class="card-title">我的评价</span>
            <el-button
              style="float: right; padding: 3px 0"
              type="text"
              @click="fetchUserReviews"
            >
              <i class="el-icon-refresh"></i> 刷新
            </el-button>
          </div>
          
          <!-- 评价列表 -->
          <div v-loading="loading">
            <div v-if="reviews.length === 0" class="no-reviews">
              <el-empty description="您还没有发布过评价"></el-empty>
            </div>
            
            <div v-else>
              <div class="review-item" v-for="review in reviews" :key="review.id">
                <div class="review-header">
                  <div class="house-info">
                    <img 
                      :src="getHouseImage(review.house)" 
                      alt="房源图片"
                      class="house-image"
                    >
                    <div class="house-details">
                      <div class="house-title">{{ review.house ? review.house.address : '房源信息获取失败' }}</div>
                      <div class="review-time">评价时间：{{ formatDate(review.createTime) }}</div>
                    </div>
                  </div>
                  <div class="review-rating">
                    <el-rate
                      :value="review.rating"
                      disabled
                      text-color="#ff9900"
                      :colors="['#FF9900', '#FF9900', '#FF9900']"
                    ></el-rate>
                    <span class="rating-text">{{ review.rating }}分</span>
                  </div>
                </div>
                
                <div class="review-content">
                  <p>{{ review.content }}</p>
                  
                  <div class="review-tags" v-if="review.tags">
                    <el-tag 
                      size="mini" 
                      v-for="(tag, index) in review.tags.split(',')" 
                      :key="index" 
                      type="info"
                    >{{ tag }}</el-tag>
                  </div>
                  
                  <div class="review-images" v-if="review.images">
                    <el-image
                      v-for="(image, index) in review.images.split(',')"
                      :key="index"
                      :src="image"
                      :preview-src-list="review.images.split(',')"
                      fit="cover"
                      class="review-image"
                    ></el-image>
                  </div>
                </div>
                
                <div class="owner-reply" v-if="review.reply">
                  <div class="reply-header">
                    <i class="el-icon-chat-line-square"></i>
                    <span>房东回复：</span>
                    <span class="reply-time">{{ formatDate(review.replyTime) }}</span>
                  </div>
                  <div class="reply-content">{{ review.reply }}</div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 分页 -->
          <div class="pagination-container" v-if="total > pageSize">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage"
              :page-sizes="[10, 20, 50]"
              :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper"
              :total="total"
            ></el-pagination>
          </div>
        </el-card>
      </div>
    </div>
    
    <frontend-footer></frontend-footer>
  </div>
</template>

<script>
import FrontendHeader from './components/Header.vue'
import FrontendFooter from './components/Footer.vue'
import reviewApi from './api/review'
import { getUser } from '../utils/auth'
import { getImageUrl } from '../utils/imageUtils'
import moment from 'moment'

export default {
  name: 'UserReviews',
  components: {
    FrontendHeader,
    FrontendFooter
  },
  data() {
    return {
      loading: false,
      reviews: [],
      total: 0,
      currentPage: 1,
      pageSize: 10,
      userId: null
    }
  },
  created() {
    const userInfo = getUser(true)
    if (userInfo && userInfo.userInfo) {
      this.userId = userInfo.userInfo.id
      this.fetchUserReviews()
    } else {
      this.$message.error('请先登录')
      this.$router.push('/frontend/login')
    }
  },
  methods: {
    // 获取用户评价列表
    async fetchUserReviews() {
      if (!this.userId) return
      
      this.loading = true
      try {
        const res = await reviewApi.getUserReviews(this.userId)
        if (res.data && res.data.flag) {
          this.reviews = res.data.data || []
          this.total = this.reviews.length
          
          // 获取房源信息
          await this.fetchHouseInfo()
        } else {
          this.$message.error('获取评价列表失败')
        }
      } catch (error) {
        console.error('获取评价列表失败:', error)
        this.$message.error('获取评价列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 获取房源信息
    async fetchHouseInfo() {
      for (let i = 0; i < this.reviews.length; i++) {
        const review = this.reviews[i]
        if (!review.houseId) continue
        
        try {
          const houseRes = await this.$http.get(`/house/gethousedetail?houseId=${review.houseId}`)
          if (houseRes.data && houseRes.data.flag) {
            this.$set(this.reviews[i], 'house', houseRes.data.data)
          }
        } catch (error) {
          console.error(`获取房源信息失败:`, error)
        }
      }
    },
    
    // 获取房屋图片
    getHouseImage(house) {
      if (!house) return require('../assets/showcase.jpg')
      
      if (house.imageUrl) {
        return getImageUrl(house.imageUrl)
      }
      
      return getImageUrl('/img/showcase.jpg')
    },
    
    // 格式化日期
    formatDate(dateStr) {
      return moment(dateStr).format('YYYY-MM-DD HH:mm')
    },
    
    // 处理页大小变化
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchUserReviews()
    },
    
    // 处理页码变化
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchUserReviews()
    }
  }
}
</script>

<style scoped>
.user-reviews-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.main-content {
  padding: 20px 0;
  min-height: calc(100vh - 140px);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.reviews-card {
  margin-bottom: 20px;
}

.card-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
}

.no-reviews {
  text-align: center;
  padding: 40px 0;
}

.review-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  background-color: #fff;
}

.review-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.house-info {
  display: flex;
  align-items: center;
}

.house-image {
  width: 80px;
  height: 60px;
  border-radius: 4px;
  margin-right: 15px;
  object-fit: cover;
}

.house-details {
  flex: 1;
}

.house-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.review-time {
  font-size: 12px;
  color: #999;
}

.review-rating {
  display: flex;
  align-items: center;
}

.rating-text {
  margin-left: 10px;
  font-weight: bold;
  color: #ff9900;
}

.review-content p {
  margin: 0 0 15px 0;
  line-height: 1.6;
  color: #666;
}

.review-tags {
  margin-bottom: 15px;
}

.review-tags .el-tag {
  margin-right: 8px;
  margin-bottom: 5px;
}

.review-images {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.review-image {
  width: 100px;
  height: 100px;
  border-radius: 4px;
}

.owner-reply {
  margin-top: 15px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  border-left: 4px solid #409eff;
}

.reply-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 14px;
  color: #666;
}

.reply-header i {
  margin-right: 5px;
  color: #409eff;
}

.reply-time {
  margin-left: auto;
  font-size: 12px;
  color: #999;
}

.reply-content {
  color: #333;
  line-height: 1.6;
}

.pagination-container {
  margin-top: 20px;
  text-align: center;
}
</style>
