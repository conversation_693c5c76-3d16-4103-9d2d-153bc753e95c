-- 订单表
CREATE TABLE IF NOT EXISTS `tb_order` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` VARCHAR(32) NOT NULL COMMENT '订单编号',
  `house_id` INT NOT NULL COMMENT '房源ID',
  `tenant_id` INT NOT NULL COMMENT '租客ID',
  `owner_id` INT NOT NULL COMMENT '房东ID',
  `start_date` DATE NOT NULL COMMENT '开始日期',
  `end_date` DATE NOT NULL COMMENT '结束日期',
  `duration` INT NOT NULL COMMENT '租期（月）',
  `monthly_price` DECIMAL(10,2) NOT NULL COMMENT '月租金',
  `deposit` DECIMAL(10,2) NOT NULL COMMENT '押金',
  `service_fee` DECIMAL(10,2) DEFAULT 0.00 COMMENT '服务费',
  `total_price` DECIMAL(10,2) NOT NULL COMMENT '总价',
  `status` VARCHAR(20) NOT NULL COMMENT '订单状态：application-租房申请，approved-申请已批准，contract_pending-待签署合同，unpaid-待支付，pending-待入住，renting-租赁中，completed-已完成，cancelled-已取消',
  `deposit_pay_time` DATETIME DEFAULT NULL COMMENT '押金支付时间',
  `contract_sign_time` DATETIME DEFAULT NULL COMMENT '合同签署时间',
  `check_in_time` DATETIME DEFAULT NULL COMMENT '入住时间',
  `reviewed` TINYINT(1) DEFAULT 0 COMMENT '是否已评价',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  INDEX `idx_house_id` (`house_id`),
  INDEX `idx_tenant_id` (`tenant_id`),
  INDEX `idx_owner_id` (`owner_id`),
  INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 合同表
CREATE TABLE IF NOT EXISTS `tb_contract` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '合同ID',
  `contract_no` VARCHAR(32) NOT NULL COMMENT '合同编号',
  `order_id` INT NOT NULL COMMENT '关联的订单ID',
  `content` TEXT COMMENT '合同内容',
  `tenant_id` INT NOT NULL COMMENT '租客ID',
  `owner_id` INT NOT NULL COMMENT '房东ID',
  `tenant_signed` TINYINT(1) DEFAULT 0 COMMENT '租客是否已签署',
  `tenant_sign_time` DATETIME DEFAULT NULL COMMENT '租客签署时间',
  `owner_signed` TINYINT(1) DEFAULT 0 COMMENT '房东是否已签署',
  `owner_sign_time` DATETIME DEFAULT NULL COMMENT '房东签署时间',
  `status` VARCHAR(20) NOT NULL COMMENT '合同状态：pending-待签署，active-生效中，expired-已过期，cancelled-已取消',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_contract_no` (`contract_no`),
  INDEX `idx_order_id` (`order_id`),
  INDEX `idx_tenant_id` (`tenant_id`),
  INDEX `idx_owner_id` (`owner_id`),
  INDEX `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合同表';

-- 支付记录表
CREATE TABLE IF NOT EXISTS `tb_payment` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '支付ID',
  `payment_no` VARCHAR(32) NOT NULL COMMENT '支付单号',
  `order_id` INT NOT NULL COMMENT '订单ID',
  `user_id` INT NOT NULL COMMENT '用户ID',
  `amount` DECIMAL(10,2) NOT NULL COMMENT '支付金额',
  `type` VARCHAR(20) NOT NULL COMMENT '支付类型：deposit-押金，rent-租金，service-服务费，other-其他',
  `method` VARCHAR(20) NOT NULL COMMENT '支付方式：alipay-支付宝，wechat-微信，bank-银行卡，other-其他',
  `status` VARCHAR(20) NOT NULL COMMENT '支付状态：pending-待支付，success-支付成功，failed-支付失败，refunded-已退款',
  `transaction_id` VARCHAR(64) DEFAULT NULL COMMENT '交易流水号',
  `pay_time` DATETIME DEFAULT NULL COMMENT '支付时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_payment_no` (`payment_no`),
  INDEX `idx_order_id` (`order_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_status` (`status`),
  INDEX `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付记录表';

-- 消息通知表
CREATE TABLE IF NOT EXISTS `tb_notification` (
  `id` INT NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `user_id` INT NOT NULL COMMENT '接收用户ID',
  `title` VARCHAR(100) NOT NULL COMMENT '通知标题',
  `content` VARCHAR(500) NOT NULL COMMENT '通知内容',
  `type` VARCHAR(20) NOT NULL COMMENT '通知类型：info-普通信息，success-成功信息，warning-警告信息，error-错误信息，order-订单通知，contract-合同通知，payment-支付通知',
  `link_id` VARCHAR(32) DEFAULT NULL COMMENT '关联ID，如订单ID、合同ID等',
  `link_type` VARCHAR(20) DEFAULT NULL COMMENT '关联类型：order-订单，contract-合同，payment-支付，house-房源',
  `read` TINYINT(1) DEFAULT 0 COMMENT '是否已读',
  `read_time` DATETIME DEFAULT NULL COMMENT '已读时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_type` (`type`),
  INDEX `idx_read` (`read`),
  INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='消息通知表'; 