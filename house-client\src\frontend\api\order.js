import request from '../../utils/request'

const api_name = 'order'

// 导出单独函数
export function getOrderList(params) {
  return request({
    url: `/orders`,
    method: 'get',
    params
  })
}

export function getOrderByOrderNo(orderNo) {
  return request({
    url: `/orders/${orderNo}`,
    method: 'get'
  })
}

export function getOrderStatsByUserId(userId) {
  return request({
    url: `/orders/stats/${userId}`,
    method: 'get'
  })
}

export function cancelOrder(orderNo) {
  return request({
    url: `/orders/${orderNo}/cancel`,
    method: 'put'
  })
}

export function payOrder(data) {
  return request({
    url: `/orders/pay`,
    method: 'post',
    data
  })
}

// 默认导出
export default {
  // 获取订单列表
  getOrderList() {
    return request({
      url: `/${api_name}/list`,
      method: 'get'
    })
  },

  // 获取用户订单列表
  getUserOrders(userId) {
    return request({
      url: `/${api_name}/user/${userId}`,
      method: 'get'
    })
  },

  // 获取订单详情
  getOrderDetail(orderId) {
    return request({
      url: `/${api_name}/detail/${orderId}`,
      method: 'get'
    })
  },

  // 创建租房申请
  createRentApplication(data) {
    return request({
      url: `/orders`,
      method: 'post',
      data
    })
  },

  // 取消订单
  cancelOrder(orderId) {
    return request({
      url: `/${api_name}/cancel/${orderId}`,
      method: 'post'
    })
  },

  // 支付订单
  payOrder(orderId, paymentData) {
    return request({
      url: `/${api_name}/pay/${orderId}`,
      method: 'post',
      data: paymentData
    })
  },

  // 确认收到房屋/钥匙
  confirmReceive(orderId) {
    return request({
      url: `/${api_name}/confirm/${orderId}`,
      method: 'post'
    })
  },

  // 评价订单
  rateOrder(orderId, ratingData) {
    return request({
      url: `/${api_name}/rate/${orderId}`,
      method: 'post',
      data: ratingData
    })
  }
} 