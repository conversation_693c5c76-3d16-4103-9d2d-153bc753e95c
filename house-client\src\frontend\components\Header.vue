<template>
  <header class="home-header" :class="{ 'scrolled': isScrolled }">
    <div class="logo">
      <div class="logo-container">
        <img src="../../assets/houselogo.png" alt="房屋租赁" />
        <div class="platform-title-wrapper">
          <span class="platform-title">房屋租赁平台</span>
        </div>
      </div>
    </div>
    <div class="nav-menu">
      <el-menu mode="horizontal" :default-active="activeIndex" class="menu">
        <el-menu-item @click="navigateTo('/frontend/home')">首页</el-menu-item>
        <el-menu-item @click="navigateTo('/frontend/houselist')">房源列表</el-menu-item>
        <el-menu-item v-if="isOwner" @click="navigateTo('/frontend/usercenter')">我的房源</el-menu-item>
        <el-menu-item @click="navigateTo('/frontend/home')">租房资讯</el-menu-item>
        <el-menu-item @click="navigateTo('/frontend/home')">关于我们</el-menu-item>
      </el-menu>
    </div>
    <div class="user-actions right-side-btn">
      <!-- 未登录状态 -->
      <template v-if="!isLoggedIn">
        <el-button type="text" @click="goToLogin" class="btn-text">登录</el-button>
        <el-divider direction="vertical"></el-divider>
        <el-button type="primary" @click="goToRegister" class="btn-primary">注册</el-button>
      </template>
      
      <!-- 已登录状态 -->
      <template v-else>
        <!-- 消息通知图标 -->
        <div class="notification-icon" @click="goToMessageCenter">
          <i class="el-icon-bell"></i>
          <el-badge v-if="unreadCount > 0" :value="unreadCount" class="message-badge"></el-badge>
        </div>
        
        <el-dropdown trigger="click" @command="handleCommand">
          <span class="user-dropdown-link">
            <div class="user-avatar" :style="{ backgroundImage: `url(${processedAvatar})` }"></div>
            <span class="username">{{ userName }}</span>
            <i class="el-icon-arrow-down el-icon--right"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="userCenter">个人中心</el-dropdown-item>
            <el-dropdown-item command="orders">我的订单</el-dropdown-item>
            <el-dropdown-item command="reviews">我的评价</el-dropdown-item>
            <el-dropdown-item command="settings">账户设置</el-dropdown-item>
            <el-dropdown-item command="backstage" v-if="isAdmin || isOwner" divided>
              <i class="el-icon-s-platform"></i> 后台管理
            </el-dropdown-item>
            <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
   
    </div>
    
    <!-- 消息通知组件 -->
    <WebSocketNotification v-if="isLoggedIn" ref="notification" @update-count="updateUnreadCount" />
  </header>
</template>

<script>
import { getFrontendUser, removeFrontendUser } from '../../utils/auth'
import WebSocketNotification from './WebSocketNotification.vue'
import { getImageUrl } from '../../utils/imageUtils'

export default {
  name: "FrontendHeader",
  components: {
    WebSocketNotification
  },
  props: {
    activeIndex: {
      type: String,
      default: '/frontend/home'
    }
  },
  data() {
    return {
      isLoggedIn: false,
      userName: '',
      userAvatar: '',
      defaultAvatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
      isOwner: false,
      isAdmin: false,
      unreadCount: 0,
      isScrolled: false
    }
  },
  computed: {
    processedAvatar() {
      return this.userAvatar ? getImageUrl(this.userAvatar) : this.defaultAvatar;
    }
  },
  created() {
    // 检查用户是否已登录
    this.checkLoginStatus();
  },
  mounted() {
    // 添加滚动事件监听
    window.addEventListener('scroll', this.handleScroll);
  },
  beforeDestroy() {
    // 移除滚动事件监听，避免内存泄漏
    window.removeEventListener('scroll', this.handleScroll);
  },
  methods: {
    handleScroll() {
      // 当滚动超过50px时，添加scrolled类
      this.isScrolled = window.scrollY > 50;
    },
    checkLoginStatus() {
      const userData = getFrontendUser();
      if (userData && userData.token) {
        this.isLoggedIn = true;
        this.userName = userData.userInfo.name || userData.userInfo.username || '用户';
        this.userAvatar = userData.userInfo.avatar || '';
        console.log('用户头像URL:', this.userAvatar);
        
        // 判断用户角色
        const userType = userData.userInfo.type;
        // 假设type=3表示房东，type=1表示管理员
        this.isOwner = userType === 3;
        this.isAdmin = userType === 1;
      } else {
        this.isLoggedIn = false;
      }
    },
    navigateTo(path) {
      // 统一处理所有导航
      window.location.href = path;
      console.log('Navigating to:', path);
    },
    goToLogin() {
      this.navigateTo('/frontend/login');
    },
    goToRegister() {
      this.navigateTo('/frontend/register');
    },
    goToMessageCenter() {
      this.navigateTo('/frontend/messagecenter');
    },
    handleCommand(command) {
      switch(command) {
        case 'userCenter':
          this.navigateTo('/frontend/usercenter');
          break;
        case 'orders':
          this.navigateTo('/frontend/orderlist');
          break;
        case 'reviews':
          this.navigateTo('/frontend/userreviews');
          break;
        case 'settings':
          this.navigateTo('/frontend/usercenter');
          break;
        case 'backstage':
          this.navigateTo('/backstage');
          break;
        case 'logout':
          this.logout();
          break;
        default:
          break;
      }
    },
    logout() {
      this.$confirm('确认退出登录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 清除本地存储的用户信息
        removeFrontendUser();
        
        // 重新设置登录状态
        this.isLoggedIn = false;
        this.userName = '';
        this.userAvatar = '';
        
        // 提示用户
        this.$message.success('已成功退出登录');
        
        // 跳转到首页
        this.navigateTo('/frontend/home');
      }).catch(() => {
        // 取消退出
      });
    },
    updateUnreadCount(count) {
      this.unreadCount = count;
    }
  }
};
</script>

<style scoped>
/* 头部样式 */
.home-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 70px;
  /* 使用更现代的渐变色 */
  background: linear-gradient(90deg, #3a7bd5 0%, #00d2ff 100%);
  /* 添加更立体的阴影效果 */
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  width: 100%;
  box-sizing: border-box;
  /* 添加过渡效果 */
  transition: all 0.3s ease;
}

/* 滚动时导航栏样式变化 - 会在JS中添加.scrolled类 */
.home-header.scrolled {
  height: 60px;
  background: linear-gradient(90deg, rgba(58, 123, 213, 0.95) 0%, rgba(0, 210, 255, 0.95) 100%);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.logo {
  display: flex;
  align-items: center;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo-container img {
  height: 40px;
  margin-right: 10px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  transition: all 0.3s ease;
}

.scrolled .logo-container img {
  height: 36px;
}

.platform-title-wrapper {
  display: inline-block;
}

.platform-title {
  font-size: 24px;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  display: inline-block;
  writing-mode: horizontal-tb !important;
  text-orientation: mixed;
  white-space: nowrap;
  letter-spacing: 1px;
  transition: all 0.3s ease;
}

.scrolled .platform-title {
  font-size: 22px;
}

.menu {
  border-bottom: none;
  background: transparent;
}

/* 覆盖Element UI的菜单样式 */
.el-menu.el-menu--horizontal {
  border-bottom: none;
  background-color: transparent;
}

.el-menu--horizontal > .el-menu-item {
  height: 70px;
  line-height: 70px;
  color: #ffffff;
  font-weight: 500;
  position: relative;
  transition: all 0.3s ease;
}

.scrolled .el-menu--horizontal > .el-menu-item {
  height: 60px;
  line-height: 60px;
}

.el-menu--horizontal > .el-menu-item:hover,
.el-menu--horizontal > .el-menu-item:focus,
.el-menu--horizontal > .el-menu-item.is-active {
  color: #ffffff;
  background-color: rgba(255, 255, 255, 0.15);
}

/* 使用动态下划线替代静态边框 */
.el-menu--horizontal > .el-menu-item::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 3px;
  background: #ffffff;
  transition: all 0.3s ease;
  transform: translateX(-50%);
  border-radius: 3px 3px 0 0;
}

.el-menu--horizontal > .el-menu-item:hover::after,
.el-menu--horizontal > .el-menu-item:focus::after,
.el-menu--horizontal > .el-menu-item.is-active::after {
  width: 80%;
}

.user-actions {
  display: flex;
  align-items: center;
  padding-right: 20px; /* 增加右侧边距，避免被滚动条遮挡 */
}

/* 消息通知图标 */
.notification-icon {
  position: relative;
  font-size: 20px;
  color: #ffffff;
  cursor: pointer;
  padding: 5px 10px;
  margin-right: 10px;
  transition: all 0.3s ease;
}

.notification-icon:hover {
  transform: scale(1.1);
}

.notification-icon .el-badge {
  position: absolute;
  top: -8px;
  right: -2px;
}

.notification-icon .el-icon-bell {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.user-dropdown-link {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #ffffff;
  padding: 5px 8px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-dropdown-link:hover {
  background-color: rgba(255, 255, 255, 0.15);
}

/* 新样式：用户头像 */
.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
  border: 2px solid #fff;
  transition: all 0.3s ease;
}

.user-avatar:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.username {
  margin: 0 5px;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #ffffff;
  font-weight: 500;
}

.message-badge {
  margin-top: -8px;
}

/* 手机版按钮样式 */
.mobile-btn {
  margin-left: 15px;
  background-color: #4cd964;
  border-color: #4cd964;
  font-size: 14px;
  padding: 9px 15px;
  border-radius: 4px;
}

.mobile-btn:hover {
  background-color: #5edf74;
  border-color: #5edf74;
}

/* 覆盖Element UI的按钮样式 */
.btn-text {
  color: #ffffff;
  transition: all 0.3s ease;
}

.btn-text:hover {
  color: #f0f0f0;
  transform: translateY(-1px);
}

.btn-primary {
  background-color: #ff6b6b;
  border-color: #ff6b6b;
  color: white;
  font-weight: 500;
  box-shadow: 0 4px 10px rgba(255, 107, 107, 0.4);
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background-color: #ff8585;
  border-color: #ff8585;
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(255, 107, 107, 0.5);
}

.el-divider--vertical {
  background-color: rgba(255, 255, 255, 0.5);
}
</style> 