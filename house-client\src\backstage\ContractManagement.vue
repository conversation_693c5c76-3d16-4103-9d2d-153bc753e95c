<template>
  <div class="contract-management-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>合同管理</span>
        <el-button
          style="float: right; padding: 3px 0"
          type="text"
          @click="handleRefresh"
        >
          <i class="el-icon-refresh"></i> 刷新
        </el-button>
      </div>

      <!-- 搜索栏 -->
      <div class="search-bar">
        <el-form :inline="true" :model="searchForm" class="demo-form-inline">
          <el-form-item label="订单编号">
            <el-input v-model="searchForm.orderNo" placeholder="订单编号" clearable></el-input>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="状态" clearable>
              <el-option label="待签署" :value="CONTRACT_STATUS.PENDING"></el-option>
              <el-option label="已生效" :value="CONTRACT_STATUS.ACTIVE"></el-option>
              <el-option label="已过期" :value="CONTRACT_STATUS.EXPIRED"></el-option>
              <el-option label="已取消" :value="CONTRACT_STATUS.CANCELLED"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="resetSearch">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格 -->
      <el-table
        v-loading="loading"
        :data="contractList"
        border
        style="width: 100%"
      >
        <el-table-column prop="contractNo" label="合同编号" width="180"></el-table-column>
        <el-table-column prop="orderNo" label="订单编号" width="180"></el-table-column>
        <el-table-column prop="houseTitle" label="房源信息" min-width="200">
          <template slot-scope="scope">
            <div class="house-info">
              <el-image
                style="width: 60px; height: 60px"
                :src="getHouseImage(scope.row.house)"
                :preview-src-list="[getHouseImage(scope.row.house)]"
                fit="cover"
              ></el-image>
              <div class="house-details">
                <div class="house-title">{{ scope.row.houseDetail || (scope.row.house ? scope.row.house.title || scope.row.house.detail : '房源信息获取失败') }}</div>
                <div class="house-address">{{ scope.row.houseAddress || (scope.row.house ? scope.row.house.address : '') }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="tenantName" label="租客" width="120"></el-table-column>
        <el-table-column prop="ownerName" label="房东" width="120"></el-table-column>
        <el-table-column prop="startDate" label="合同期限" width="220">
          <template slot-scope="scope">
            {{ formatDate(scope.row.startDate) }} 至 {{ formatDate(scope.row.endDate) }}
          </template>
        </el-table-column>
        <el-table-column label="签署状态" width="160">
          <template slot-scope="scope">
            <div>
              <div>
                <el-tag size="mini" :type="scope.row.ownerSignTime ? 'success' : 'info'">
                  房东: {{ scope.row.ownerSignTime ? '已签署' : '未签署' }}
                </el-tag>
              </div>
              <div style="margin-top: 5px;">
                <el-tag size="mini" :type="scope.row.tenantSignTime ? 'success' : 'info'">
                  租客: {{ scope.row.tenantSignTime ? '已签署' : '未签署' }}
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template slot-scope="scope">
            <el-tag :type="getContractStatusType(scope.row.status)">{{ getContractStatusText(scope.row.status) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160">
          <template slot-scope="scope">{{ formatDateTime(scope.row.createTime) }}</template>
        </el-table-column>
        <el-table-column label="操作" width="220">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="handleView(scope.row)"
              icon="el-icon-view"
            >查看</el-button>
            
            <!-- 房东签署按钮 -->
            <el-button
              v-if="isCurrentUserOwner && isOwnerOfContract(scope.row) && !scope.row.ownerSignTime"
              size="mini"
              type="success"
              @click="handleSignWithName(scope.row, 'owner')"
              icon="el-icon-check"
            >签署</el-button>
            
            <!-- 租客签署按钮 -->
            <el-button
              v-if="!isCurrentUserOwner && scope.row.tenantId === userId && !scope.row.tenantSignTime"
              size="mini"
              type="success"
              @click="handleSignWithName(scope.row, 'tenant')"
              icon="el-icon-check"
            >签署</el-button>
            
            <el-button
              v-if="scope.row.status === CONTRACT_STATUS.ACTIVE"
              size="mini"
              type="warning"
              @click="handleTerminate(scope.row)"
              icon="el-icon-close"
            >终止</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
        ></el-pagination>
      </div>
    </el-card>

    <!-- 合同详情对话框 -->
    <el-dialog title="合同详情" :visible.sync="dialogVisible" width="80%" top="5vh">
      <div v-if="currentContract" class="contract-detail-wrapper">
        <!-- 使用共享合同组件 -->
        <contract-detail
          :contract="currentContract"
          :isOwner="isCurrentUserOwner && isOwnerOfContract(currentContract)"
          :isTenant="!isCurrentUserOwner && currentContract.tenantId === userId"
          :canSign="canSign(currentContract)"
          :userInfo="userInfo"
          @sign="handleSignFromComponent"
        ></contract-detail>

        <div class="dialog-footer" slot="footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
          <el-button
            v-if="isCurrentUserOwner && isOwnerOfContract(currentContract) && !currentContract.ownerSignTime"
            type="success"
            @click="handleSignWithName(currentContract, 'owner')"
          >房东签署合同</el-button>
          <el-button
            v-if="!isCurrentUserOwner && currentContract.tenantId === userId && !currentContract.tenantSignTime"
            type="success"
            @click="handleSignWithName(currentContract, 'tenant')"
          >租客签署合同</el-button>
          <el-button
            v-if="currentContract.status === CONTRACT_STATUS.ACTIVE"
            type="warning"
            @click="handleTerminate(currentContract)"
          >终止合同</el-button>
          <el-button type="primary" @click="exportPDF">导出PDF</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getBackstageUser, isAdmin, isOwner } from '../utils/auth'
import { getImageUrl } from '../utils/imageUtils'
import { ContractStatusUtils, CONTRACT_STATUS } from '../constants/contractStatus'
import { OrderStatusUtils, ORDER_STATUS } from '../constants/orderStatus'
import ContractDetail from '../components/ContractDetail'
import moment from 'moment'

// 添加jsPDF和html2canvas导入
import jsPDF from 'jspdf'
import html2canvas from 'html2canvas'

export default {
  name: 'ContractManagement',
  components: {
    ContractDetail
  },
  data() {
    return {
      loading: false,
      contractList: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      searchForm: {
        orderNo: '',
        status: ''
      },
      dialogVisible: false,
      currentContract: null,
      userInfo: null,
      userId: null,
      userRole: '',
      CONTRACT_STATUS, // 添加合同状态常量供模板使用
      ORDER_STATUS     // 添加订单状态常量供模板使用
    }
  },
  computed: {
    // 判断当前用户是否是房东
    isCurrentUserOwner() {
      return isOwner(false) || isAdmin(false);
    }
  },
  created() {
    this.userInfo = getBackstageUser()
    if (this.userInfo && this.userInfo.userInfo) {
      this.userId = this.userInfo.userInfo.id
      this.userRole = this.userInfo.role || ''
    }
    this.fetchContractList()
  },
  methods: {
    // 获取合同列表
    async fetchContractList() {
      this.loading = true
      try {
        // 构建查询参数
        const params = {
          page: this.currentPage,
          limit: this.pageSize
        }

        // 添加搜索条件
        if (this.searchForm.orderNo) {
          params.orderNo = this.searchForm.orderNo
        }
        if (this.searchForm.status) {
          params.status = this.searchForm.status
        }

        // 根据角色添加不同的参数
        if (isAdmin(false)) {
          // 管理员可以查看所有合同
        } else if (isOwner(false)) {
          // 房东只能查看自己的合同
          params.ownerId = this.userId
        } else {
          // 租客只能查看自己的合同
          params.tenantId = this.userId
        }

        // 调用API获取合同列表
        const res = await this.$http.get('/contracts', { params })
        
        if (res.data && res.data.flag) {
          this.contractList = res.data.data.list || []
          this.total = res.data.data.total || 0
          
          // 获取关联信息
          await this.fetchRelatedInfo()
        } else {
          this.$message.error('获取合同列表失败')
        }
      } catch (error) {
        console.error('获取合同列表失败:', error)
        this.$message.error('获取合同列表失败')
      } finally {
        this.loading = false
      }
    },
    
    // 获取关联信息（房源、租客、房东）
    async fetchRelatedInfo() {
      try {
        if (!this.contractList || this.contractList.length === 0) {
          return
        }
        
        // 遍历合同列表
        for (let i = 0; i < this.contractList.length; i++) {
          const contract = this.contractList[i]
          
          // 获取房源信息
          if (contract.houseId) {
            try {
              const houseRes = await this.$http.get(`/houses/${contract.houseId}`)
              if (houseRes.data && houseRes.data.flag) {
                this.$set(this.contractList[i], 'house', houseRes.data.data)
              }
            } catch (error) {
              console.error(`获取房源信息失败:`, error)
            }
          }
          
          // 获取租客信息
          if (contract.tenantId && !contract.tenantName) {
            try {
              const tenantRes = await this.$http.get(`/user/${contract.tenantId}`)
              if (tenantRes.data && tenantRes.data.flag) {
                this.$set(this.contractList[i], 'tenantName', tenantRes.data.data.username)
              }
            } catch (error) {
              console.error(`获取租客信息失败:`, error)
            }
          }
          
          // 获取房东信息
          if (contract.ownerId && !contract.ownerName) {
            try {
              const ownerRes = await this.$http.get(`/user/${contract.ownerId}`)
              if (ownerRes.data && ownerRes.data.flag) {
                this.$set(this.contractList[i], 'ownerName', ownerRes.data.data.username)
              }
            } catch (error) {
              console.error(`获取房东信息失败:`, error)
            }
          }
        }
      } catch (error) {
        console.error('获取关联信息失败:', error)
      }
    },
    
    // 处理查询
    handleSearch() {
      this.currentPage = 1
      this.fetchContractList()
    },
    
    // 重置查询
    resetSearch() {
      this.searchForm = {
        orderNo: '',
        status: ''
      }
      this.handleSearch()
    },
    
    // 刷新
    handleRefresh() {
      this.fetchContractList()
    },
    
    // 查看详情
    handleView(row) {
      this.currentContract = { ...row };
      
      // 处理合同金额数据
      if (this.currentContract) {
        // 从关联的订单或房源中获取价格信息
        this.currentContract.monthlyRent = this.currentContract.monthlyPrice || 
            (this.currentContract.house ? this.currentContract.house.price : 0);
        
        this.currentContract.deposit = this.currentContract.deposit || 0;
        this.currentContract.duration = this.currentContract.duration || 12;
        
        // 确保所有金额字段都有值
        if (!this.currentContract.monthlyRent && row.order) {
          this.currentContract.monthlyRent = row.order.monthlyPrice || 0;
          this.currentContract.deposit = row.order.deposit || 0;
          this.currentContract.duration = row.order.duration || 12;
        }

        // 确保房东和租客名字在签名中显示
        if (this.currentContract.ownerSignTime && !this.currentContract.ownerSignature) {
          this.currentContract.ownerSignature = this.currentContract.ownerName;
        }
        if (this.currentContract.tenantSignTime && !this.currentContract.tenantSignature) {
          this.currentContract.tenantSignature = this.currentContract.tenantName;
        }
      }
      
      this.dialogVisible = true;
    },
    
    // 签署合同
    async handleSign(contract) {
      try {
        await this.$confirm('确定要签署该合同吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'success'
        })
        
        // 根据角色确定签署字段
        const signData = {}
        const isOwnerSigning = isOwner(false) || isAdmin(false);
        
        if (isOwnerSigning) {
          signData.ownerSignTime = new Date()
          signData.ownerSignature = this.userInfo.userInfo.username
        } else {
          signData.tenantSignTime = new Date()
          signData.tenantSignature = this.userInfo.userInfo.username
        }
        
        // 调用API
        const res = await this.$http.put(`/contracts/${contract.contractNo}/sign`, signData)
        
        if (res.data && res.data.flag) {
          this.$message.success('合同签署成功')
          
          // 检查合同是否已完全签署（房东和租客都已签署）
          const updatedContract = res.data.data;
          const bothSigned = updatedContract.ownerSignTime && updatedContract.tenantSignTime;
          
          if (bothSigned) {
            // 如果双方都已签署，更新合同状态为生效中
            await this.$http.put(`/contracts/${contract.contractNo}`, {
              status: CONTRACT_STATUS.ACTIVE
            });

            // 更新订单状态为待支付押金
            await this.$http.put(`/orders/${contract.orderNo}`, {
              status: ORDER_STATUS.UNPAID,
              remark: '合同已签署，请尽快支付押金'
            });
            
            // 发送消息通知租客支付租金
            await this.$http.post('/messages', {
              userId: contract.tenantId,
              title: '合同已签署，请支付租金',
              content: `合同(${contract.contractNo})已完成签署，请前往"我的订单"页面支付租金。`,
              type: 'payment',
              relatedId: contract.orderNo
            });
            
            this.$message.success('双方已完成签署，订单状态已更新为待支付押金');
          } else {
            // 如果是房东签署，通知租客签署合同
            if (isOwnerSigning) {
              await this.$http.post('/messages', {
                userId: contract.tenantId,
                title: '房东已签署合同，请您签署',
                content: `房东已签署合同(${contract.contractNo})，请前往"我的订单"页面查看并签署合同。`,
                type: 'contract',
                relatedId: contract.orderNo
              });
            } 
            // 如果是租客签署，通知房东签署合同
            else {
              await this.$http.post('/messages', {
                userId: contract.ownerId,
                title: '租客已签署合同，请您签署',
                content: `租客已签署合同(${contract.contractNo})，请前往"合同管理"页面查看并签署合同。`,
                type: 'contract',
                relatedId: contract.orderNo
              });
            }
          }
          
          this.dialogVisible = false
          this.fetchContractList()
        } else {
          this.$message.error(res.data.message || '操作失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('签署合同失败:', error)
          this.$message.error('签署合同失败')
        }
      }
    },
    
    // 终止合同
    async handleTerminate(contract) {
      try {
        const { value } = await this.$prompt('请输入终止理由', '终止合同', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPlaceholder: '请输入终止理由'
        })
        
        // 调用API
        const res = await this.$http.put(`/contracts/${contract.contractNo}/terminate`, {
          status: CONTRACT_STATUS.CANCELLED,
          terminationReason: value || '合同已终止',
          terminationTime: new Date()
        })
        
        if (res.data && res.data.flag) {
          this.$message.success('合同已终止')
          this.dialogVisible = false
          this.fetchContractList()
        } else {
          this.$message.error(res.data.message || '操作失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('终止合同失败:', error)
          this.$message.error('终止合同失败')
        }
      }
    },
    
    // 判断是否可以签署
    canSign(contract) {
      if (isAdmin(false)) {
        return true
      }

      if (isOwner(false) && contract.ownerId === this.userId && !contract.ownerSignTime) {
        return true
      }

      if (!isOwner(false) && !isAdmin(false) && contract.tenantId === this.userId && !contract.tenantSignTime) {
        return true
      }

      return false
    },
    
    // 打印合同
    handlePrint() {
      window.print()
    },

    // 添加导出PDF方法
    async exportPDF() {
      if (!this.currentContract) return
      
      try {
        this.$message({
          message: '正在生成PDF，请稍候...',
          type: 'info',
          duration: 0,
          showClose: true
        })
        
        // 获取合同内容DOM
        const contractElement = document.querySelector('.contract-detail')
        if (!contractElement) {
          this.$message.error('找不到合同内容')
          return
        }
        
        // 使用html2canvas将DOM转换为canvas
        const canvas = await html2canvas(contractElement, {
          scale: 2, // 提高清晰度
          useCORS: true, // 允许跨域图片
          logging: false
        })
        
        // 获取canvas的宽高
        const imgWidth = 210 // A4宽度，单位mm
        const pageHeight = 297 // A4高度，单位mm
        const imgHeight = canvas.height * imgWidth / canvas.width
        const heightLeft = imgHeight
        
        // 创建PDF实例
        const pdf = new jsPDF('p', 'mm', 'a4')
        const imgData = canvas.toDataURL('image/png')
        
        // 添加图片到PDF
        pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight)
        
        // 如果内容超过一页，添加新页
        let position = 0
        
        // 如果高度超过一页，添加新页
        while (heightLeft > pageHeight) {
          position = heightLeft - pageHeight
          pdf.addPage()
          pdf.addImage(imgData, 'PNG', 0, -position, imgWidth, imgHeight)
          heightLeft -= pageHeight
        }
        
        // 生成合同文件名
        const fileName = `租赁合同_${this.currentContract.contractNo}.pdf`
        
        // 保存PDF
        pdf.save(fileName)
        
        this.$message.closeAll()
        this.$message.success('PDF导出成功')
      } catch (error) {
        console.error('导出PDF失败:', error)
        this.$message.closeAll()
        this.$message.error('导出PDF失败: ' + error.message)
      }
    },
    
    // 获取房屋图片
    getHouseImage(house) {
      if (!house) return require('../assets/showcase.jpg')
      
      if (house.imageUrl) {
        return getImageUrl(house.imageUrl)
      }
      
      return getImageUrl('/img/showcase.jpg')
    },
    
    // 格式化日期
    formatDate(dateStr) {
      return moment(dateStr).format('YYYY-MM-DD')
    },
    
    // 格式化日期时间
    formatDateTime(dateStr) {
      return moment(dateStr).format('YYYY-MM-DD HH:mm')
    },
    
    // 获取合同状态类型
    getContractStatusType(status) {
      return ContractStatusUtils.getStatusType(status)
    },

    // 获取合同状态文本
    getContractStatusText(status) {
      return ContractStatusUtils.getStatusText(status)
    },
    
    // 获取付款方式文本
    getPaymentMethodText(method) {
      const methodMap = {
        'monthly': '月付',
        'quarterly': '季付',
        'annually': '年付',
        'semiannual': '半年付'
      }
      return methodMap[method] || method
    },
    
    // 处理页大小变化
    handleSizeChange(val) {
      this.pageSize = val
      this.fetchContractList()
    },
    
    // 处理页码变化
    handleCurrentChange(val) {
      this.currentPage = val
      this.fetchContractList()
    },

    // 判断合同是否是当前用户的房东签署
    isOwnerOfContract(contract) {
      return contract.ownerId === this.userId;
    },

    // 处理房东签署
    async handleOwnerSign(contract) {
      try {
        await this.$confirm('确定要签署该合同吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'success'
        })

        const signData = {
          ownerSignTime: new Date(),
          ownerSignature: this.userInfo.userInfo.username
        };

        const res = await this.$http.put(`/contracts/${contract.contractNo}/sign`, signData);

        if (res.data && res.data.flag) {
          this.$message.success('合同签署成功');
          this.dialogVisible = false;
          this.fetchContractList();
        } else {
          this.$message.error(res.data.message || '操作失败');
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('房东签署合同失败:', error);
          this.$message.error('房东签署合同失败');
        }
      }
    },

    // 处理签署合同并显示姓名
    async handleSignWithName(contract, role) {
      try {
        await this.$confirm('确定要签署该合同吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'success'
        })

        const signData = {
          [`${role}SignTime`]: new Date(),
          [`${role}Signature`]: this.userInfo.userInfo.username
        };

        const res = await this.$http.put(`/contracts/${contract.contractNo}/sign`, signData);

        if (res.data && res.data.flag) {
          this.$message.success('合同签署成功');
          this.dialogVisible = false;
          this.fetchContractList();
        } else {
          this.$message.error(res.data.message || '操作失败');
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('签署合同失败:', error);
          this.$message.error('签署合同失败');
        }
      }
    },

    // 处理共享组件的签名事件
    handleSignFromComponent({ role, contract }) {
      this.handleSignWithName(contract, role);
    }
  }
}
</script>

<style scoped>
.contract-management-container {
  padding: 20px;
}

.search-bar {
  margin-bottom: 20px;
}

.house-info {
  display: flex;
  align-items: center;
}

.house-details {
  margin-left: 10px;
  overflow: hidden;
}

.house-title {
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.house-address {
  color: #909399;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.pagination-container {
  margin-top: 20px;
  text-align: right;
}

.contract-detail-wrapper {
  padding: 0;
  overflow: auto;
}

.contract-detail {
  padding: 20px 0;
}

.contract-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.contract-header h2 {
  margin: 0;
}

.contract-info {
  margin-bottom: 20px;
}

.contract-content {
  border: 1px solid #eee;
  padding: 20px;
  margin-top: 20px;
  background-color: #fafafa;
}

.contract-body {
  padding: 30px;
  border: 1px solid #dcdfe6;
  background: #fff;
  min-height: 600px;
}

.contract-title {
  text-align: center;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 40px;
}

.contract-parties {
  margin-bottom: 30px;
}

.contract-terms h3 {
  font-size: 18px;
  margin: 25px 0 15px;
  color: #333;
}

.contract-terms p {
  line-height: 1.8;
  text-indent: 2em;
  margin-bottom: 10px;
}

.contract-signatures {
  margin-top: 60px;
  display: flex;
  justify-content: space-between;
}

.signature-area {
  width: 45%;
  text-align: center;
}

.signature-title {
  font-weight: bold;
  margin-bottom: 15px;
}

.signature-content {
  border: 1px dashed #ddd;
  padding: 20px;
  min-height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.sign-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.signature-name {
  font-size: 22px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #409EFF;
  font-family: "楷体", KaiTi, serif;
  padding: 10px 0;
}

.sign-time {
  color: #909399;
  font-size: 12px;
  margin-top: 5px;
}

.contract-badge {
  margin-top: 0;
}

.el-badge__content {
  background-color: #f56c6c;
}

@media print {
  .contract-page {
    background-color: #fff;
  }
  
  .container {
    margin: 0;
    padding: 0;
    max-width: 100%;
  }
  
  .contract-card {
    box-shadow: none;
    padding: 0;
  }
  
  h2, .el-divider, .header-info, .el-tabs__header, .contract-footer, .contract-status-tips, .contract-actions {
    display: none !important;
  }
  
  .el-tabs__content {
    padding: 0 !important;
  }
  
  .contract-body {
    border: none;
    padding: 0;
  }
}
</style> 