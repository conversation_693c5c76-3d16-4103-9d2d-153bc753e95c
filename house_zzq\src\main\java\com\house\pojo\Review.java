package com.house.pojo;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 评价实体类
 */
public class Review implements Serializable {
    
    private static final long serialVersionUID = 1L;

    // 主键ID
    private Integer id;
    
    // 订单ID
    private Integer orderId;
    
    // 订单编号
    private String orderNo;
    
    // 房屋ID
    private Integer houseId;
    
    // 房东ID
    private Integer ownerId;
    
    // 评价用户ID
    private Integer userId;
    
    // 评价用户名
    private String userName;
    
    // 用户头像
    private String userAvatar;
    
    // 总体评分（1-5分）
    private Double rating;
    
    // 位置便利性评分
    private Double locationRating;
    
    // 房屋设施评分
    private Double facilityRating;
    
    // 房东服务评分
    private Double serviceRating;
    
    // 性价比评分
    private Double valueRating;
    
    // 周边环境评分
    private Double environmentRating;
    
    // 卫生情况评分
    private Double cleanlinessRating;
    
    // 平均评分
    private Double averageRating;
    
    // 评价内容
    private String content;
    
    // 评价标签，逗号分隔
    private String tags;
    
    // 评价图片，逗号分隔的URL
    private String images;
    
    // 是否匿名评价
    private Boolean anonymous;
    
    // 房东回复
    private String reply;
    
    // 回复时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date replyTime;
    
    // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    // 更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    // Getters and Setters
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public Integer getHouseId() {
        return houseId;
    }

    public void setHouseId(Integer houseId) {
        this.houseId = houseId;
    }

    public Integer getOwnerId() {
        return ownerId;
    }

    public void setOwnerId(Integer ownerId) {
        this.ownerId = ownerId;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserAvatar() {
        return userAvatar;
    }

    public void setUserAvatar(String userAvatar) {
        this.userAvatar = userAvatar;
    }

    public Double getRating() {
        return rating;
    }

    public void setRating(Double rating) {
        this.rating = rating;
    }

    public Double getLocationRating() {
        return locationRating;
    }

    public void setLocationRating(Double locationRating) {
        this.locationRating = locationRating;
    }

    public Double getFacilityRating() {
        return facilityRating;
    }

    public void setFacilityRating(Double facilityRating) {
        this.facilityRating = facilityRating;
    }

    public Double getServiceRating() {
        return serviceRating;
    }

    public void setServiceRating(Double serviceRating) {
        this.serviceRating = serviceRating;
    }

    public Double getValueRating() {
        return valueRating;
    }

    public void setValueRating(Double valueRating) {
        this.valueRating = valueRating;
    }

    public Double getEnvironmentRating() {
        return environmentRating;
    }

    public void setEnvironmentRating(Double environmentRating) {
        this.environmentRating = environmentRating;
    }

    public Double getCleanlinessRating() {
        return cleanlinessRating;
    }

    public void setCleanlinessRating(Double cleanlinessRating) {
        this.cleanlinessRating = cleanlinessRating;
    }

    public Double getAverageRating() {
        return averageRating;
    }

    public void setAverageRating(Double averageRating) {
        this.averageRating = averageRating;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public String getImages() {
        return images;
    }

    public void setImages(String images) {
        this.images = images;
    }

    public Boolean getAnonymous() {
        return anonymous;
    }

    public void setAnonymous(Boolean anonymous) {
        this.anonymous = anonymous;
    }

    public String getReply() {
        return reply;
    }

    public void setReply(String reply) {
        this.reply = reply;
    }

    public Date getReplyTime() {
        return replyTime;
    }

    public void setReplyTime(Date replyTime) {
        this.replyTime = replyTime;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "Review{" +
                "id=" + id +
                ", orderId=" + orderId +
                ", orderNo='" + orderNo + '\'' +
                ", houseId=" + houseId +
                ", ownerId=" + ownerId +
                ", userId=" + userId +
                ", userName='" + userName + '\'' +
                ", rating=" + rating +
                ", content='" + content + '\'' +
                ", anonymous=" + anonymous +
                ", createTime=" + createTime +
                '}';
    }
} 