package com.house.controller;

import com.house.common.Result;
import com.house.common.StatusCode;
import com.house.dto.UserExecution;
import com.house.pojo.User;
import com.house.pojo.UserList;
import com.house.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 后台管理控制器
 */
@Slf4j
@RestController
@CrossOrigin(origins = "http://localhost:8080", allowCredentials = "true", maxAge = 3600)
@RequestMapping("/admin")
public class AdminController {

    @Autowired
    private UserService userService;

    /**
     * 后台用户注册
     * @param registerData 注册数据
     * @return 注册结果
     */
    @PostMapping("/register")
    public Result register(@RequestBody Map<String, Object> registerData) {
        log.info("【后台注册】接收到注册请求：{}", registerData);
        
        try {
            // 创建用户账号
            User user = new User();
            user.setUsername((String) registerData.get("username"));
            user.setPassword((String) registerData.get("password"));
            
            // 根据角色设置用户类型（1:管理员, 2:租客, 3:房东）
            String role = (String) registerData.get("role");
            if ("admin".equals(role)) {
                user.setType(1);
            } else if ("tenant".equals(role)) {
                user.setType(2);
            } else if ("owner".equals(role)) {
                user.setType(3);
            } else {
                user.setType(3); // 默认为房东
            }
            
            // 添加用户账号
            int effectedNum = userService.getUserDao().insertUser(user);
            if (effectedNum < 1) {
                return new Result(false, StatusCode.ERROR, "添加用户账号失败");
            }
            
            // 获取生成的用户ID
            int accountId = user.getId();
            
            // 创建用户信息
            UserList userList = new UserList();
            userList.setUserId(accountId);
            userList.setName((String) registerData.get("realName"));
            userList.setPhone((String) registerData.get("phone"));
            userList.setIdCard((String) registerData.get("idCard"));
            userList.setAvatar((String) registerData.get("avatar"));
            userList.setType(user.getType());
            
            // 添加用户信息
            effectedNum = userService.getUserListDao().insertUserList(userList);
            if (effectedNum < 1) {
                return new Result(false, StatusCode.ERROR, "添加用户信息失败");
            }
            
            log.info("【后台注册】用户注册成功，用户名：{}", user.getUsername());
            return new Result(true, StatusCode.SUCCESS, "注册成功");
        } catch (Exception e) {
            log.error("【后台注册】注册失败：", e);
            return new Result(false, StatusCode.ERROR, "注册失败：" + e.getMessage());
        }
    }
    
    /**
     * 检查用户名是否存在
     * @param username 用户名
     * @return 是否存在
     */
    @GetMapping("/check-username")
    public Result checkUsername(@RequestParam String username) {
        User user = userService.getUserDao().selectByUsername(username);
        return new Result(true, StatusCode.SUCCESS, "查询成功", user != null);
    }
    
    /**
     * 检查邮箱是否存在（实际业务中需实现）
     * @param email 邮箱
     * @return 是否存在
     */
    @GetMapping("/check-email")
    public Result checkEmail(@RequestParam String email) {
        // TODO: 实现邮箱检查逻辑
        return new Result(true, StatusCode.SUCCESS, "查询成功", false);
    }
    
    /**
     * 检查手机号是否存在
     * @param phone 手机号
     * @return 是否存在
     */
    @GetMapping("/check-phone")
    public Result checkPhone(@RequestParam String phone) {
        UserList userList = userService.getUserListDao().selectByPhone(phone);
        return new Result(true, StatusCode.SUCCESS, "查询成功", userList != null);
    }
} 