<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.house.mapper.OrderMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.house.pojo.Order">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="house_id" property="houseId" />
        <result column="tenant_id" property="tenantId" />
        <result column="owner_id" property="ownerId" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
        <result column="duration" property="duration" />
        <result column="monthly_price" property="monthlyPrice" />
        <result column="deposit" property="deposit" />
        <result column="service_fee" property="serviceFee" />
        <result column="total_price" property="totalPrice" />
        <result column="status" property="status" />
        <result column="deposit_pay_time" property="depositPayTime" />
        <result column="contract_sign_time" property="contractSignTime" />
        <result column="check_in_time" property="checkInTime" />
        <result column="reviewed" property="reviewed" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 条件查询 -->
    <select id="selectByCondition" parameterType="map" resultMap="BaseResultMap">
        SELECT * FROM tb_order
        <where>
            <if test="id != null">
                AND id = #{id}
            </if>
            <if test="orderNo != null and orderNo != ''">
                AND order_no = #{orderNo}
            </if>
            <if test="houseId != null">
                AND house_id = #{houseId}
            </if>
            <if test="tenantId != null">
                AND tenant_id = #{tenantId}
            </if>
            <if test="ownerId != null">
                AND owner_id = #{ownerId}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="userId != null">
                AND (tenant_id = #{userId} OR owner_id = #{userId})
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

</mapper> 