<template>
  <div class="register-container no-scroll-page">
   
    <div class="register-content">
      <div class="welcome-section">
        <h1 class="welcome-title">加入我们</h1>
        <p class="welcome-subtitle">成为我们平台的一员，开启您的租房之旅</p>
        <div class="welcome-features">
          <div class="feature-item">
            <i class="el-icon-search"></i>
            <span>找到心仪房源</span>
          </div>
          <div class="feature-item">
            <i class="el-icon-document"></i>
            <span>在线签约流程</span>
          </div>
          <div class="feature-item">
            <i class="el-icon-s-home"></i>
            <span>一键发布房源</span>
          </div>
        </div>
      </div>
      <div class="register-box">
        <h2>用户注册</h2>
        <el-form :model="registerForm" status-icon :rules="rules" ref="registerForm" label-width="90px">
          <el-form-item label="用户名" prop="username">
            <el-input v-model="registerForm.username" prefix-icon="el-icon-user" placeholder="请输入用户名"></el-input>
          </el-form-item>
          <el-form-item label="账号" prop="account">
            <el-input v-model="registerForm.account" prefix-icon="el-icon-key" placeholder="请输入账号"></el-input>
          </el-form-item>
          <el-form-item label="密码" prop="password">
            <el-input type="password" v-model="registerForm.password" prefix-icon="el-icon-lock" placeholder="请输入密码"></el-input>
          </el-form-item>
          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input type="password" v-model="registerForm.confirmPassword" prefix-icon="el-icon-check" placeholder="请再次输入密码"></el-input>
          </el-form-item>
          <el-form-item label="手机号码" prop="phone">
            <el-input v-model="registerForm.phone" prefix-icon="el-icon-phone" placeholder="请输入手机号码"></el-input>
          </el-form-item>
          <el-form-item label="邮箱" prop="email">
            <el-input v-model="registerForm.email" prefix-icon="el-icon-message" placeholder="请输入邮箱"></el-input>
          </el-form-item>
          <el-form-item label="用户类型" prop="type">
            <el-radio-group v-model="registerForm.type">
              <el-radio :label="2">我要租房</el-radio>
              <el-radio :label="3">我是房东</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item class="register-actions">
            <el-button type="primary" @click="submitForm('registerForm')" class="btn-primary register-btn">注册</el-button>
            <el-button @click="resetForm('registerForm')" plain class="btn-text">重置</el-button>
          </el-form-item>
          <div class="form-footer">
            <div class="login-link">
              已有账号？<router-link to="/frontend/login" class="btn-text">立即登录</router-link>
            </div>
            <div class="home-link">
              <router-link to="/frontend/home" class="btn-text">
                <i class="el-icon-back"></i> 返回首页
              </router-link>
            </div>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { userApi } from './api'

export default {
  name: 'Register',
  data() {
    // 验证密码是否一致
    const validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.registerForm.password) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }
    // 验证手机号
    const validatePhone = (rule, value, callback) => {
      const reg = /^1[3456789]\d{9}$/
      if (value === '') {
        callback(new Error('请输入手机号码'))
      } else if (!reg.test(value)) {
        callback(new Error('手机号码格式不正确'))
      } else {
        callback()
      }
    }
    return {
      registerForm: {
        username: '',
        account: '',
        password: '',
        confirmPassword: '',
        phone: '',
        email: '',
        type: 2 // 默认为租客
      },
      rules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 2, max: 10, message: '长度在 2 到 10 个字符', trigger: 'blur' }
        ],
        account: [
          { required: true, message: '请输入账号', trigger: 'blur' },
          { min: 4, max: 16, message: '长度在 4 到 16 个字符', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 18, message: '长度在 6 到 18 个字符', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, validator: validatePass, trigger: 'blur' }
        ],
        phone: [
          { required: true, validator: validatePhone, trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // 构造用户数据
          const userData = {
            name: this.registerForm.username,
            phone: this.registerForm.phone,
            email: this.registerForm.email,
            type: this.registerForm.type,
            username: this.registerForm.account,
            password: this.registerForm.password
          }
          
          // 发送注册请求
          userApi.register(userData)
            .then(response => {
              if (response.data && response.data.flag) {
                this.$message({
                  message: '注册成功!',
                  type: 'success'
                })
                // 注册成功后跳转到登录页面
                this.$router.push('/frontend/login')
              } else {
                this.$message.error(response.data.message || '注册失败，请稍后再试')
              }
            })
            .catch(error => {
              console.error('注册请求失败:', error)
              this.$message.error('注册失败，请稍后再试')
            })
        } else {
          console.log('表单验证失败')
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>

<style scoped>
/* 防止滚动条 */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  width: 100%;
}

.register-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: url('../assets/bg.jpg') no-repeat center center;
  background-size: cover;
  position: relative;
}

.register-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 0;
}

.register-header {
  position: relative;
  z-index: 1;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 15px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.logo {
  width: 1200px;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 20px;
}

.logo a {
  display: flex;
  align-items: center;
  text-decoration: none;
}

.logo-img {
  height: 40px;
  margin-right: 10px;
}

.logo-text {
  font-size: 22px;
  font-weight: bold;
  color: #409EFF;
}

.register-content {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh - 70px);
  padding: 20px;
  overflow-y: auto;
  overflow-x: hidden;
}

.welcome-section {
  color: #fff;
  width: 400px;
  margin-right: 80px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.welcome-title {
  font-size: 42px;
  margin-bottom: 15px;
  font-weight: 600;
}

.welcome-subtitle {
  font-size: 18px;
  margin-bottom: 30px;
  opacity: 0.9;
}

.welcome-features {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.feature-item {
  display: flex;
  align-items: center;
  font-size: 16px;
}

.feature-item i {
  font-size: 24px;
  margin-right: 15px;
  color: #409EFF;
}

.register-box {
  width: 500px;
  padding: 25px 35px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  max-height: calc(100vh - 130px);
  overflow-y: auto;
}

.register-box:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
}

.register-box h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #409EFF;
  font-weight: 600;
}

.register-actions {
  margin-top: 20px;
}

.register-btn {
  width: 100%;
  margin-bottom: 15px;
}

.form-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 20px;
  padding: 0 20px;
}

.login-link a, .home-link a {
  text-decoration: none;
  color: #409EFF;
}

.login-link a:hover, .home-link a:hover {
  color: #66b1ff;
}

/* 响应式设计 */
@media screen and (max-width: 992px) {
  .register-content {
    flex-direction: column;
  }
  
  .welcome-section {
    margin-right: 0;
    margin-bottom: 40px;
    text-align: center;
    width: 100%;
    max-width: 500px;
  }
  
  .welcome-features {
    flex-direction: row;
    justify-content: center;
    gap: 30px;
  }
}

@media screen and (max-width: 576px) {
  .register-box {
    width: 100%;
    padding: 25px;
  }
  
  .welcome-features {
    flex-direction: column;
    align-items: center;
  }
}
</style> 