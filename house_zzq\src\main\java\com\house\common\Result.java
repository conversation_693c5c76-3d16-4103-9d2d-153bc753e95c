package com.house.common;

import lombok.Data;

import java.io.Serializable;

/**
 * 通用响应结果类
 */
@Data
public class Result<T> implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 是否成功
     */
    private boolean flag;
    
    /**
     * 返回码
     */
    private Integer code;
    
    /**
     * 返回消息
     */
    private String message;
    
    /**
     * 返回数据
     */
    private T data;
    
    /**
     * 用户信息（登录时使用）
     */
    private Object userInfo;
    
    /**
     * token（登录时使用）
     */
    private String token;
    
    public Result() {
    }
    
    public Result(boolean flag, Integer code, String message) {
        this.flag = flag;
        this.code = code;
        this.message = message;
    }
    
    public Result(boolean flag, Integer code, String message, T data) {
        this.flag = flag;
        this.code = code;
        this.message = message;
        this.data = data;
    }
    
    /**
     * 成功返回结果
     *
     * @param data 数据
     * @return 结果
     */
    public static <T> Result<T> success(T data) {
        return new Result<>(true, ResultCode.SUCCESS, "操作成功", data);
    }
    
    /**
     * 成功返回结果
     *
     * @param message 提示信息
     * @return 结果
     */
    public static <T> Result<T> success(String message) {
        return new Result<>(true, ResultCode.SUCCESS, message);
    }
    
    /**
     * 成功返回结果
     *
     * @param message 提示信息
     * @param data 数据
     * @return 结果
     */
    public static <T> Result<T> success(String message, T data) {
        return new Result<>(true, ResultCode.SUCCESS, message, data);
    }
    
    /**
     * 失败返回结果
     *
     * @param message 提示信息
     * @return 结果
     */
    public static <T> Result<T> error(String message) {
        return new Result<>(false, ResultCode.ERROR, message);
    }
    
    /**
     * 失败返回结果
     *
     * @param code 错误码
     * @param message 提示信息
     * @return 结果
     */
    public static <T> Result<T> error(Integer code, String message) {
        return new Result<>(false, code, message);
    }
}
