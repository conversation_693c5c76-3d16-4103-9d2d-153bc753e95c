import request from '@/utils/request'
const api_name = 'house'
export default{
    getAllHouseList(){
        return request({
            url:`/${api_name}/getallhouselist`,
            method:'get'
        })
    },
    getHouseListByCondition(pojo){
        return request({
            url:`/${api_name}/gethouselistbycondition`,
            method:'post',
            data:pojo
        })
    },
    addHouse(pojo){
        return request({
            url:`/${api_name}/addhouse`,
            method:'post',
            data:pojo
        })
    },
    updateHouse(pojo){
        return request({
            url:`/${api_name}/updatehouse`,
            method:'post',
            data:pojo
        })
    },
    deleteHouse(id){
        return request({
            url:`/${api_name}/deletehouse?houseId=`+id,
            method:'delete',
        })
    },
    
    // 根据房东ID获取房源列表
    getHouseListByOwnerId(ownerId){
        return request({
            url:`/${api_name}/getownerhouselist?ownerId=${ownerId}`,
            method:'get'
        })
    },
    
    // 分页查询房东的房源列表
    getOwnerHousePage(query){
        return request({
            url:`/${api_name}/getownerhousepage`,
            method:'post',
            data: query
        })
    }
}







