package com.house.service;

import com.house.pojo.Review;

import java.util.List;
import java.util.Map;

/**
 * 评价服务接口
 */
public interface ReviewService {

    /**
     * 提交评价
     * @param review 评价对象
     * @return 评价对象（包含ID）
     */
    Review addReview(Review review);

    /**
     * 获取房屋评价统计信息
     * @param houseId 房屋ID
     * @return 统计信息
     */
    Map<String, Object> getReviewStats(Integer houseId);

    /**
     * 获取房屋所有评价
     * @param houseId 房屋ID
     * @return 评价列表
     */
    List<Review> getReviewsByHouseId(Integer houseId);

    /**
     * 获取房东的所有评价
     * @param ownerId 房东ID
     * @return 评价列表
     */
    List<Review> getReviewsByOwnerId(Integer ownerId);

    /**
     * 房东回复评价
     * @param reviewId 评价ID
     * @param reply 回复内容
     * @return 是否成功
     */
    boolean replyReview(Integer reviewId, String reply);

    /**
     * 删除评价
     * @param reviewId 评价ID
     * @return 是否成功
     */
    boolean deleteReview(Integer reviewId);

    /**
     * 检查用户是否已评价订单
     * @param orderId 订单ID
     * @return 是否已评价
     */
    boolean hasReviewed(Integer orderId);
} 