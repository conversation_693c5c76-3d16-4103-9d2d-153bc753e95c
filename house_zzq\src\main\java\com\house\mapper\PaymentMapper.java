package com.house.mapper;

import com.house.pojo.Payment;
import org.apache.ibatis.annotations.*;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 支付记录数据访问接口
 */
@Mapper
@Repository
public interface PaymentMapper {

    /**
     * 插入支付记录
     */
    @Insert("INSERT INTO tb_payment(payment_no, order_id, user_id, amount, type, method, " +
            "status, transaction_id, create_time, update_time) " +
            "VALUES(#{paymentNo}, #{orderId}, #{userId}, #{amount}, #{type}, #{method}, " +
            "#{status}, #{transactionId}, #{createTime}, #{updateTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Payment payment);

    /**
     * 根据ID查询支付记录
     */
    @Select("SELECT * FROM tb_payment WHERE id = #{id}")
    Payment selectById(Integer id);

    /**
     * 根据支付单号查询支付记录
     */
    @Select("SELECT * FROM tb_payment WHERE payment_no = #{paymentNo}")
    Payment selectByPaymentNo(String paymentNo);

    /**
     * 更新支付记录
     */
    @Update("UPDATE tb_payment SET status = #{status}, transaction_id = #{transactionId}, " +
            "pay_time = #{payTime}, update_time = #{updateTime} WHERE id = #{id}")
    int update(Payment payment);

    /**
     * 更新支付状态
     */
    @Update("UPDATE tb_payment SET status = #{status}, transaction_id = #{transactionId}, " +
            "pay_time = #{payTime}, update_time = NOW() WHERE payment_no = #{paymentNo}")
    int updateStatus(@Param("paymentNo") String paymentNo, @Param("status") String status,
                      @Param("transactionId") String transactionId, @Param("payTime") Date payTime);

    /**
     * 查询订单的支付记录列表
     */
    @Select("SELECT * FROM tb_payment WHERE order_id = #{orderId} ORDER BY create_time DESC")
    List<Payment> selectByOrderId(Integer orderId);

    /**
     * 查询用户的支付记录列表
     */
    @Select("SELECT * FROM tb_payment WHERE user_id = #{userId} ORDER BY create_time DESC")
    List<Payment> selectByUserId(Integer userId);

    /**
     * 查询订单的押金支付记录
     */
    @Select("SELECT * FROM tb_payment WHERE order_id = #{orderId} AND type = 'deposit' ORDER BY create_time DESC LIMIT 1")
    Payment selectDepositByOrderId(Integer orderId);

    /**
     * 查询订单的租金支付记录列表
     */
    @Select("SELECT * FROM tb_payment WHERE order_id = #{orderId} AND type = 'rent' ORDER BY create_time DESC")
    List<Payment> selectRentsByOrderId(Integer orderId);

    /**
     * 条件查询支付记录列表
     */
    List<Payment> selectByCondition(Map<String, Object> params);

    /**
     * 统计用户各状态的支付记录数量
     */
    @Select("SELECT status, COUNT(*) as count FROM tb_payment WHERE user_id = #{userId} GROUP BY status")
    List<Map<String, Object>> countPaymentsByUserIdGroupByStatus(Integer userId);

    /**
     * 统计用户各类型的支付记录金额总和
     */
    @Select("SELECT type, SUM(amount) as total FROM tb_payment WHERE user_id = #{userId} AND status = 'success' GROUP BY type")
    List<Map<String, Object>> sumPaymentsByUserIdGroupByType(Integer userId);

    /**
     * 批量更新订单相关的支付记录状态
     */
    @Update("UPDATE tb_payment SET status = #{status}, transaction_id = #{transactionId}, " +
            "pay_time = NOW(), update_time = NOW() WHERE order_id = #{orderId} AND status = 'pending'")
    int updateOrderPaymentStatus(@Param("orderId") Integer orderId, @Param("status") String status,
                                 @Param("transactionId") String transactionId);
} 