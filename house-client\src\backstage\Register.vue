<template>
  <div class="register-container">
    <div class="register-box">
      <div class="register-header">
        <h2>房屋租赁管理系统</h2>
        <p>房东账号注册</p>
      </div>
      
      <el-steps :active="activeStep" finish-status="success" simple class="register-steps">
        <el-step title="填写账号信息"></el-step>
        <el-step title="完善个人资料"></el-step>
        <el-step title="注册成功"></el-step>
      </el-steps>
      
      <!-- 步骤1：账号信息 -->
      <el-form v-if="activeStep === 0" ref="accountForm" :model="registerForm" :rules="accountRules" class="register-form" label-position="top">
        <el-form-item prop="username" label="用户名">
          <el-input 
            v-model="registerForm.username" 
            prefix-icon="el-icon-user" 
            placeholder="请输入用户名（3-20个字符）">
          </el-input>
        </el-form-item>
        
        <el-form-item prop="password" label="密码">
          <el-input 
            v-model="registerForm.password" 
            prefix-icon="el-icon-lock" 
            placeholder="请输入密码（6-20个字符）" 
            show-password>
          </el-input>
        </el-form-item>
        
        <el-form-item prop="confirmPassword" label="确认密码">
          <el-input 
            v-model="registerForm.confirmPassword" 
            prefix-icon="el-icon-lock" 
            placeholder="请再次输入密码" 
            show-password>
          </el-input>
        </el-form-item>
        
        <el-form-item prop="email" label="电子邮箱">
          <el-input 
            v-model="registerForm.email" 
            prefix-icon="el-icon-message" 
            placeholder="请输入电子邮箱">
          </el-input>
        </el-form-item>
        
        <el-form-item prop="phone" label="手机号码">
          <el-input 
            v-model="registerForm.phone" 
            prefix-icon="el-icon-mobile-phone" 
            placeholder="请输入手机号码">
          </el-input>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" class="register-button" @click="nextStep">下一步</el-button>
        </el-form-item>
        
        <div class="login-link">
          <span>已有账号?</span>
          <router-link to="/backstage/login">立即登录</router-link>
        </div>
      </el-form>
      
      <!-- 步骤2：个人资料 -->
      <el-form v-else-if="activeStep === 1" ref="profileForm" :model="registerForm" :rules="profileRules" class="register-form" label-position="top">
        <el-form-item prop="realName" label="真实姓名">
          <el-input 
            v-model="registerForm.realName" 
            prefix-icon="el-icon-user" 
            placeholder="请输入您的真实姓名">
          </el-input>
        </el-form-item>
        
        <el-form-item prop="idCard" label="身份证号">
          <el-input 
            v-model="registerForm.idCard" 
            prefix-icon="el-icon-document" 
            placeholder="请输入您的身份证号">
          </el-input>
        </el-form-item>
        
        <el-form-item prop="address" label="联系地址">
          <el-input 
            v-model="registerForm.address" 
            prefix-icon="el-icon-location" 
            placeholder="请输入您的联系地址">
          </el-input>
        </el-form-item>
        
        <el-form-item prop="avatar" label="头像上传">
          <el-upload
            class="avatar-uploader"
            action="http://localhost:9002/file/upload"
            :show-file-list="false"
            :on-success="handleAvatarSuccess"
            :before-upload="beforeAvatarUpload">
            <img v-if="registerForm.avatar" :src="getImageUrl(registerForm.avatar)" class="avatar">
            <i v-else class="el-icon-plus avatar-uploader-icon"></i>
          </el-upload>
          <div class="upload-tip">请上传清晰的头像照片</div>
        </el-form-item>
        
        <el-form-item prop="agreement" label="用户协议">
          <el-checkbox v-model="registerForm.agreement">
            我已阅读并同意<a href="javascript:;" @click="showAgreement">《用户协议》</a>和<a href="javascript:;" @click="showPrivacy">《隐私政策》</a>
          </el-checkbox>
        </el-form-item>
        
        <el-form-item>
          <el-button type="default" class="prev-button" @click="prevStep">上一步</el-button>
          <el-button type="primary" class="register-button" @click="submitRegistration" :loading="loading">提交注册</el-button>
        </el-form-item>
      </el-form>
      
      <!-- 步骤3：注册成功 -->
      <div v-else-if="activeStep === 2" class="success-container">
        <div class="success-icon">
          <i class="el-icon-success"></i>
        </div>
        <h3>注册成功！</h3>
        <p>您的房东账号已创建成功，现在可以登录系统发布和管理您的房源。</p>
        <el-button type="primary" class="login-now-button" @click="goToLogin">立即登录</el-button>
      </div>
      
      <div class="register-footer">
        <p>Copyright © 2023 房屋租赁管理系统</p>
      </div>
    </div>
  </div>
</template>

<script>
import { getImageUrl } from '../utils/imageUtils'
export default {
  name: 'OwnerRegister',
  data() {
    // 确认密码验证规则
    const validateConfirmPassword = (rule, value, callback) => {
      if (value !== this.registerForm.password) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }
    
    // 身份证号验证规则
    const validateIdCard = (rule, value, callback) => {
      const reg = /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/
      if (!reg.test(value)) {
        callback(new Error('请输入正确的身份证号码'))
      } else {
        callback()
      }
    }
    
    return {
      activeStep: 0,
      loading: false,
      registerForm: {
        username: '',
        password: '',
        confirmPassword: '',
        email: '',
        phone: '',
        realName: '',
        idCard: '',
        address: '',
        avatar: '',
        agreement: false,
        role: 'owner' // 默认注册为房东角色
      },
      accountRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请确认密码', trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入电子邮箱', trigger: 'blur' },
          { type: 'email', message: '请输入正确的电子邮箱地址', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入手机号码', trigger: 'blur' },
          { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
        ]
      },
      profileRules: {
        realName: [
          { required: true, message: '请输入真实姓名', trigger: 'blur' },
          { min: 2, max: 20, message: '长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        idCard: [
          { required: true, message: '请输入身份证号', trigger: 'blur' },
          { validator: validateIdCard, trigger: 'blur' }
        ],
        address: [
          { required: true, message: '请输入联系地址', trigger: 'blur' }
        ],
        agreement: [
          { required: true, message: '请阅读并同意用户协议', trigger: 'change', type: 'boolean' }
        ]
      }
    }
  },
  computed: {
    // 为了在模板中使用getImageUrl
    getImageUrl() {
      return getImageUrl;
    }
  },
  methods: {
    // 下一步
    nextStep() {
      this.$refs.accountForm.validate(valid => {
        if (valid) {
          // 检查用户名是否已存在
          this.checkUsernameExists(this.registerForm.username)
            .then(exists => {
              if (exists) {
                this.$message.error('该用户名已被注册，请更换其他用户名')
                return
              }
              
              // 检查邮箱是否已存在
              this.checkEmailExists(this.registerForm.email)
                .then(exists => {
                  if (exists) {
                    this.$message.error('该邮箱已被注册，请更换其他邮箱')
                    return
                  }
                  
                  // 检查手机号是否已存在
                  this.checkPhoneExists(this.registerForm.phone)
                    .then(exists => {
                      if (exists) {
                        this.$message.error('该手机号已被注册，请更换其他手机号')
                        return
                      }
                      
                      // 所有检查通过，进入下一步
                      this.activeStep = 1
                    })
                })
            })
        } else {
          return false
        }
      })
    },
    
    // 上一步
    prevStep() {
      this.activeStep = 0
    },
    
    // 检查用户名是否存在
    async checkUsernameExists(username) {
      try {
        const response = await this.$http.get(`/admin/check-username?username=${username}`)
        return response.data.data
      } catch (error) {
        console.error('检查用户名失败', error)
        return false
      }
    },
    
    // 检查邮箱是否存在
    async checkEmailExists(email) {
      try {
        const response = await this.$http.get(`/admin/check-email?email=${email}`)
        return response.data.data
      } catch (error) {
        console.error('检查邮箱失败', error)
        return false
      }
    },
    
    // 检查手机号是否存在
    async checkPhoneExists(phone) {
      try {
        const response = await this.$http.get(`/admin/check-phone?phone=${phone}`)
        return response.data.data
      } catch (error) {
        console.error('检查手机号失败', error)
        return false
      }
    },
    
    // 上传头像 - 移除此方法，使用Element UI自带的上传功能
    /* 移除以下方法
    async uploadAvatar(options) {
      const formData = new FormData()
      formData.append('file', options.file)
      
      try {
        const response = await this.$http.post('/upload/avatar', formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })
        
        if (response.data && response.data.flag) {
          this.registerForm.avatar = response.data.data
          options.onSuccess()
        } else {
          options.onError(new Error(response.data.message || '上传失败'))
        }
      } catch (error) {
        console.error('上传头像失败', error)
        options.onError(new Error('上传头像失败'))
      }
    },
    */
    
    // 头像上传成功
    handleAvatarSuccess(res, file) {
      if (res.flag) {
        // 获取上传成功后的文件路径
        const avatarUrl = res.data || '';
        console.log('上传成功，获取到的头像路径：', avatarUrl);
        
        // 使用imageUtils处理头像路径
        let formattedAvatarUrl = avatarUrl;
        if (avatarUrl && !avatarUrl.startsWith('/api/images/') && !avatarUrl.startsWith('http')) {
          // 如果是文件名，则添加前缀
          if (!avatarUrl.includes('/')) {
            formattedAvatarUrl = `/api/images/${avatarUrl}`;
          } 
          // 如果是/images/开头，则替换为/api/images/
          else if (avatarUrl.startsWith('/images/')) {
            formattedAvatarUrl = `/api/images/${avatarUrl.substring(8)}`;
          }
        }
        
        console.log('格式化后的头像路径：', formattedAvatarUrl);
        
        // 将头像路径保存到表单中
        this.registerForm.avatar = formattedAvatarUrl;
      } else {
        this.$message.error('头像上传失败');
      }
    },
    
    // 头像上传前验证
    beforeAvatarUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png'
      const isLt20M = file.size / 1024 / 1024 < 20
      
      if (!isJPG) {
        this.$message.error('头像只能是JPG或PNG格式!')
      }
      if (!isLt20M) {
        this.$message.error('头像大小不能超过20MB!')
      }
      return isJPG && isLt20M
    },
    
    // 提交注册
    submitRegistration() {
      this.$refs.profileForm.validate(async valid => {
        if (valid) {
          this.loading = true
          try {
            // 准备注册数据
            const registerData = {
              username: this.registerForm.username,
              password: this.registerForm.password,
              email: this.registerForm.email,
              phone: this.registerForm.phone,
              realName: this.registerForm.realName,
              idCard: this.registerForm.idCard,
              address: this.registerForm.address,
              avatar: this.registerForm.avatar,
              role: this.registerForm.role
            }
            
            // 调用注册API
            const response = await this.$http.post('/admin/register', registerData)
            
            if (response.data && response.data.flag) {
              this.$message({
                message: '注册成功',
                type: 'success'
              })
              // 进入成功页面
              this.activeStep = 2
            } else {
              this.$message.error(response.data.message || '注册失败，请稍后再试')
            }
          } catch (error) {
            console.error('注册请求失败', error)
            this.$message.error('注册失败，请稍后再试')
          } finally {
            this.loading = false
          }
        } else {
          return false
        }
      })
    },
    
    // 跳转到登录页
    goToLogin() {
      this.$router.push('/backstage/login')
    },
    
    // 显示用户协议
    showAgreement() {
      this.$alert('用户协议内容...', '用户协议', {
        confirmButtonText: '我已阅读',
        callback: action => {}
      })
    },
    
    // 显示隐私政策
    showPrivacy() {
      this.$alert('隐私政策内容...', '隐私政策', {
        confirmButtonText: '我已阅读',
        callback: action => {}
      })
    }
  }
}
</script>

<style scoped>
.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 40px 0;
}

.register-box {
  width: 500px;
  padding: 40px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.register-header {
  text-align: center;
  margin-bottom: 30px;
}

.register-header h2 {
  font-size: 24px;
  color: #333;
  margin-bottom: 10px;
}

.register-header p {
  font-size: 14px;
  color: #666;
}

.register-steps {
  margin-bottom: 30px;
}

.register-form {
  margin-bottom: 20px;
}

.register-button {
  width: 100%;
  padding: 12px 0;
}

.prev-button {
  width: 48%;
  margin-right: 4%;
}

.register-button {
  width: 48%;
}

.login-link {
  text-align: center;
  margin-top: 15px;
  font-size: 14px;
  color: #666;
}

.login-link a {
  color: #409EFF;
  text-decoration: none;
  margin-left: 5px;
}

.register-footer {
  text-align: center;
  margin-top: 30px;
  font-size: 12px;
  color: #999;
}

.avatar-uploader {
  text-align: center;
}

.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  display: inline-block;
}

.avatar-uploader .el-upload:hover {
  border-color: #409EFF;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}

.avatar {
  width: 100px;
  height: 100px;
  display: block;
  object-fit: cover;
}

.upload-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 10px;
  text-align: center;
}

.success-container {
  text-align: center;
  padding: 30px 0;
}

.success-icon {
  font-size: 60px;
  color: #67C23A;
  margin-bottom: 20px;
}

.success-container h3 {
  font-size: 20px;
  color: #333;
  margin-bottom: 15px;
}

.success-container p {
  font-size: 14px;
  color: #666;
  margin-bottom: 25px;
}

.login-now-button {
  padding: 12px 30px;
}
</style> 