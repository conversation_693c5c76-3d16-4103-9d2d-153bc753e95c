package com.house.dto;

import lombok.Data;

/**
 * 房源查询条件DTO
 */
@Data
public class HouseQueryDTO {
    
    /**
     * 关键词（地址、详情等）
     */
    private String keyword;
    
    /**
     * 最小价格
     */
    private Double minPrice;
    
    /**
     * 最大价格
     */
    private Double maxPrice;
    
    /**
     * 房源状态
     */
    private String status;
    
    /**
     * 区域
     */
    private String area;
    
    /**
     * 房东ID
     */
    private Integer ownerId;
    
    /**
     * 页码
     */
    private Integer pageNum = 1;
    
    /**
     * 每页大小
     */
    private Integer pageSize = 10;
} 