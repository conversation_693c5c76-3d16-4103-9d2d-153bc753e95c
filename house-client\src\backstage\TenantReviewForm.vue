<template>
  <div class="tenant-review-form-container">
    <el-form ref="reviewForm" :model="reviewForm" :rules="rules" label-width="100px">
      <el-form-item label="总体评分" prop="rating">
        <el-rate
          v-model="reviewForm.rating"
          :colors="['#99A9BF', '#F7BA2A', '#FF9900']"
          :texts="['很差', '较差', '一般', '较好', '很好']"
          show-text
          :max="5"
        ></el-rate>
      </el-form-item>
      
      <div class="aspect-ratings">
        <h4>各方面评分</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="付款及时性">
              <el-rate v-model="reviewForm.paymentRating" :max="5"></el-rate>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="沟通配合度">
              <el-rate v-model="reviewForm.communicationRating" :max="5"></el-rate>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="房屋爱护度">
              <el-rate v-model="reviewForm.careRating" :max="5"></el-rate>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      
      <el-form-item label="评价内容" prop="content">
        <el-input
          type="textarea"
          v-model="reviewForm.content"
          :rows="4"
          placeholder="请分享您对这位租客的评价，如付款情况、沟通配合情况、退房状况等..."
        ></el-input>
      </el-form-item>
      
      <el-form-item label="推荐标签">
        <el-checkbox-group v-model="reviewForm.tags">
          <el-checkbox label="信用良好">信用良好</el-checkbox>
          <el-checkbox label="付款及时">付款及时</el-checkbox>
          <el-checkbox label="保持整洁">保持整洁</el-checkbox>
          <el-checkbox label="遵守规则">遵守规则</el-checkbox>
          <el-checkbox label="沟通顺畅">沟通顺畅</el-checkbox>
          <el-checkbox label="爱护房屋">爱护房屋</el-checkbox>
          <el-checkbox label="邻里和谐">邻里和谐</el-checkbox>
          <el-checkbox label="安静有礼">安静有礼</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      
      <el-form-item>
        <el-button type="primary" @click="submitReview">提交评价</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'TenantReviewForm',
  props: {
    orderId: {
      type: [Number, String],
      required: true
    },
    orderNo: {
      type: String,
      required: true
    },
    tenantId: {
      type: [Number, String],
      required: true
    },
    houseId: {
      type: [Number, String],
      required: true
    }
  },
  data() {
    return {
      reviewForm: {
        rating: 5,
        paymentRating: 5,
        communicationRating: 5,
        careRating: 5,
        content: '',
        tags: []
      },
      rules: {
        rating: [
          { required: true, message: '请选择总体评分', trigger: 'change' }
        ],
        content: [
          { required: true, message: '请填写评价内容', trigger: 'blur' },
          { min: 10, message: '评价内容最少10个字', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    // 提交评价
    submitReview() {
      this.$refs.reviewForm.validate(async (valid) => {
        if (valid) {
          try {
            // 计算各方面评分的平均值作为总评分
            const aspectRatings = [
              this.reviewForm.paymentRating,
              this.reviewForm.communicationRating,
              this.reviewForm.careRating
            ];
            const averageRating = aspectRatings.reduce((sum, rating) => sum + rating, 0) / aspectRatings.length;
            
            // 构建评价数据
            const reviewData = {
              orderId: this.orderId,
              orderNo: this.orderNo,
              tenantId: this.tenantId,
              houseId: this.houseId,
              rating: this.reviewForm.rating,
              content: this.reviewForm.content,
              paymentRating: this.reviewForm.paymentRating,
              communicationRating: this.reviewForm.communicationRating,
              careRating: this.reviewForm.careRating,
              averageRating: averageRating.toFixed(1),
              tags: this.reviewForm.tags.join(','),
              reviewType: 'tenant' // 标记为对租客的评价
            };
            
            // 调用API提交评价
            const res = await this.$http.post('/tenant-reviews', reviewData);
            
            if (res.data && res.data.flag) {
              this.$message.success('评价提交成功');
              this.$emit('review-submitted', res.data.data);
              this.resetForm();
            } else {
              this.$message.error(res.data.message || '评价提交失败');
            }
          } catch (error) {
            console.error('提交评价失败:', error);
            this.$message.error('提交评价失败');
          }
        } else {
          return false;
        }
      });
    },
    
    // 重置表单
    resetForm() {
      this.$refs.reviewForm.resetFields();
      this.reviewForm.tags = [];
    }
  }
}
</script>

<style scoped>
.tenant-review-form-container {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.aspect-ratings {
  margin: 20px 0;
  padding: 15px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.aspect-ratings h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #606266;
  font-weight: normal;
}
</style> 