<template>
  <div class="frontend-home">
    <!-- 使用Header组件 -->
    <frontend-header :active-index="activeIndex"></frontend-header>

    <!-- 轮播图 -->
    <div class="banner-container">
      <el-carousel height="400px">
        <el-carousel-item v-for="item in bannerList" :key="item.id">
          <div class="banner-content" :style="{ backgroundImage: `url(${item.imgUrl})` }">
            <div class="banner-text">
              <h2>{{ item.title }}</h2>
              <p>{{ item.desc }}</p>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>

    <!-- 搜索区域 -->
    <div class="search-container">
      <div class="search-box">
        <el-input placeholder="请输入地址、小区名" v-model="searchParams.keyword" class="search-input" clearable>
          <template #prepend>
            <i class="el-icon-search"></i>
          </template>
        </el-input>
        <el-select v-model="searchParams.price" placeholder="租金范围" class="search-select">
          <el-option label="不限" value=""></el-option>
          <el-option label="1000以下" value="0-1000"></el-option>
          <el-option label="1000-2000" value="1000-2000"></el-option>
          <el-option label="2000-3000" value="2000-3000"></el-option>
          <el-option label="3000-5000" value="3000-5000"></el-option>
          <el-option label="5000以上" value="5000-999999"></el-option>
        </el-select>
        <el-button type="primary" @click="searchHouse" class="search-button right-side-btn">搜索房源</el-button>
      </div>
    </div>

    <!-- 推荐房源 -->
    <div class="recommend-container">
      <div class="section-title">
        <h2>精选房源推荐</h2>
        <router-link to="/frontend/houses" class="view-more-link">查看更多</router-link>
      </div>
      <div class="house-list">
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="house in recommendHouses" :key="house.houseId">
            <div @click="viewHouseDetail(house.houseId)" style="cursor: pointer;">
              <el-card class="house-card" shadow="hover">
                <div class="house-img" :style="{ backgroundImage: `url(${house.imageUrl || '../assets/showcase.jpg'})` }"></div>
                <div class="house-info">
                  <h3 class="house-title">{{ house.address }}</h3>
                  <div class="house-tags">
                    <el-tag size="small" effect="plain">{{ house.status }}</el-tag>
                  </div>
                  <div class="house-price">{{ house.price }} 元/月</div>
                  <div class="house-desc">{{ house.detail }}</div>
                </div>
              </el-card>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 租房资讯 -->
    <div class="news-container">
      <div class="section-title">
        <h2>租房资讯</h2>
        <router-link to="/frontend/news" class="view-more-link">查看更多</router-link>
      </div>
      <div class="news-list">
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" v-for="news in newsList" :key="news.id">
            <el-card class="news-card" shadow="hover">
              <div class="news-content">
                <h3>{{ news.title }}</h3>
                <p>{{ news.summary }}</p>
                <div class="news-date">{{ news.date }}</div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 使用Footer组件 -->
    <frontend-footer></frontend-footer>
    
    <!-- WebSocket通知组件 -->
    <frontend-websocket-notification />
  </div>
</template>

<script>
// 导入组件
import FrontendHeader from './components/Header.vue';
import FrontendFooter from './components/Footer.vue';
import FrontendWebsocketNotification from './components/WebSocketNotification.vue';
import houseApi from './api/house';
import { processHouseListImages } from '@/utils/imageUtils';

export default {
  name: "FrontendHome",
  // 注册组件
  components: {
    FrontendHeader,
    FrontendFooter,
    FrontendWebsocketNotification
  },
  data() {
    return {
      activeIndex: '/',
      searchParams: {
        keyword: '',
        price: ''
      },
      // 轮播图模拟数据
      bannerList: [
        {
          id: 1,
          title: '高品质租房体验',
          desc: '精选优质房源，让您的生活更加舒适',
          imgUrl: require('../assets/bg.jpg')
        },
        {
          id: 2,
          title: '便捷的租房流程',
          desc: '一站式服务，省时省心',
          imgUrl: require('../assets/showcase.jpg')
        },
        {
          id: 3,
          title: '安全可靠的平台保障',
          desc: '真实房源，安全交易',
          imgUrl: require('../assets/bg.jpg')
        }
      ],
      // 推荐房源数据
      recommendHouses: [],
      // 租房资讯模拟数据
      newsList: [
        {
          id: 1,
          title: '2025年租房市场走势分析',
          summary: '随着政策调整，租房市场预计将迎来新的发展机遇...',
          date: '2025-07-01'
        },
        {
          id: 2,
          title: '租房注意事项大全',
          summary: '签约前必看！这些租房陷阱千万要避开...',
          date: '2025-06-28'
        },
        {
          id: 3,
          title: '如何打造舒适的租住空间',
          summary: '小户型也能有大空间，租房装修指南...',
          date: '2025-06-25'
        },
        {
          id: 4,
          title: '房屋租赁合同必知条款',
          summary: '了解这些条款，保护自己的权益...',
          date: '2025-06-20'
        }
      ]
    };
  },
  created() {
    // 获取房源数据
    this.fetchHouseData();
  },
  methods: {
    // 获取房源数据
    fetchHouseData() {
      // 直接获取点击量排序的热门房源
      houseApi.getHotHouses(4)
        .then(response => {
          if (response.data && response.data.code === 20000) {
            // 获取热门房源数据（已按点击量排序）
            this.recommendHouses = processHouseListImages(response.data.data || []);
            
            // 如果热门房源不足4个，则获取所有可租房源补充
            if (this.recommendHouses.length < 4) {
              houseApi.getAllHouseList()
                .then(resp => {
                  if (resp.data && resp.data.code === 20000) {
                    // 过滤已经在推荐列表中的房源
                    const existingIds = this.recommendHouses.map(house => house.houseId);
                    const availableHouses = resp.data.data.filter(house => 
                      house.status === '可租' && !existingIds.includes(house.houseId)
                    );
                    
                    // 补充不足的房源
                    const remaining = 4 - this.recommendHouses.length;
                    if (remaining > 0 && availableHouses.length > 0) {
                      // 处理图片路径
                      const additionalHouses = processHouseListImages(availableHouses.slice(0, remaining));
                      this.recommendHouses = [
                        ...this.recommendHouses,
                        ...additionalHouses
                      ];
                    }
                  }
                })
                .catch(error => {
                  console.error('获取补充房源数据出错:', error);
                });
            }
          } else if (error && error.status === 401) {
            console.error('获取热门房源数据失败:', response.data);
            this.$message.error('请先登录后查看房源信息');
          }
        })
        .catch(error => {
          console.error('获取热门房源数据出错:', error);
          // 检查是否是401未登录错误
          if (error && error.status === 401) {
            this.$message.error('请先登录后查看房源信息');
          } else {
            this.$message.error('获取热门房源数据失败，请稍后再试');
          }
        });
    },
    searchHouse() {
      console.log('搜索参数：', this.searchParams);
      // 实际应用中这里应该是跳转到搜索结果页面
      this.$router.push({
        path: '/frontend/houses',
        query: this.searchParams
      });
    },
    viewHouseDetail(houseId) {
      console.log('Home.vue - 查看房源详情，房源ID：', houseId);
      // 使用name参数跳转到详情页，确保路由匹配正确
      try {
        this.$router.push({
          name: 'FrontendHouseDetail',
          params: { id: houseId }
        });
      } catch (err) {
        console.error('路由跳转失败:', err);
      }
    }
  }
};
</script>

<style scoped>
/* 添加滚动样式 */
.frontend-home {
  width: 100%;
  min-height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
  padding-top: 60px; /* 添加与导航栏高度相同的上边距 */
}

/* 链接样式 */
.view-more-link {
  color: #409EFF;
  text-decoration: none;
  font-size: 14px;
}

.view-more-link:hover {
  color: #66b1ff;
  text-decoration: underline;
}

/* 轮播图样式 */
.banner-container {
  width: 100%;
}

.banner-content {
  height: 400px;
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  padding: 0 100px;
}

.banner-text {
  background-color: rgba(0, 0, 0, 0.5);
  color: #fff;
  padding: 20px;
  border-radius: 5px;
  max-width: 500px;
}

.banner-text h2 {
  font-size: 28px;
  margin-bottom: 10px;
}

/* 搜索区域样式 */
.search-container {
  background-color: #f0f8ff;
  padding: 40px 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-bottom: 1px solid #e6f0fa;
}

.search-box {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.search-input {
  flex: 1;
  margin-right: 15px;
}

.search-input >>> .el-input__inner {
  height: 50px;
  border-radius: 4px;
  font-size: 16px;
  border: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.search-select {
  width: 150px;
  margin-right: 15px;
}

.search-select >>> .el-input__inner {
  height: 50px;
  border-radius: 4px;
  font-size: 16px;
  border: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* 推荐房源样式 */
.recommend-container, .news-container {
  padding: 30px;
  background-color: #fff;
  margin-bottom: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.section-title h2 {
  font-size: 22px;
  color: #333;
  position: relative;
  padding-left: 15px;
}

.section-title h2:before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 20px;
  background-color: #ff6b6b;
  border-radius: 2px;
}

.house-card {
  margin-bottom: 25px;
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 8px;
  overflow: hidden;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.house-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.house-img {
  height: 180px;
  background-size: cover;
  background-position: center;
  border-radius: 8px 8px 0 0;
  position: relative;
}

.house-info {
  padding: 15px;
}

.house-title {
  font-size: 18px;
  margin-bottom: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #333;
}

.house-tags {
  margin-bottom: 12px;
}

.house-price {
  color: #ff6b6b;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 12px;
}

.house-desc {
  color: #666;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
}

/* 租房资讯样式 */
.news-card {
  margin-bottom: 20px;
  height: 150px;
}

.news-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.news-content h3 {
  font-size: 16px;
  margin-bottom: 10px;
}

.news-content p {
  color: #666;
  font-size: 14px;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.news-date {
  color: #999;
  font-size: 12px;
  text-align: right;
}
</style> 