<template>
  <div class="checkin-page content-scroll">
    <Header />
    <div class="container">
      <div class="checkin-card">
        <h2>入住确认</h2>
        <el-divider></el-divider>
        
        <div class="checkin-content" v-if="orderData && houseData">
          <!-- 订单信息 -->
          <div class="order-info">
            <h3>订单信息</h3>
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="info-item">
                  <label>订单号：</label>
                  <span>{{ orderData.orderNo }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <label>订单状态：</label>
                  <el-tag :type="getStatusType(orderData.status)">{{ getStatusText(orderData.status) }}</el-tag>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="info-item">
                  <label>租期开始：</label>
                  <span>{{ formatDate(orderData.startDate) }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="info-item">
                  <label>租期结束：</label>
                  <span>{{ formatDate(orderData.endDate) }}</span>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 房源信息 -->
          <div class="house-info">
            <h3>房源信息</h3>
            <el-row :gutter="20">
              <el-col :span="8">
                <div class="house-image">
                  <img :src="houseData.imageUrl || require('../assets/showcase.jpg')" alt="房源图片" />
                </div>
              </el-col>
              <el-col :span="16">
                <div class="house-details">
                  <h4>{{ houseData.address }}</h4>
                  <p><strong>房源描述：</strong>{{ houseData.detail }}</p>
                  <p><strong>月租金：</strong><span class="price">¥{{ houseData.price }}</span></p>
                  <p><strong>房东：</strong>{{ ownerInfo.name || '未知' }}</p>
                  <p><strong>联系电话：</strong>{{ ownerInfo.phone || '未提供' }}</p>
                </div>
              </el-col>
            </el-row>
          </div>

          <!-- 入住确认信息 -->
          <div class="checkin-form">
            <h3>入住确认</h3>
            <el-form :model="checkinForm" :rules="checkinRules" ref="checkinForm" label-width="120px">
              <el-form-item label="实际入住日期" prop="actualCheckInDate">
                <el-date-picker
                  v-model="checkinForm.actualCheckInDate"
                  type="date"
                  placeholder="选择实际入住日期"
                  :picker-options="datePickerOptions"
                  style="width: 100%;">
                </el-date-picker>
              </el-form-item>
              
              <el-form-item label="房屋状况确认" prop="houseCondition">
                <el-radio-group v-model="checkinForm.houseCondition">
                  <el-radio label="good">房屋状况良好，无问题</el-radio>
                  <el-radio label="minor">有轻微问题，但不影响居住</el-radio>
                  <el-radio label="major">有较大问题，需要处理</el-radio>
                </el-radio-group>
              </el-form-item>
              
              <el-form-item label="问题描述" prop="issueDescription" v-if="checkinForm.houseCondition !== 'good'">
                <el-input
                  type="textarea"
                  v-model="checkinForm.issueDescription"
                  placeholder="请详细描述发现的问题"
                  :rows="3">
                </el-input>
              </el-form-item>
              
              <el-form-item label="备注" prop="remarks">
                <el-input
                  type="textarea"
                  v-model="checkinForm.remarks"
                  placeholder="其他需要说明的事项（可选）"
                  :rows="2">
                </el-input>
              </el-form-item>
              
              <el-form-item label="确认入住" prop="confirmCheckin">
                <el-checkbox v-model="checkinForm.confirmCheckin">
                  我确认已查看房屋状况，同意按照合同约定入住该房屋
                </el-checkbox>
              </el-form-item>
            </el-form>
          </div>

          <!-- 重要提醒 -->
          <div class="checkin-notice">
            <h3>重要提醒</h3>
            <div class="notice-content">
              <p>1. 请仔细检查房屋设施和状况，如有问题请及时反馈。</p>
              <p>2. 确认入住后，租期正式开始，请按时支付租金。</p>
              <p>3. 如发现房屋与描述不符或存在安全隐患，请立即联系房东。</p>
              <p>4. 入住确认后，押金将转为履约保证金。</p>
            </div>
          </div>
        </div>
        
        <div v-else class="loading-skeleton">
          <el-skeleton :rows="10" animated />
        </div>
        
        <!-- 底部操作按钮 -->
        <div class="checkin-footer">
          <el-button @click="goBack">返回</el-button>
          <el-button @click="goToContract">查看合同</el-button>
          <el-button 
            type="primary" 
            @click="confirmCheckIn" 
            :loading="submitting"
            :disabled="!canConfirmCheckIn">
            确认入住
          </el-button>
        </div>
      </div>
    </div>
    <Footer />
  </div>
</template>

<script>
import Header from './components/Header'
import Footer from './components/Footer'
import { getOrderByOrderNo } from '@/api/order'
import { confirmCheckIn } from '@/api/order'
import { getUser } from '@/utils/auth'
import moment from 'moment'

export default {
  name: 'CheckIn',
  components: {
    Header,
    Footer
  },
  data() {
    return {
      orderNo: '',
      orderData: null,
      houseData: null,
      ownerInfo: {},
      userInfo: null,
      submitting: false,
      checkinForm: {
        actualCheckInDate: new Date(),
        houseCondition: 'good',
        issueDescription: '',
        remarks: '',
        confirmCheckin: false
      },
      checkinRules: {
        actualCheckInDate: [
          { required: true, message: '请选择实际入住日期', trigger: 'change' }
        ],
        houseCondition: [
          { required: true, message: '请选择房屋状况', trigger: 'change' }
        ],
        issueDescription: [
          { 
            validator: (rule, value, callback) => {
              if (this.checkinForm.houseCondition !== 'good' && (!value || value.trim() === '')) {
                callback(new Error('请描述发现的问题'));
              } else {
                callback();
              }
            }, 
            trigger: 'blur' 
          }
        ],
        confirmCheckin: [
          { 
            validator: (rule, value, callback) => {
              if (!value) {
                callback(new Error('请确认同意入住'));
              } else {
                callback();
              }
            }, 
            trigger: 'change' 
          }
        ]
      },
      datePickerOptions: {
        disabledDate(time) {
          // 不能选择今天之前的日期
          return time.getTime() < Date.now() - 8.64e7;
        }
      }
    }
  },
  computed: {
    canConfirmCheckIn() {
      return this.orderData && 
             this.orderData.status === 'pending' && 
             this.orderData.contractSignTime && 
             this.checkinForm.confirmCheckin;
    }
  },
  created() {
    this.orderNo = this.$route.params.orderNo;
    this.userInfo = getUser();
    
    if (!this.orderNo) {
      this.$message.error('订单号不能为空');
      this.$router.push('/frontend/orderlist');
      return;
    }
    
    this.fetchOrderData();
  },
  methods: {
    // 获取订单数据
    async fetchOrderData() {
      try {
        const res = await getOrderByOrderNo(this.orderNo);
        
        if (res.data && res.data.flag) {
          this.orderData = res.data.data;
          
          // 检查用户权限
          const userId = this.userInfo.id || (this.userInfo.userInfo && this.userInfo.userInfo.id);
          if (userId !== this.orderData.tenantId) {
            this.$message.warning('您无权进行此操作');
            this.$router.push('/frontend/orderlist');
            return;
          }
          
          // 检查订单状态
          if (this.orderData.status !== 'pending') {
            this.$message.warning('订单状态不正确，无法确认入住');
            this.$router.push('/frontend/orderlist');
            return;
          }
          
          // 检查是否已签署合同
          if (!this.orderData.contractSignTime) {
            this.$message.warning('合同尚未签署，无法确认入住');
            this.$router.push(`/frontend/contract/${this.orderNo}`);
            return;
          }
          
          // 获取房源信息
          this.houseData = this.orderData.house || {};
          this.ownerInfo = this.orderData.owner || {};
          
        } else {
          const errorMsg = res.data && res.data.message ? res.data.message : '获取订单信息失败';
          this.$message.error(errorMsg);
          this.$router.push('/frontend/orderlist');
        }
      } catch (error) {
        console.error('获取订单数据失败:', error);
        this.$message.error('获取订单数据失败');
        this.$router.push('/frontend/orderlist');
      }
    },
    
    // 确认入住
    async confirmCheckIn() {
      try {
        // 表单验证
        await this.$refs.checkinForm.validate();
        
        await this.$confirm('确认要提交入住确认吗？提交后将无法修改。', '确认入住', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        });
        
        this.submitting = true;
        
        // 调用确认入住API
        const res = await confirmCheckIn(this.orderNo);
        
        if (res.data && res.data.flag) {
          this.$message.success('入住确认成功！');
          
          // 跳转到订单详情页面
          this.$router.push(`/frontend/orderdetail/${this.orderNo}`);
        } else {
          const errorMsg = res.data && res.data.message ? res.data.message : '入住确认失败';
          this.$message.error(errorMsg);
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('入住确认失败:', error);
          this.$message.error('入住确认失败: ' + (error.response ? error.response.data.message : error.message));
        }
      } finally {
        this.submitting = false;
      }
    },
    
    // 格式化日期
    formatDate(date) {
      if (!date) return '';
      return moment(date).format('YYYY-MM-DD');
    },
    
    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        'pending': 'warning',
        'approved': 'success',
        'rejected': 'danger',
        'paid': 'success',
        'active': 'success',
        'completed': 'info',
        'cancelled': 'info'
      };
      return statusMap[status] || 'info';
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        'pending': '待确认',
        'approved': '已批准',
        'rejected': '已拒绝',
        'paid': '已支付',
        'active': '进行中',
        'completed': '已完成',
        'cancelled': '已取消'
      };
      return statusMap[status] || '未知状态';
    },
    
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    
    // 查看合同
    goToContract() {
      this.$router.push(`/frontend/contract/${this.orderNo}`);
    }
  }
}
</script>

<style scoped>
.checkin-page {
  background-color: #f8f8f8;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.container {
  flex: 1;
  max-width: 1200px;
  margin: 30px auto;
  padding: 0 20px;
}

.checkin-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 30px;
}

h2 {
  font-size: 24px;
  color: #333;
  margin-bottom: 20px;
}

h3 {
  font-size: 18px;
  color: #333;
  margin-bottom: 15px;
  border-left: 4px solid #409EFF;
  padding-left: 10px;
}

.order-info, .house-info, .checkin-form, .checkin-notice {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #fafafa;
  border-radius: 6px;
}

.info-item {
  margin-bottom: 10px;
}

.info-item label {
  font-weight: bold;
  color: #666;
  margin-right: 10px;
}

.house-image img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 6px;
}

.house-details h4 {
  font-size: 20px;
  color: #333;
  margin-bottom: 15px;
}

.house-details p {
  margin-bottom: 10px;
  color: #666;
}

.price {
  color: #ff6b6b;
  font-size: 18px;
  font-weight: bold;
}

.notice-content {
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
  padding: 15px;
}

.notice-content p {
  margin-bottom: 8px;
  color: #856404;
}

.checkin-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.loading-skeleton {
  padding: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    margin: 20px auto;
    padding: 0 15px;
  }
  
  .checkin-card {
    padding: 20px;
  }
  
  .checkin-footer {
    flex-direction: column;
    gap: 10px;
  }
  
  .checkin-footer .el-button {
    width: 100%;
  }
}
</style>
