-- 添加合同表
CREATE TABLE IF NOT EXISTS `tb_contract` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `contract_no` varchar(50) NOT NULL COMMENT '合同编号',
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `content` text COMMENT '合同内容',
  `tenant_id` int(11) NOT NULL COMMENT '租客ID',
  `owner_id` int(11) NOT NULL COMMENT '房东ID',
  `tenant_signed` tinyint(1) DEFAULT '0' COMMENT '租客是否已签署',
  `tenant_sign_time` datetime DEFAULT NULL COMMENT '租客签署时间',
  `tenant_signature` varchar(100) DEFAULT NULL COMMENT '租客签名',
  `owner_signed` tinyint(1) DEFAULT '0' COMMENT '房东是否已签署',
  `owner_sign_time` datetime DEFAULT NULL COMMENT '房东签署时间',
  `owner_signature` varchar(100) DEFAULT NULL COMMENT '房东签名',
  `status` varchar(20) DEFAULT 'pending' COMMENT '合同状态：pending - 待签署, signed - 已签署, active - 生效中, expired - 已过期, terminated - 已终止',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_contract_no` (`contract_no`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_owner_id` (`owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='合同表';

-- 检查是否已有signature字段，如果没有则添加
ALTER TABLE `tb_contract` ADD COLUMN IF NOT EXISTS `tenant_signature` varchar(100) DEFAULT NULL COMMENT '租客签名';
ALTER TABLE `tb_contract` ADD COLUMN IF NOT EXISTS `owner_signature` varchar(100) DEFAULT NULL COMMENT '房东签名'; 