<template>
  <div class="review-list">
    <!-- 评价统计信息 -->
    <div class="review-stats" v-if="houseId">
      <div class="rating-overview">
        <div class="rating-score">
          <span class="score">{{ stats.averageRating || 0 }}</span>
          <el-rate
            :value="stats.averageRating || 0"
            disabled
            show-score
            text-color="#ff9900"
            :colors="['#FF9900', '#FF9900', '#FF9900']"
          ></el-rate>
        </div>
        <div class="rating-count">
          <span>{{ stats.totalReviews || 0 }}条评价</span>
        </div>
      </div>
      
      <div class="rating-aspects" v-if="stats.totalReviews > 0">
        <div class="aspect-item">
          <span class="aspect-name">位置便利性</span>
          <el-progress 
            :percentage="Math.round(stats.locationRating/5*100 || 0)" 
            :stroke-width="8" 
            :show-text="false" 
            :color="progressColor"
          ></el-progress>
          <span class="aspect-score">{{ stats.locationRating || 0 }}</span>
        </div>
        <div class="aspect-item">
          <span class="aspect-name">房屋设施</span>
          <el-progress 
            :percentage="Math.round(stats.facilityRating/5*100 || 0)" 
            :stroke-width="8" 
            :show-text="false" 
            :color="progressColor"
          ></el-progress>
          <span class="aspect-score">{{ stats.facilityRating || 0 }}</span>
        </div>
        <div class="aspect-item">
          <span class="aspect-name">房东服务</span>
          <el-progress 
            :percentage="Math.round(stats.serviceRating/5*100 || 0)" 
            :stroke-width="8" 
            :show-text="false" 
            :color="progressColor"
          ></el-progress>
          <span class="aspect-score">{{ stats.serviceRating || 0 }}</span>
        </div>
        <div class="aspect-item">
          <span class="aspect-name">性价比</span>
          <el-progress 
            :percentage="Math.round(stats.valueRating/5*100 || 0)" 
            :stroke-width="8" 
            :show-text="false" 
            :color="progressColor"
          ></el-progress>
          <span class="aspect-score">{{ stats.valueRating || 0 }}</span>
        </div>
        <div class="aspect-item">
          <span class="aspect-name">周边环境</span>
          <el-progress 
            :percentage="Math.round(stats.environmentRating/5*100 || 0)" 
            :stroke-width="8" 
            :show-text="false" 
            :color="progressColor"
          ></el-progress>
          <span class="aspect-score">{{ stats.environmentRating || 0 }}</span>
        </div>
        <div class="aspect-item">
          <span class="aspect-name">卫生情况</span>
          <el-progress 
            :percentage="Math.round(stats.cleanlinessRating/5*100 || 0)" 
            :stroke-width="8" 
            :show-text="false" 
            :color="progressColor"
          ></el-progress>
          <span class="aspect-score">{{ stats.cleanlinessRating || 0 }}</span>
        </div>
      </div>
      
      <div class="review-tags" v-if="tags.length > 0">
        <div class="tag-item" v-for="(tag, index) in tags" :key="index">
          <el-tag size="small">{{ tag.name }} ({{ tag.count }})</el-tag>
        </div>
      </div>
    </div>
    
    <!-- 评价过滤器 -->
    <div class="review-filter" v-if="reviews.length > 0">
      <el-select v-model="filterRating" placeholder="按评分筛选" size="small" @change="handleFilterChange">
        <el-option label="全部评分" value=""></el-option>
        <el-option label="5分" value="5"></el-option>
        <el-option label="4分" value="4"></el-option>
        <el-option label="3分" value="3"></el-option>
        <el-option label="2分" value="2"></el-option>
        <el-option label="1分" value="1"></el-option>
      </el-select>
      
      <el-select v-model="sortOrder" placeholder="排序方式" size="small" @change="handleSortChange">
        <el-option label="最新优先" value="newest"></el-option>
        <el-option label="最早优先" value="oldest"></el-option>
        <el-option label="评分由高到低" value="rating_desc"></el-option>
        <el-option label="评分由低到高" value="rating_asc"></el-option>
      </el-select>
      
      <el-select v-model="filterTags" placeholder="按标签筛选" size="small" @change="handleFilterChange" v-if="tags.length > 0">
        <el-option label="全部标签" value=""></el-option>
        <el-option v-for="(tag, index) in tags" :key="index" :label="tag.name" :value="tag.name"></el-option>
      </el-select>
      
      <el-checkbox v-model="showPhotosOnly" @change="handleFilterChange">只看有图评价</el-checkbox>
    </div>
    
    <!-- 评价列表 -->
    <div class="review-items" v-loading="loading">
      <div v-if="filteredReviews.length === 0" class="no-reviews">
        <el-empty description="暂无评价" v-if="reviews.length === 0"></el-empty>
        <el-empty description="没有符合条件的评价" v-else></el-empty>
      </div>
      
      <div class="review-item" v-for="review in filteredReviews" :key="review.id">
        <div class="reviewer-info">
          <div class="reviewer-avatar">
            <img :src="review.userAvatar || require('@/assets/showcase.jpg')" alt="用户头像">
          </div>
          <div class="reviewer-detail">
            <div class="reviewer-name">{{ review.anonymous ? '匿名用户' : review.userName }}</div>
            <div class="review-time">{{ formatDate(review.createTime) }}</div>
          </div>
        </div>
        
        <div class="review-content">
          <div class="review-rating">
            <el-rate
              :value="review.rating"
              disabled
              text-color="#ff9900"
              :colors="['#FF9900', '#FF9900', '#FF9900']"
            ></el-rate>
          </div>
          
          <div class="review-text">{{ review.content }}</div>
          
          <div class="review-images" v-if="review.images && review.images.length > 0">
            <el-image
              v-for="(image, index) in review.images.split(',')"
              :key="index"
              :src="image"
              :preview-src-list="review.images.split(',')"
              fit="cover"
              class="review-image"
            ></el-image>
          </div>
          
          <div class="review-tags" v-if="review.tags">
            <el-tag size="mini" v-for="(tag, index) in review.tags.split(',')" :key="index" type="info">{{ tag }}</el-tag>
          </div>
          
          <div class="review-reply" v-if="review.reply">
            <div class="reply-title">房东回复：</div>
            <div class="reply-content">{{ review.reply }}</div>
            <div class="reply-time">{{ formatDate(review.replyTime) }}</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 分页 -->
    <div class="pagination" v-if="reviews.length > 0">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[5, 10, 20, 30]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="filteredReviews.length"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
import moment from 'moment'
import reviewApi from '../api/review'

export default {
  name: 'ReviewList',
  props: {
    houseId: {
      type: [Number, String],
      required: false
    }
  },
  data() {
    return {
      loading: false,
      reviews: [],
      stats: {
        averageRating: 0,
        totalReviews: 0,
        locationRating: 0,
        facilityRating: 0,
        serviceRating: 0,
        valueRating: 0,
        environmentRating: 0,
        cleanlinessRating: 0
      },
      tags: [],
      filterRating: '',
      filterTags: '',
      sortOrder: 'newest',
      showPhotosOnly: false,
      currentPage: 1,
      pageSize: 10,
      progressColor: {
        from: '#FF9900',
        to: '#FFD700'
      }
    }
  },
  computed: {
    // 过滤后的评价列表
    filteredReviews() {
      let result = [...this.reviews]
      
      // 按评分筛选
      if (this.filterRating) {
        result = result.filter(review => Math.floor(review.rating) === parseInt(this.filterRating))
      }
      
      // 按标签筛选
      if (this.filterTags) {
        result = result.filter(review => review.tags && review.tags.includes(this.filterTags))
      }
      
      // 只看有图评价
      if (this.showPhotosOnly) {
        result = result.filter(review => review.images && review.images.length > 0)
      }
      
      // 排序
      if (this.sortOrder === 'newest') {
        result.sort((a, b) => new Date(b.createTime) - new Date(a.createTime))
      } else if (this.sortOrder === 'oldest') {
        result.sort((a, b) => new Date(a.createTime) - new Date(b.createTime))
      } else if (this.sortOrder === 'rating_desc') {
        result.sort((a, b) => b.rating - a.rating)
      } else if (this.sortOrder === 'rating_asc') {
        result.sort((a, b) => a.rating - b.rating)
      }
      
      return result
    },
    
    // 当前页的评价
    currentPageReviews() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.filteredReviews.slice(start, end)
    }
  },
  watch: {
    houseId: {
      immediate: true,
      handler(val) {
        if (val) {
          this.fetchReviews()
        }
      }
    }
  },
  methods: {
    // 获取评价列表
    async fetchReviews() {
      if (!this.houseId) return
      
      this.loading = true
      try {
        // 获取评价统计
        const statsRes = await reviewApi.getHouseReviewStats(this.houseId)
        if (statsRes.data && statsRes.data.flag) {
          this.stats = statsRes.data.data

          // 处理标签
          if (statsRes.data.data.tags) {
            this.tags = statsRes.data.data.tags
          }
        }

        // 获取评价列表
        const reviewsRes = await reviewApi.getHouseReviews(this.houseId)
        if (reviewsRes.data && reviewsRes.data.flag) {
          this.reviews = reviewsRes.data.data
        }
      } catch (error) {
        console.error('获取评价失败:', error)
        this.$message.error('获取评价失败')
      } finally {
        this.loading = false
      }
    },
    
    // 处理筛选变化
    handleFilterChange() {
      this.currentPage = 1
    },
    
    // 处理排序变化
    handleSortChange() {
      this.currentPage = 1
    },
    
    // 处理页大小变化
    handleSizeChange(val) {
      this.pageSize = val
    },
    
    // 处理页码变化
    handleCurrentChange(val) {
      this.currentPage = val
    },
    
    // 格式化日期
    formatDate(dateStr) {
      return moment(dateStr).format('YYYY-MM-DD')
    }
  }
}
</script>

<style scoped>
.review-list {
  margin-bottom: 30px;
}

.review-stats {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.rating-overview {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.rating-score {
  display: flex;
  flex-direction: column;
  margin-right: 30px;
}

.rating-score .score {
  font-size: 36px;
  font-weight: bold;
  color: #FF9900;
  line-height: 1;
  margin-bottom: 5px;
}

.rating-count {
  font-size: 14px;
  color: #606266;
}

.rating-aspects {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 15px;
  margin-bottom: 20px;
}

.aspect-item {
  display: flex;
  align-items: center;
}

.aspect-name {
  width: 80px;
  font-size: 14px;
  color: #606266;
}

.aspect-score {
  width: 30px;
  text-align: right;
  font-size: 14px;
  color: #303133;
  margin-left: 10px;
}

.aspect-item .el-progress {
  flex: 1;
}

.review-tags {
  display: flex;
  flex-wrap: wrap;
}

.tag-item {
  margin-right: 10px;
  margin-bottom: 10px;
}

.review-filter {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.review-filter .el-select {
  margin-right: 15px;
  width: 140px;
}

.review-filter .el-checkbox {
  margin-left: auto;
}

.review-items {
  margin-bottom: 20px;
}

.no-reviews {
  padding: 30px 0;
}

.review-item {
  display: flex;
  padding: 20px 0;
  border-bottom: 1px solid #EBEEF5;
}

.reviewer-info {
  width: 120px;
  margin-right: 20px;
}

.reviewer-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  overflow: hidden;
  margin-bottom: 10px;
}

.reviewer-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.reviewer-name {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.review-time {
  font-size: 12px;
  color: #909399;
}

.review-content {
  flex: 1;
}

.review-rating {
  margin-bottom: 10px;
}

.review-text {
  font-size: 14px;
  color: #303133;
  line-height: 1.6;
  margin-bottom: 15px;
}

.review-images {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 15px;
}

.review-image {
  width: 100px;
  height: 100px;
  margin-right: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
  overflow: hidden;
}

.review-tags {
  margin-bottom: 15px;
}

.review-tags .el-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

.review-reply {
  background-color: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
}

.reply-title {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.reply-content {
  font-size: 14px;
  color: #606266;
  line-height: 1.6;
  margin-bottom: 8px;
}

.reply-time {
  font-size: 12px;
  color: #909399;
  text-align: right;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}
</style> 