import request from '../../utils/request'

// 合同API
export default {
  // 获取合同详情
  getContractByOrderNo(orderNo) {
    return request({
      url: `/contracts/order/${orderNo}`,
      method: 'get'
    })
  },

  // 根据合同编号获取合同
  getContractByContractNo(contractNo) {
    return request({
      url: `/contracts/${contractNo}`,
      method: 'get'
    })
  },

  // 租客签署合同
  tenantSignContract(contractNo, tenantSignature) {
    return request({
      url: `/contracts/${contractNo}/sign`,
      method: 'put',
      data: {
        tenantSignTime: new Date(),
        tenantSignature: tenantSignature
      }
    })
  },

  // 房东签署合同
  ownerSignContract(contractNo, ownerSignature) {
    return request({
      url: `/contracts/${contractNo}/sign`,
      method: 'put',
      data: {
        ownerSignTime: new Date(),
        ownerSignature: ownerSignature
      }
    })
  },

  // 更新合同状态
  updateContractStatus(contractNo, status) {
    return request({
      url: `/contracts/${contractNo}`,
      method: 'put',
      data: { status }
    })
  },

  // 取消合同
  cancelContract(contractNo) {
    return request({
      url: `/contracts/${contractNo}/cancel`,
      method: 'put'
    })
  },

  // 获取合同列表
  getContractList(params) {
    return request({
      url: '/contracts',
      method: 'get',
      params
    })
  },

  // 创建合同
  createContract(contractData) {
    return request({
      url: '/contracts',
      method: 'post',
      data: contractData
    })
  }
} 