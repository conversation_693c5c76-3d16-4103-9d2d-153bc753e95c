<template>
  <!-- 这个文件只是为了注册自定义组件，不会被直接使用 -->
</template>

<script>
import Vue from 'vue';

// 自定义分割线组件
Vue.component('el-divider', {
  functional: false,
  render(h) {
    return h('div', {
      class: ['custom-divider']
    });
  }
});

// 自定义骨架屏组件
Vue.component('el-skeleton', {
  props: {
    rows: {
      type: Number,
      default: 4
    },
    animated: Boolean
  },
  render(h) {
    const rows = [];
    for (let i = 0; i < this.rows; i++) {
      rows.push(h('div', {
        class: ['skeleton-item', this.animated ? 'is-animated' : '']
      }));
    }
    return h('div', {
      class: ['custom-skeleton']
    }, rows);
  }
});

// 添加骨架屏项组件
Vue.component('el-skeleton-item', {
  props: {
    variant: {
      type: String,
      default: 'text'
    }
  },
  render(h) {
    return h('div', {
      class: ['skeleton-item', `is-${this.variant}`]
    });
  }
});

// 自定义描述列表组件
Vue.component('el-descriptions', {
  props: {
    column: {
      type: Number,
      default: 3
    },
    border: Boolean,
    title: String,
    direction: {
      type: String,
      default: 'horizontal'
    },
    size: String
  },
  render(h, context) {
    // 修复context未定义的问题
    const slots = this.$slots.default || [];
    
    return h('div', {
      class: ['custom-descriptions', this.border ? 'is-bordered' : '']
    }, [
      this.title ? h('div', { class: 'descriptions-title' }, this.title) : null,
      h('table', { class: 'descriptions-table' }, [
        h('tbody', {}, slots)
      ])
    ]);
  }
});

// 自定义描述列表项组件
Vue.component('el-descriptions-item', {
  props: {
    label: String,
    span: {
      type: Number,
      default: 1
    }
  },
  render(h) {
    // 修复context未定义的问题
    const slots = this.$slots.default || [];
    
    return h('tr', { class: 'descriptions-row' }, [
      h('th', { class: 'descriptions-item-label' }, this.label),
      h('td', { 
        class: 'descriptions-item-content',
        attrs: {
          colspan: this.span
        }
      }, slots)
    ]);
  }
});

export default {
  name: 'CustomElementComponents'
}
</script>

<style>
/* 分割线样式 */
.custom-divider {
  height: 1px;
  background-color: #ebeef5;
  margin: 24px 0;
  display: block;
  width: 100%;
}

/* 骨架屏样式 */
.custom-skeleton {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 16px;
}

.skeleton-item {
  height: 20px;
  background-color: #f2f2f2;
  border-radius: 4px;
}

.skeleton-item.is-animated {
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
  background-size: 400% 100%;
  animation: skeleton-loading 1.4s ease infinite;
}

.skeleton-item.is-circle {
  width: 36px;
  height: 36px;
  border-radius: 50%;
}

.skeleton-item.is-image {
  width: 100%;
  height: 160px;
}

.skeleton-item.is-button {
  width: 100px;
  height: 36px;
  border-radius: 18px;
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}

/* 描述列表样式 */
.custom-descriptions {
  margin-bottom: 20px;
  box-sizing: border-box;
  font-size: 14px;
  color: #606266;
}

.descriptions-title {
  margin-bottom: 16px;
  color: #303133;
  font-weight: bold;
  font-size: 16px;
}

.descriptions-table {
  width: 100%;
  border-collapse: collapse;
  table-layout: fixed;
}

.custom-descriptions.is-bordered .descriptions-table {
  border: 1px solid #ebeef5;
}

.descriptions-row {
  border-top: 1px solid #ebeef5;
}

.descriptions-item-label {
  color: #909399;
  font-weight: normal;
  background-color: #fafafa;
  padding: 12px 10px;
  text-align: right;
  width: 150px;
  border-right: 1px solid #ebeef5;
  vertical-align: middle;
}

.descriptions-item-content {
  color: #606266;
  padding: 12px 10px;
  vertical-align: middle;
}

.custom-descriptions.is-bordered .descriptions-item-label,
.custom-descriptions.is-bordered .descriptions-item-content {
  border-right: 1px solid #ebeef5;
}
</style> 