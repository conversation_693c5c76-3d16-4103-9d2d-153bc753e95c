<template>
  <div class="my-houses">
    <div class="search-form">
      <el-form :inline="true" :model="searchForm">
        <el-form-item label="地址">
          <el-input v-model="searchForm.address" placeholder="请输入房源地址"></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态">
            <el-option label="全部" value=""></el-option>
            <el-option label="未出租" value="未出租"></el-option>
            <el-option label="已出租" value="已出租"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="resetForm">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-actions">
      <el-button type="primary" icon="el-icon-plus" @click="handleAdd">添加房源</el-button>
    </div>

    <el-table
      v-loading="loading"
      :data="tableData"
      border
      style="width: 100%">
      <el-table-column type="index" label="序号" width="80"></el-table-column>
      <el-table-column prop="address" label="地址" min-width="200"></el-table-column>
      <el-table-column prop="price" label="价格" width="120">
        <template slot-scope="scope">
          ¥{{ scope.row.price }}/月
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" width="100">
        <template slot-scope="scope">
          <el-tag :type="scope.row.status === '已出租' ? 'success' : 'info'">
            {{ scope.row.status }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="userlist_Name" label="租客" width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.userlist_Name">{{ scope.row.userlist_Name }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="250" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
          <el-button size="mini" type="success" @click="handleView(scope.row)">查看</el-button>
          <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total">
      </el-pagination>
    </div>

    <!-- 房源添加/编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="50%">
      <el-form :model="houseForm" :rules="rules" ref="houseForm" label-width="100px">
        <el-form-item label="地址" prop="address">
          <el-input v-model="houseForm.address"></el-input>
        </el-form-item>
        <el-form-item label="价格" prop="price">
          <el-input-number v-model="houseForm.price" :min="0" :step="100"></el-input-number>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="houseForm.status" placeholder="请选择状态">
            <el-option label="未出租" value="未出租"></el-option>
            <el-option label="已出租" value="已出租"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="详情" prop="detail">
          <el-input type="textarea" v-model="houseForm.detail" :rows="4"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import houseApi from '../api/house.js'
import { getBackstageUser } from '../utils/auth'

export default {
  name: 'MyHouses',
  data() {
    return {
      loading: false,
      searchForm: {
        address: '',
        status: ''
      },
      tableData: [],
      allData: [],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      dialogVisible: false,
      dialogTitle: '',
      houseForm: {
        id: '',
        address: '',
        price: 0,
        status: '未出租',
        detail: '',
        userlist_Id: '',
        userlist_Name: ''
      },
      rules: {
        address: [
          { required: true, message: '请输入房源地址', trigger: 'blur' }
        ],
        price: [
          { required: true, message: '请输入房源价格', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择房源状态', trigger: 'change' }
        ]
      },
      userId: ''
    }
  },
  created() {
    const userInfo = getBackstageUser()
    if (userInfo && userInfo.userInfo) {
      this.userId = userInfo.userInfo.id
      this.fetchData()
    }
  },
  methods: {
    fetchData() {
      this.loading = true
      
      // 构造分页查询参数
      const query = {
        ownerId: this.userId,
        address: this.searchForm.address,
        status: this.searchForm.status,
        pageNum: this.pagination.currentPage,
        pageSize: this.pagination.pageSize
      }
      
      // 使用分页接口获取当前房东的房源列表
      houseApi.getOwnerHousePage(query).then(res => {
        if (res.data.flag) {
          this.tableData = res.data.data.list
          this.pagination.total = res.data.data.total
          this.pagination.currentPage = res.data.data.pageNum
        } else {
          this.$message.error('获取房源列表失败')
        }
        this.loading = false
      }).catch(() => {
        this.loading = false
        this.$message.error('获取房源列表失败')
      })
    },
    handleSearch() {
      this.pagination.currentPage = 1 // 重置为第一页
      this.fetchData()
    },
    resetForm() {
      this.searchForm = {
        address: '',
        status: ''
      }
      this.pagination.currentPage = 1 // 重置为第一页
      this.fetchData()
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.fetchData()
    },
    handleCurrentChange(val) {
      this.pagination.currentPage = val
      this.fetchData()
    },
    handleAdd() {
      this.$router.push('/backstage/house-add')
    },
    handleEdit(row) {
      this.dialogTitle = '编辑房源'
      this.houseForm = {
        id: row.houseId,
        address: row.address,
        price: row.price,
        status: row.status,
        detail: row.detail,
        userlist_Id: row.userlist_Id,
        userlist_Name: row.userlist_Name || '无'
      }
      this.dialogVisible = true
      this.$nextTick(() => {
        this.$refs.houseForm && this.$refs.houseForm.clearValidate()
      })
    },
    handleView(row) {
      this.$router.push(`/frontend/housedetail/${row.houseId}`)
    },
    handleDelete(row) {
      this.$confirm('确认删除该房源?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        houseApi.deleteHouse(row.houseId).then(res => {
          if (res.data.flag) {
            this.$message.success('删除成功')
            this.fetchData()
          } else {
            this.$message.error('删除失败')
          }
        }).catch(() => {
          this.$message.error('删除失败')
        })
      }).catch(() => {})
    },
    submitForm() {
      this.$refs.houseForm.validate(valid => {
        if (valid) {
          if (this.houseForm.id) {
            // 编辑
            const params = {
              houseId: this.houseForm.id,
              address: this.houseForm.address,
              price: this.houseForm.price,
              status: this.houseForm.status,
              detail: this.houseForm.detail,
              ownerId: this.userId
            }
            
            houseApi.updateHouse(params).then(res => {
              if (res.data.flag) {
                this.$message.success('更新成功')
                this.dialogVisible = false
                this.fetchData()
              } else {
                this.$message.error('更新失败')
              }
            }).catch(() => {
              this.$message.error('更新失败')
            })
          }
        }
      })
    }
  }
}
</script>

<style scoped>
.my-houses {
  padding: 20px;
}

.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.table-actions {
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-end;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}
</style> 