package com.house.controller;

import com.house.pojo.ChatMessage;
import com.house.service.ChatMessageService;
import com.house.common.Result;
import com.house.websocket.WebSocketChatServer;
import com.alibaba.fastjson.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.HashMap;
import java.util.Map;

/**
 * 聊天消息控制器
 */
@RestController
@RequestMapping("/chat")
public class ChatMessageController {
    
    @Autowired
    private ChatMessageService chatMessageService;
    
    /**
     * 获取聊天记录
     * @param houseId 房源ID
     * @param userId 用户ID
     * @param landlordId 房东ID
     * @return 聊天记录
     */
    @GetMapping("/history")
    public Result<List<ChatMessage>> getChatHistory(
            @RequestParam("houseId") Long houseId,
            @RequestParam("userId") Long userId,
            @RequestParam("landlordId") Long landlordId) {
        List<ChatMessage> chatHistory = chatMessageService.getChatHistory(houseId, userId, landlordId);
        return Result.success(chatHistory);
    }
    
    /**
     * 将消息标记为已读
     * @param toUserId 接收者ID
     * @param fromUserId 发送者ID
     * @return 结果
     */
    @PutMapping("/markRead")
    public Result<Boolean> markMessagesAsRead(
            @RequestParam("toUserId") Long toUserId,
            @RequestParam("fromUserId") Long fromUserId) {
        boolean result = chatMessageService.markMessagesAsRead(toUserId, fromUserId);
        return Result.success(result);
    }
    
    /**
     * 获取用户未读消息数量
     * @param userId 用户ID
     * @return 未读消息数量
     */
    @GetMapping("/unreadCount/{userId}")
    public Result<Integer> getUnreadMessageCount(@PathVariable("userId") Long userId) {
        int count = chatMessageService.getUnreadMessageCount(userId);
        return Result.success(count);
    }
    
    /**
     * 获取房东未读消息数量
     * @param landlordId 房东ID
     * @return 未读消息数量
     */
    @GetMapping("/unreadCount/landlord")
    public Result<Integer> getLandlordUnreadMessageCount(@RequestParam("landlordId") Long landlordId) {
        int count = chatMessageService.getUnreadMessageCount(landlordId);
        return Result.success(count);
    }
    
    /**
     * 获取房东的所有聊天消息
     * @param landlordId 房东ID
     * @return 聊天消息列表
     */
    @GetMapping("/landlord/messages")
    public Result<List<ChatMessage>> getLandlordMessages(@RequestParam("landlordId") Long landlordId) {
        List<ChatMessage> messages = chatMessageService.getLandlordMessages(landlordId);
        return Result.success(messages);
    }
    
    /**
     * 发送消息
     * @param message 消息对象
     * @return 结果
     */
    @PostMapping("/send")
    public Result<Boolean> sendMessage(@RequestBody ChatMessage message) {
        boolean result = chatMessageService.saveChatMessage(message);
        
        // 如果保存成功，通过WebSocket发送消息给接收者
        if (result) {
            // 构建WebSocket消息
            Map<String, Object> wsMessage = new HashMap<>();
            wsMessage.put("type", "chat");
            wsMessage.put("id", message.getId());
            wsMessage.put("houseId", message.getHouseId());
            wsMessage.put("fromId", message.getFromUserId().toString());
            wsMessage.put("toId", message.getToUserId().toString());
            wsMessage.put("content", message.getContent());
            wsMessage.put("timestamp", System.currentTimeMillis());
            
            // 通过WebSocket发送消息给接收者
            WebSocketChatServer.sendMessageToUser(message.getToUserId().toString(), JSON.toJSONString(wsMessage));
        }
        
        return Result.success(result);
    }
    
    /**
     * 删除聊天记录
     * @param houseId 房源ID
     * @param userId 用户ID
     * @param landlordId 房东ID
     * @return 结果
     */
    @DeleteMapping("/delete")
    public Result<Boolean> deleteChatHistory(
            @RequestParam("houseId") Long houseId,
            @RequestParam("userId") Long userId,
            @RequestParam("landlordId") Long landlordId) {
        boolean result = chatMessageService.deleteChatHistory(houseId, userId, landlordId);
        return Result.success(result);
    }
} 