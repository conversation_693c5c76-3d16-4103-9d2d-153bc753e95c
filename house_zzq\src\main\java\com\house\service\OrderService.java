package com.house.service;

import com.house.pojo.Order;
import com.github.pagehelper.PageInfo;

import java.util.Map;

/**
 * 订单服务接口
 */
public interface OrderService {

    /**
     * 创建订单
     * @param order 订单对象
     * @return 创建的订单
     */
    Order createOrder(Order order);

    /**
     * 根据订单号获取订单
     * @param orderNo 订单号
     * @return 订单对象
     */
    Order getOrderByOrderNo(String orderNo);

    /**
     * 根据ID获取订单
     * @param id 订单ID
     * @return 订单对象
     */
    Order getOrderById(Integer id);

    /**
     * 更新订单状态
     * @param orderNo 订单号
     * @param status 状态
     * @return 更新是否成功
     */
    boolean updateOrderStatus(String orderNo, String status);

    /**
     * 取消订单
     * @param orderNo 订单号
     * @return 取消是否成功
     */
    boolean cancelOrder(String orderNo);

    /**
     * 支付押金
     * @param orderNo 订单号
     * @param paymentMethod 支付方式
     * @return 支付是否成功
     */
    boolean payDeposit(String orderNo, String paymentMethod);

    /**
     * 支付租金
     * @param orderNo 订单号
     * @param paymentMethod 支付方式
     * @return 支付是否成功
     */
    boolean payRent(String orderNo, String paymentMethod);

    /**
     * 确认入住
     * @param orderNo 订单号
     * @return 确认是否成功
     */
    boolean confirmCheckIn(String orderNo);

    /**
     * 完成租约
     * @param orderNo 订单号
     * @return 完成是否成功
     */
    boolean completeOrder(String orderNo);

    /**
     * 分页查询订单列表
     * @param params 查询参数，包括：
     *               - userId: 用户ID（可选）
     *               - tenantId: 租客ID（可选）
     *               - ownerId: 房东ID（可选）
     *               - houseId: 房源ID（可选）
     *               - status: 订单状态（可选）
     *               - keyword: 关键字（可选）
     *               - page: 页码（默认1）
     *               - limit: 每页数量（默认10）
     * @return 分页订单列表
     */
    PageInfo<Order> getOrderList(Map<String, Object> params);

    /**
     * 查询用户的订单统计
     * @param userId 用户ID
     * @return 订单统计信息，包括各状态订单数量
     */
    Map<String, Object> getOrderStatsByUserId(Integer userId);
} 