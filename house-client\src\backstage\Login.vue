<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <h2>房屋租赁管理系统</h2>
        <p>管理员/房东登录</p>
      </div>
      
      <el-form ref="loginForm" :model="loginForm" :rules="loginRules" class="login-form" label-position="top">
        <el-form-item prop="username" label="用户名">
          <el-input 
            v-model="loginForm.username" 
            prefix-icon="el-icon-user" 
            placeholder="请输入用户名"
            @keyup.enter.native="handleLogin">
          </el-input>
        </el-form-item>
        
        <el-form-item prop="password" label="密码">
          <el-input 
            v-model="loginForm.password" 
            prefix-icon="el-icon-lock" 
            placeholder="请输入密码" 
            show-password
            @keyup.enter.native="handleLogin">
          </el-input>
        </el-form-item>
        
        <el-form-item>
          <div class="remember-forgot">
            <el-checkbox v-model="loginForm.remember">记住我</el-checkbox>
            <a href="javascript:;" @click="forgotPassword" class="forgot-link">忘记密码?</a>
          </div>
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            :loading="loading" 
            class="login-button" 
            @click="handleLogin">
            登录
          </el-button>
        </el-form-item>
        
        <div class="register-link">
          <span>还没有房东账号?</span>
          <router-link to="/backstage/register">立即注册</router-link>
        </div>
      </el-form>
      
      <div class="login-footer">
        <p>Copyright © 2023 房屋租赁管理系统</p>
      </div>
    </div>
  </div>
</template>

<script>
import { setUser, getBackstageUser, isLoggedIn, removeBackstageUser, isAdmin, isOwner } from '@/utils/auth'

export default {
  name: 'BackstageLogin',
  data() {
    return {
      loginForm: {
        username: '', // 在提交时会作为account发送
        password: '',
        remember: false
      },
      loginRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 4, max: 16, message: '长度在 4 到 16 个字符', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 18, message: '长度在 6 到 18 个字符', trigger: 'blur' }
        ]
      },
      loading: false,
      redirect: undefined
    }
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect
      },
      immediate: true
    }
  },
  created() {
    // 检查是否已经登录，并且有有效的权限
    if (isLoggedIn(false)) {
      // 进一步检查用户是否有后台权限
      if (isAdmin(false) || isOwner(false)) {
        this.redirectToDashboard()
      } else {
        // 如果没有后台权限，清除登录状态
        console.log('用户没有后台权限，清除登录状态')
        removeBackstageUser()
      }
    }

    // 从本地存储中获取用户名
    const rememberedUser = localStorage.getItem('rememberedUser')
    if (rememberedUser) {
      try {
        const userData = JSON.parse(rememberedUser)
        this.loginForm.username = userData.username
        this.loginForm.remember = true
      } catch (e) {
        console.error('解析记住的用户信息失败', e)
      }
    }
  },
  methods: {
    // 处理登录
    handleLogin() {
      this.$refs.loginForm.validate(async valid => {
        if (valid) {
          this.loading = true
          try {
            // 调用后台登录API
            const response = await this.$http.post('/user/login', {
              username: this.loginForm.username,
              password: this.loginForm.password,
              system: 'backstage'
            })
            
            if (response.data && response.data.flag) {
              // 构造用户信息对象，从data字段中获取token和userInfo
              const userInfo = response.data.data.userInfo;
              const roles = response.data.data.roles;

              // 根据用户类型设置systemRole
              let systemRole = 'user'; // 默认为普通用户
              if (userInfo.type === 1) {
                systemRole = 'admin'; // 管理员
              } else if (userInfo.type === 3) {
                systemRole = 'owner'; // 房东
              }

              const loginResult = {
                flag: response.data.flag,
                token: response.data.data.token,
                userInfo: userInfo,
                roles: roles,
                systemRole: systemRole,
                tokenTimestamp: Date.now() // 添加token时间戳
              }

              // 保存token和用户信息
              setUser(loginResult, false) // 第二个参数false表示这是后台用户

              // 如果选择了记住我，保存用户名到本地存储
              if (this.loginForm.remember) {
                localStorage.setItem('rememberedUser', JSON.stringify({
                  username: this.loginForm.username
                }))
              } else {
                localStorage.removeItem('rememberedUser')
              }

              this.$message({
                message: '登录成功',
                type: 'success'
              })

              // 初始化WebSocket连接
              this.initBackstageWebSocket(loginResult.userInfo.id)

              // 重定向到目标页面或默认页面
              this.redirectToDashboard()
            } else {
              this.$message.error(response.data.message || '登录失败，请检查用户名和密码')
            }
          } catch (error) {
            console.error('登录请求失败', error)
            this.$message.error('登录失败，请稍后再试')
          } finally {
            this.loading = false
          }
        } else {
          return false
        }
      })
    },
    
    // 重定向到仪表盘
    redirectToDashboard() {
      const path = this.redirect || '/backstage/dashboard'
      this.$router.replace(path)
    },
    
    // 忘记密码
    forgotPassword() {
      this.$message.info('请联系系统管理员重置密码')
    },

    // 初始化WebSocket连接
    initBackstageWebSocket(userId) {
      // 初始化WebSocket连接，用于消息通知
      if ('WebSocket' in window) {
        // 获取token
        const userInfo = getBackstageUser();
        const token = userInfo ? userInfo.token : '';
        
        // 如果token为空，不初始化WebSocket
        if (!token) {
          console.error('无法初始化WebSocket：token为空');
          this.$message.error('登录信息已失效，请重新登录');
          return;
        }
        
        // 使用与前台相同的连接方式
        // 使用相对路径，而不是硬编码的localhost
        const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const host = window.location.host;
        const encodedToken = encodeURIComponent(token);
        
        // 使用前台相同的WebSocket连接方式
        const wsUrl = `${wsProtocol}//${host}/ws/chat/${userId}?houseId=0&landlordId=${userId}&token=${encodedToken}`;
        
        console.log('初始化后台WebSocket连接:', wsUrl);
        
        // 将WebSocket实例存储在全局Vue实例中，方便其他组件访问
        this.$root.$data.backstageWebSocket = new WebSocket(wsUrl);
        
        // 连接成功回调
        this.$root.$data.backstageWebSocket.onopen = () => {
          console.log('后台WebSocket连接已建立');
        };
        
        // 接收消息回调
        this.$root.$data.backstageWebSocket.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data);
            console.log('后台收到消息:', message);
            
            // 触发全局事件，通知消息组件更新
            this.$root.$emit('backstage-new-message', message);
            
            // 如果是聊天消息，显示通知
            if (message.type === 'chat') {
              this.$notify({
                title: '新聊天消息',
                message: `来自用户的咨询: ${message.content}`,
                type: 'info',
                duration: 5000
              });
            }
          } catch (error) {
            console.error('解析WebSocket消息失败:', error);
          }
        };
        
        // 连接关闭回调
        this.$root.$data.backstageWebSocket.onclose = () => {
          console.log('后台WebSocket连接已关闭');
        };
        
        // 连接错误回调
        this.$root.$data.backstageWebSocket.onerror = (error) => {
          console.error('后台WebSocket连接错误:', error);
        };
      }
    }
  }
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-box {
  width: 400px;
  padding: 40px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2 {
  font-size: 24px;
  color: #333;
  margin-bottom: 10px;
}

.login-header p {
  font-size: 14px;
  color: #666;
}

.login-form {
  margin-bottom: 20px;
}

.remember-forgot {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.forgot-link {
  font-size: 14px;
  color: #409EFF;
  text-decoration: none;
}

.login-button {
  width: 100%;
  padding: 12px 0;
}

.register-link {
  text-align: center;
  margin-top: 15px;
  font-size: 14px;
  color: #666;
}

.register-link a {
  color: #409EFF;
  text-decoration: none;
  margin-left: 5px;
}

.login-footer {
  text-align: center;
  margin-top: 30px;
  font-size: 12px;
  color: #999;
}
</style> 