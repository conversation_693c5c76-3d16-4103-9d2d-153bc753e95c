package com.house.service;

/**
 * 消息通知服务接口
 */
public interface NotificationService {
    
    /**
     * 向指定用户发送通知
     * @param userId 用户ID
     * @param title 通知标题
     * @param content 通知内容
     * @param type 通知类型
     * @return 消息ID
     */
    Long sendNotification(String userId, String title, String content, String type);
    
    /**
     * 向指定用户发送带关联信息的通知
     * @param userId 用户ID
     * @param title 通知标题
     * @param content 通知内容
     * @param type 通知类型
     * @param linkId 关联ID
     * @param linkType 关联类型
     * @return 消息ID
     */
    Long sendNotification(String userId, String title, String content, String type, String linkId, String linkType);
    
    /**
     * 向所有用户发送通知
     * @param title 通知标题
     * @param content 通知内容
     * @param type 通知类型
     * @return 是否发送成功
     */
    boolean sendNotificationToAll(String title, String content, String type);
    
    /**
     * 发送订单通知
     * @param userId 用户ID
     * @param title 通知标题
     * @param content 通知内容
     * @param orderId 订单ID
     * @return 消息ID
     */
    Long sendOrderNotification(String userId, String title, String content, String orderId);
    
    /**
     * 发送合同通知
     * @param userId 用户ID
     * @param title 通知标题
     * @param content 通知内容
     * @param contractId 合同ID
     * @return 消息ID
     */
    Long sendContractNotification(String userId, String title, String content, String contractId);
    
    /**
     * 发送支付通知
     * @param userId 用户ID
     * @param title 通知标题
     * @param content 通知内容
     * @param paymentId 支付ID
     * @return 消息ID
     */
    Long sendPaymentNotification(String userId, String title, String content, String paymentId);
} 