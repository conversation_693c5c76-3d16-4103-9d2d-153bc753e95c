<template>
  <div class="contract-page content-scroll">
    <Header />
    <div class="container">
      <div class="contract-card">
        <h2>租房合同</h2>
        <el-divider></el-divider>
        
        <div class="contract-info" v-if="contractData">
          <div class="header-info">
            <h3>合同编号: {{ contractData.contractNo }}</h3>
            <el-tag :type="statusType">{{ statusText }}</el-tag>
          </div>
          
          <el-tabs type="border-card" class="contract-tabs">
            <el-tab-pane label="合同内容">
              <div class="contract-content" v-loading="loading">
                <!-- 使用共享合同组件 -->
                <contract-detail
                  :contract="contractData"
                  :isOwner="isOwner"
                  :isTenant="isTenant"
                  :canSign="true"
                  :userInfo="userInfo"
                  @sign="handleSignContract"
                ></contract-detail>
              </div>
            </el-tab-pane>
            
            <el-tab-pane label="合同状态">
              <div class="contract-status">
                <el-steps direction="vertical" :active="contractStepActive">
                  <el-step title="合同生成" :description="'时间：' + formatDateTime(contractData.createTime)"></el-step>
                  <el-step title="租客签署" :description="getStepDescription('tenant')"></el-step>
                  <el-step title="房东签署" :description="getStepDescription('owner')"></el-step>
                  <el-step title="合同生效" :description="getContractEffectiveDesc()"></el-step>
                </el-steps>
                
                <div class="contract-status-tips" v-if="contractData.status === CONTRACT_STATUS.PENDING">
                  <el-alert
                    title="温馨提示"
                    type="info"
                    description="合同需要双方签署后才能生效，请尽快完成签署。"
                    show-icon>
                  </el-alert>
                </div>

                <div class="contract-actions" v-if="contractData.status === CONTRACT_STATUS.ACTIVE">
                  <el-button type="primary" @click="downloadContract">下载合同</el-button>
                  <el-button @click="printContract">打印合同</el-button>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
        
        <div v-else class="loading-skeleton">
          <el-skeleton :rows="10" animated />
        </div>
        
        <!-- 底部导航 -->
        <div class="contract-footer">
          <el-button @click="goBack">返回</el-button>
          <el-button
            type="primary"
            @click="goToCheckIn"
            v-if="contractData && contractData.status === CONTRACT_STATUS.ACTIVE && isTenant && orderData && orderData.status === 'pending'">
            前往入住确认
          </el-button>
          <el-button @click="goToOrderList">查看订单列表</el-button>
        </div>
      </div>
    </div>
    <Footer />
  </div>
</template>

<script>
import Header from './components/Header'
import Footer from './components/Footer'
import { getUserInfo } from '../utils/auth'
import { ContractStatusUtils, CONTRACT_STATUS } from '../constants/contractStatus'
import ContractDetail from '../components/ContractDetail'
import moment from 'moment'
import http from '../utils/request'

export default {
  name: 'Contract',
  components: {
    Header,
    Footer,
    ContractDetail
  },
  data() {
    return {
      orderNo: '',
      order: null,
      orderData: null,
      contractData: null,
      house: null,
      ownerInfo: {},
      tenantInfo: {},
      loading: false,
      signingOwner: false,
      signingTenant: false,
      userInfo: null,
      $http: http, // 添加HTTP服务实例
      CONTRACT_STATUS // 添加合同状态常量供模板使用
    }
  },
  computed: {
    // 合同状态类型
    statusType() {
      return this.contractData ? ContractStatusUtils.getStatusType(this.contractData.status) : 'info';
    },

    // 合同状态文本
    statusText() {
      return this.contractData ? ContractStatusUtils.getStatusText(this.contractData.status) : '';
    },
    
    // 合同步骤状态
    contractStepActive() {
      if (!this.contractData) return 0;
      
      if (this.contractData.status === CONTRACT_STATUS.ACTIVE) {
        return 4;
      } else if (this.contractData.ownerSigned && this.contractData.tenantSigned) {
        return 3;
      } else if (this.contractData.ownerSigned || this.contractData.tenantSigned) {
        return 2;
      } else {
        return 1;
      }
    },
    
    // 房源信息
    houseInfo() {
      return this.house || {};
    },
    
    // 判断当前用户是否为租客
    isTenant() {
      if (!this.userInfo || !this.contractData) return false;
      
      // 获取用户ID - 处理不同的用户对象结构
      const userId = this.getUserId();
      return userId === this.contractData.tenantId;
    },
    
    // 判断当前用户是否为房东
    isOwner() {
      if (!this.userInfo || !this.contractData) return false;
      
      // 获取用户ID - 处理不同的用户对象结构
      const userId = this.getUserId();
      return userId === this.contractData.ownerId;
    }
  },
  created() {
    this.orderNo = this.$route.params.orderNo;
    console.log('合同组件创建，获取订单号:', this.orderNo);
    
    if (!this.orderNo) {
      this.$message.error('订单号不存在');
      this.$router.push('/frontend/orderlist');
      return;
    }
    
    // 尝试从localStorage获取用户信息
    const storedUser = localStorage.getItem('currentUser');
    if (storedUser) {
      try {
        this.userInfo = JSON.parse(storedUser);
        console.log('从localStorage获取到用户信息:', this.userInfo);
      } catch (e) {
        console.error('解析用户信息失败:', e);
      }
    }
    
    // 如果localStorage中没有，则调用getUserInfo()
    if (!this.userInfo) {
      this.userInfo = getUserInfo();
      console.log('从getUserInfo获取用户信息:', this.userInfo);
    }
    
    if (!this.userInfo) {
      this.$message.warning('请先登录');
      this.$router.push('/frontend/login?redirect=/frontend/contract/' + this.orderNo);
      return;
    }
    
    // 获取订单和合同信息
    this.fetchOrderAndContract();
  },
  methods: {
    // 获取订单和合同信息
    async fetchOrderAndContract() {
      this.loading = true;
      
      try {
        console.log('获取订单信息，订单号:', this.orderNo);
        // 使用自定义API调用获取订单
        const res = await this.$http.get(`/orders/${this.orderNo}`);
        console.log('订单API响应:', res);
        
        if (res.data && res.data.flag) {
          this.order = res.data.data;
          console.log('获取到订单数据:', this.order);
          
          // 检查用户权限
          const userId = this.userInfo.id || (this.userInfo.userInfo && this.userInfo.userInfo.id);
          if (userId !== this.order.tenantId && userId !== this.order.ownerId) {
            this.$message.warning('您无权查看此合同');
            this.$router.push('/frontend/orderlist');
            return;
          }
          
          // 获取房源信息
          await this.fetchHouseDetail(this.order.houseId);
          
          // 获取合同数据
          await this.fetchContractData();
        } else {
          const errorMsg = res.data && res.data.message ? res.data.message : '获取订单信息失败';
          this.$message.error(errorMsg);
          console.error('获取订单失败:', errorMsg);
        }
      } catch (error) {
        console.error('获取订单信息失败:', error);
        const errorMsg = error.response && error.response.data && error.response.data.message 
          ? error.response.data.message 
          : error.message || '未知错误';
        this.$message.error('获取订单信息失败: ' + errorMsg);
      } finally {
        this.loading = false;
      }
    },
    
    // 获取房源详情
    async fetchHouseDetail(houseId) {
      try {
        console.log('获取房源详情，房源ID:', houseId);
        
        if (!houseId) {
          console.error('房源ID为空，无法获取房源详情');
          return;
        }
        
        // 使用http服务实例调用API
        const res = await this.$http.get(`/houses/${houseId}`);
        console.log('房源API响应:', res);
        
        if (res.data && res.data.flag) {
          this.house = res.data.data;
          console.log('获取到房源数据:', this.house);
        } else {
          const errorMsg = res.data && res.data.message ? res.data.message : '获取房源信息失败';
          console.error('获取房源详情失败:', errorMsg);
        }
      } catch (error) {
        console.error('获取房源信息失败:', error);
        const errorMsg = error.response && error.response.data && error.response.data.message 
          ? error.response.data.message 
          : error.message || '未知错误';
        this.$message.error('获取房源信息失败: ' + errorMsg);
      }
    },
    
    // 获取合同数据
    async fetchContractData() {
      try {
        console.log('获取合同数据, 订单号:', this.orderNo);
        // 使用正确的API路径获取合同信息
        const res = await this.$http.get(`/contracts/order/${this.orderNo}`);
        
        console.log('合同API响应:', res);
        
        if (res.data && res.data.flag) {
          this.contractData = res.data.data;
          console.log('获取到合同数据:', this.contractData);
          
          // 设置签署状态
          this.contractData.tenantSigned = !!this.contractData.tenantSignTime;
          this.contractData.ownerSigned = !!this.contractData.ownerSignTime;

          // 关联订单和房源信息到合同数据
          this.contractData.house = this.house;
          this.contractData.order = this.order;
          this.contractData.monthlyPrice = this.order.monthlyPrice;
          this.contractData.deposit = this.order.deposit;
          this.contractData.duration = this.order.duration;
          this.contractData.startDate = this.order.startDate;
          this.contractData.endDate = this.order.endDate;
          
          // 不再请求用户信息接口，直接使用已有数据
          // 房东信息从订单数据中获取
          if (this.contractData.ownerId) {
            this.contractData.ownerName = '房东'; // 使用默认名称
          }

          // 租客信息从订单数据中获取
          if (this.contractData.tenantId) {
            this.contractData.tenantName = '租客'; // 使用默认名称
          }

          // 确保签名中有用户名
          if (this.contractData.ownerSignTime && !this.contractData.ownerSignature) {
            this.contractData.ownerSignature = this.ownerInfo.name || this.userInfo.username || (this.userInfo.userInfo && this.userInfo.userInfo.username);
          }
          if (this.contractData.tenantSignTime && !this.contractData.tenantSignature) {
            this.contractData.tenantSignature = this.tenantInfo.name || this.userInfo.username || (this.userInfo.userInfo && this.userInfo.userInfo.username);
          }
        } else {
          // 如果没有找到合同，可能是因为合同尚未生成
          this.$message.warning('未找到相关合同信息，可能需要先生成合同');
        }
      } catch (error) {
        console.error('获取合同信息失败:', error);
        this.$message.error('获取合同信息失败: ' + (error.response ? error.response.data.message : error.message));
      }
    },
    
    // 处理签署合同
    handleSignContract({ role, contract }) {
      if (role === 'owner') {
        this.signContract('owner');
      } else {
        this.signContract('tenant');
      }
    },
    
    // 签署合同
    async signContract(role) {
      try {
        await this.$confirm('确定要签署该合同吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'success'
        });
        
        if (role === 'owner') {
          this.signingOwner = true;
        } else {
          this.signingTenant = true;
        }
        
        // 调用正确的API路径
        let res;
        const userId = this.getUserId();
        console.log('签署合同，用户ID:', userId, '角色:', role, '合同ID:', this.contractData.id);
        
        // 使用统一的签署接口
        const signData = {};
        if (role === 'owner') {
          signData.ownerSignTime = new Date();
          signData.ownerSignature = this.userInfo.username || (this.userInfo.userInfo && this.userInfo.userInfo.username);
        } else {
          signData.tenantSignTime = new Date();
          signData.tenantSignature = this.userInfo.username || (this.userInfo.userInfo && this.userInfo.userInfo.username);
        }
        
        res = await this.$http.put(`/contracts/${this.contractData.contractNo}/sign`, signData);
        
        console.log('签署合同响应:', res);
        
        if (res.data && res.data.flag) {
          this.$message.success('合同签署成功');

          // 后端已经处理了所有逻辑，包括状态更新和订单状态更新
          // 直接刷新页面数据获取最新状态
          this.fetchOrderAndContract();
        } else {
          this.$message.error(res.data && res.data.message ? res.data.message : '签署失败');
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('签署合同失败:', error);
          this.$message.error('签署合同失败: ' + (error.response ? error.response.data.message : error.message));
        }
      } finally {
        this.signingOwner = false;
        this.signingTenant = false;
      }
    },
    
    // 更新合同状态
    updateContractStatus() {
      if (this.contractData.ownerSigned && this.contractData.tenantSigned) {
        this.contractData.status = CONTRACT_STATUS.ACTIVE
      }
    },
    
    // 格式化日期
    formatDate(dateStr) {
      return moment(dateStr).format('YYYY年MM月DD日')
    },
    
    // 格式化日期时间
    formatDateTime(dateStr) {
      return dateStr ? moment(dateStr).format('YYYY-MM-DD HH:mm:ss') : '未签署'
    },
    
    // 获取步骤描述
    getStepDescription(role) {
      if (role === 'tenant') {
        return this.contractData.tenantSigned 
          ? '签署时间：' + this.formatDateTime(this.contractData.tenantSignTime)
          : '等待签署'
      } else if (role === 'owner') {
        return this.contractData.ownerSigned
          ? '签署时间：' + this.formatDateTime(this.contractData.ownerSignTime)
          : '等待签署'
      }
      return ''
    },
    
    // 获取合同生效描述
    getContractEffectiveDesc() {
      if (this.contractData.status === CONTRACT_STATUS.ACTIVE) {
        // 取两个签署时间中较晚的一个作为生效时间
        const effectiveTime = this.contractData.ownerSignTime && this.contractData.tenantSignTime
          ? (new Date(this.contractData.ownerSignTime) > new Date(this.contractData.tenantSignTime)
              ? this.contractData.ownerSignTime
              : this.contractData.tenantSignTime)
          : null
        return '生效时间：' + this.formatDateTime(effectiveTime)
      }
      return '等待双方签署'
    },
    
    // 下载合同
    downloadContract() {
      this.$message.info('合同下载功能开发中')
      // 实际项目中应调用后端API下载合同PDF
    },
    
    // 打印合同
    printContract() {
      window.print()
    },
    
    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },
    
    // 前往入住确认
    goToCheckIn() {
      this.$router.push('/frontend/checkin/' + this.orderNo)
    },
    
    // 查看订单列表
    goToOrderList() {
      this.$router.push('/frontend/orderlist')
    },

    // 获取当前用户ID的辅助方法
    getUserId() {
      // 处理不同格式的用户对象
      if (this.userInfo.id) {
        return this.userInfo.id;
      } else if (this.userInfo.userInfo && this.userInfo.userInfo.id) {
        return this.userInfo.userInfo.id;
      }
      return null;
    }
  }
}
</script>

<style scoped>
.contract-page {
  background-color: #f8f8f8;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.container {
  flex: 1;
  max-width: 1200px;
  margin: 30px auto;
  padding: 0 20px;
}

.contract-card {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  padding: 30px;
}

h2 {
  font-size: 24px;
  color: #333;
  margin-bottom: 20px;
}

.header-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header-info h3 {
  margin: 0;
  font-size: 18px;
}

.contract-tabs {
  margin-top: 30px;
}

.contract-content {
  padding: 20px;
  background: #fff;
}

.contract-status {
  padding: 30px;
}

.contract-status-tips {
  margin-top: 30px;
}

.contract-actions {
  margin-top: 30px;
  text-align: center;
}

.contract-footer {
  margin-top: 30px;
  text-align: center;
}

.loading-skeleton {
  padding: 20px 0;
}

@media print {
  .contract-page {
    background-color: #fff;
  }
  
  .container {
    margin: 0;
    padding: 0;
    max-width: 100%;
  }
  
  .contract-card {
    box-shadow: none;
    padding: 0;
  }
  
  h2, .el-divider, .header-info, .el-tabs__header, .contract-footer, .contract-status-tips, .contract-actions {
    display: none !important;
  }
  
  .el-tabs__content {
    padding: 0 !important;
  }
}
</style>