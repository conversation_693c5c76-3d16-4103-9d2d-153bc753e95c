package com.house.mapper;

import com.house.dto.HouseQueryDTO;
import com.house.pojo.House;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 房源Mapper接口
 */
@Mapper
public interface HouseMapper {
    
    /**
     * 获取所有房源列表
     *
     * @return 房源列表
     */
    List<House> getAllHouseList();
    
    /**
     * 获取热门房源列表（按点击量排序）
     *
     * @param limit 获取数量
     * @return 热门房源列表
     */
    List<House> getHotHouseList(@Param("limit") int limit);
    
    /**
     * 根据条件查询房源
     *
     * @param queryDTO 查询条件
     * @return 房源列表
     */
    List<House> getHouseListByCondition(HouseQueryDTO queryDTO);
    
    /**
     * 获取房源详情
     *
     * @param houseId 房源ID
     * @return 房源详情
     */
    House getHouseDetail(@Param("houseId") Integer houseId);
    
    /**
     * 获取用户发布的房源
     *
     * @param userId 用户ID
     * @return 房源列表
     */
    List<House> getHouseListByUserId(@Param("userId") Integer userId);
    
    /**
     * 增加房源点击量
     *
     * @param houseId 房源ID
     * @return 影响行数
     */
    int increaseViewCount(@Param("houseId") Integer houseId);
    
    /**
     * 添加房源
     *
     * @param house 房源信息
     * @return 影响行数
     */
    int addHouse(House house);
    
    /**
     * 更新房源信息
     *
     * @param house 房源信息
     * @return 影响行数
     */
    int updateHouse(House house);
    
    /**
     * 删除房源
     *
     * @param houseId 房源ID
     * @return 影响行数
     */
    int deleteHouse(@Param("houseId") Integer houseId);
} 