import request from '../../utils/request'

const api_name = 'reviews'

export default {
  // 提交评价
  submitReview(data) {
    return request({
      url: `/${api_name}`,
      method: 'post',
      data
    })
  },

  // 获取房屋所有评价
  getHouseReviews(houseId) {
    return request({
      url: `/${api_name}/house/${houseId}`,
      method: 'get'
    })
  },

  // 获取房屋评价统计信息
  getHouseReviewStats(houseId) {
    return request({
      url: `/${api_name}/stats/${houseId}`,
      method: 'get'
    })
  },

  // 检查订单是否已评价
  checkOrderReviewed(orderId) {
    return request({
      url: `/${api_name}/check/${orderId}`,
      method: 'get'
    })
  },

  // 房东回复评价
  replyReview(reviewId, reply) {
    const formData = new FormData()
    formData.append('reply', reply)
    
    return request({
      url: `/${api_name}/${reviewId}/reply`,
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    })
  },

  // 删除评价回复
  deleteReply(reviewId) {
    return request({
      url: `/${api_name}/${reviewId}/reply`,
      method: 'delete'
    })
  },

  // 获取评价列表（后台用）
  getReviews(params) {
    return request({
      url: `/${api_name}`,
      method: 'get',
      params
    })
  },

  // 获取房东的所有评价
  getOwnerReviews(ownerId) {
    return request({
      url: `/${api_name}/owner/${ownerId}`,
      method: 'get'
    })
  },

  // 获取用户发布的所有评价
  getUserReviews(userId) {
    return request({
      url: `/${api_name}/user/${userId}`,
      method: 'get'
    })
  }
}
