-- 创建评价表
CREATE TABLE IF NOT EXISTS `tb_review` (
  `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT '评价ID',
  `order_id` INT(11) NOT NULL COMMENT '订单ID',
  `order_no` VARCHAR(64) NOT NULL COMMENT '订单编号',
  `house_id` INT(11) NOT NULL COMMENT '房屋ID',
  `owner_id` INT(11) NOT NULL COMMENT '房东ID',
  `user_id` INT(11) NOT NULL COMMENT '评价用户ID',
  `user_name` VARCHAR(64) DEFAULT NULL COMMENT '评价用户名',
  `user_avatar` VARCHAR(255) DEFAULT NULL COMMENT '用户头像',
  `rating` DECIMAL(2,1) NOT NULL DEFAULT 5.0 COMMENT '总体评分（1-5分）',
  `location_rating` DECIMAL(2,1) NOT NULL DEFAULT 5.0 COMMENT '位置便利性评分',
  `facility_rating` DECIMAL(2,1) NOT NULL DEFAULT 5.0 COMMENT '房屋设施评分',
  `service_rating` DECIMAL(2,1) NOT NULL DEFAULT 5.0 COMMENT '房东服务评分',
  `value_rating` DECIMAL(2,1) NOT NULL DEFAULT 5.0 COMMENT '性价比评分',
  `environment_rating` DECIMAL(2,1) NOT NULL DEFAULT 5.0 COMMENT '周边环境评分',
  `cleanliness_rating` DECIMAL(2,1) NOT NULL DEFAULT 5.0 COMMENT '卫生情况评分',
  `average_rating` DECIMAL(2,1) NOT NULL DEFAULT 5.0 COMMENT '平均评分',
  `content` TEXT NOT NULL COMMENT '评价内容',
  `tags` VARCHAR(255) DEFAULT NULL COMMENT '评价标签，逗号分隔',
  `images` TEXT DEFAULT NULL COMMENT '评价图片，逗号分隔的URL',
  `anonymous` TINYINT(1) DEFAULT 0 COMMENT '是否匿名评价',
  `reply` TEXT DEFAULT NULL COMMENT '房东回复',
  `reply_time` DATETIME DEFAULT NULL COMMENT '回复时间',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_house_id` (`house_id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_owner_id` (`owner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='房屋评价表';

-- 创建评价统计表（用于快速查询）
CREATE TABLE IF NOT EXISTS `tb_review_stats` (
  `house_id` INT(11) NOT NULL COMMENT '房屋ID',
  `total_reviews` INT(11) DEFAULT 0 COMMENT '评价总数',
  `average_rating` DECIMAL(2,1) DEFAULT 5.0 COMMENT '平均总体评分',
  `location_rating` DECIMAL(2,1) DEFAULT 5.0 COMMENT '平均位置便利性评分',
  `facility_rating` DECIMAL(2,1) DEFAULT 5.0 COMMENT '平均房屋设施评分',
  `service_rating` DECIMAL(2,1) DEFAULT 5.0 COMMENT '平均房东服务评分',
  `value_rating` DECIMAL(2,1) DEFAULT 5.0 COMMENT '平均性价比评分',
  `environment_rating` DECIMAL(2,1) DEFAULT 5.0 COMMENT '平均周边环境评分',
  `cleanliness_rating` DECIMAL(2,1) DEFAULT 5.0 COMMENT '平均卫生情况评分',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`house_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='房屋评价统计表';

-- 创建评价标签统计表
CREATE TABLE IF NOT EXISTS `tb_review_tag_stats` (
  `id` INT(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `house_id` INT(11) NOT NULL COMMENT '房屋ID',
  `tag_name` VARCHAR(64) NOT NULL COMMENT '标签名称',
  `count` INT(11) DEFAULT 1 COMMENT '使用次数',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_house_tag` (`house_id`, `tag_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='房屋评价标签统计表'; 