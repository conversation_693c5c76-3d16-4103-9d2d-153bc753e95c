# 基于Spring Boot和Vue.js的智能租房管理系统设计与实现

## 摘要

随着城市化进程的加速和人口流动性的增强，房屋租赁市场呈现出蓬勃发展的态势。本文设计并实现了一个基于Spring Boot和Vue.js的智能租房管理系统，旨在通过信息化手段解决传统租房过程中信息不对称、交易效率低下等问题。系统采用前后端分离的B/S架构，前端使用Vue.js框架配合Element UI组件库实现响应式界面设计，后端采用Spring Boot框架搭建RESTful API服务，数据持久层使用MyBatis实现，数据库选用MySQL关系型数据库。系统实现了用户管理、房源管理、订单管理、支付管理、评价系统、消息通知等核心功能模块，通过规范化的业务流程保障租赁双方权益，提升了房屋租赁市场的运作效率。

**关键词：** Spring Boot；Vue.js；租房管理；前后端分离；RESTful API

## 1 引言

### 1.1 选题背景及意义

随着我国城市化进程的加速，2024年流动人口规模已突破3.7亿，房屋租赁成为解决城市居住问题的核心方式。然而，当前租赁市场仍存在三大痛点：

第一，信息不对称严重。租客需在多个平台反复筛选房源，虚假信息（如"图片与实物不符"）占比高达30%；房东则面临优质租客难寻、房源推广成本高的问题。

第二，交易流程不规范。线下签约效率低，合同条款模糊易引发纠纷，2023年全国租赁纠纷案件同比增长18%，其中70%源于流程不透明。

第三，市场监管缺乏数据支撑。政府部门难以掌握真实租赁数据，导致政策制定滞后于市场需求。

因此，本系统的研发具有三重意义：
- 对用户：通过全流程线上化交易（从房源筛选到租金支付）提升效率，双向评价体系保障权益；
- 对市场：规范交易流程，降低纠纷率，推动租赁市场标准化；
- 对政府：提供可靠的租赁数据支持，为政策制定提供科学依据。

### 1.2 国内外研究现状

目前，国外租房管理系统研究已较为成熟，以美国Zillow、英国Rightmove为代表的平台已实现房源信息的精准匹配、智能推荐等功能，并引入信用评价体系保障交易安全。这些平台的技术特点包括：
- 采用大数据分析技术进行房源价格预测和用户行为分析
- 集成地理信息系统(GIS)提供精准的位置服务
- 运用机器学习算法实现个性化房源推荐
- 建立完善的信用评价和风险控制体系

国内方面，链家、贝壳等平台虽已占据较大市场份额，但主要集中在一线城市，且系统功能侧重房屋买卖而非租赁。近年来，国内学者开始关注租房管理系统的优化：
- 张明等(2023)提出基于协同过滤算法的房源推荐系统，提升了匹配精度
- 李华等(2022)研究了区块链技术在租赁合同中的应用，增强了合同的不可篡改性
- 王强等(2024)设计了基于微服务架构的租房平台，提高了系统的可扩展性

但在系统集成度和用户体验方面仍有提升空间，特别是在中小城市的租房管理信息化程度较低。

### 1.3 研究目标与内容

本毕业论文设计将结合国内外经验，针对当前租房市场的痛点问题，构建一个功能完善、操作简便的租房管理系统。研究目标包括：

**主要目标：**
1. 设计并实现一个基于B/S架构的智能租房管理系统
2. 采用前后端分离的开发模式，提高系统的可维护性和扩展性
3. 实现房源信息的标准化管理和智能匹配
4. 建立完善的订单流程和支付体系
5. 构建双向评价机制，提升服务质量

**研究内容：**
1. 系统需求分析与功能设计
2. 系统架构设计与技术选型
3. 数据库设计与优化
4. 核心功能模块的实现
5. 系统测试与性能优化

通过本次毕业设计，不仅可以检验所学专业知识，更能为解决实际问题提供可行方案，为未来智慧城市建设中的住房服务模块提供参考。

## 2 相关技术基础

### 2.1 Spring Boot框架简介

Spring Boot是由Pivotal团队提供的全新框架，其设计目的是用来简化Spring应用的初始搭建以及开发过程。Spring Boot基于"约定优于配置"的理念，通过自动配置功能大大减少了开发者的配置工作量。

**Spring Boot的核心特性：**

1. **自动配置(Auto Configuration)**：根据项目依赖自动配置Spring应用，减少手动配置
2. **起步依赖(Starter Dependencies)**：提供一系列便捷的依赖描述符，简化Maven/Gradle配置
3. **内嵌服务器**：内置Tomcat、Jetty等服务器，无需外部部署
4. **生产就绪特性**：提供健康检查、指标监控、外部化配置等功能
5. **无代码生成**：不生成代码，也不需要XML配置

**在本系统中的应用：**
- 使用Spring Boot 3.2.1作为后端框架
- 集成Spring Security实现安全认证
- 使用Spring Boot Starter Web构建RESTful API
- 集成MyBatis进行数据持久化
- 使用Spring Boot Actuator进行系统监控

### 2.2 Vue.js前端框架

Vue.js是一套用于构建用户界面的渐进式JavaScript框架。与其它大型框架不同的是，Vue被设计为可以自底向上逐层应用。Vue的核心库只关注视图层，不仅易于上手，还便于与第三方库或既有项目整合。

**Vue.js的核心特性：**

1. **响应式数据绑定**：通过数据劫持结合发布者-订阅者模式实现双向数据绑定
2. **组件化开发**：将界面拆分为独立、可复用的组件，提高开发效率
3. **虚拟DOM**：通过虚拟DOM diff算法，最小化DOM操作，提升性能
4. **指令系统**：提供v-if、v-for、v-model等指令，简化DOM操作
5. **生态丰富**：拥有Vue Router、Vuex等官方库，以及丰富的第三方组件

**在本系统中的应用：**
- 使用Vue.js 2.6作为前端框架
- 集成Element UI组件库提供丰富的UI组件
- 使用Vue Router实现单页面应用的路由管理
- 使用Vuex进行状态管理
- 使用Axios进行HTTP请求处理

### 2.3 数据库技术

本系统采用MySQL 5.7作为关系型数据库管理系统，MySQL是目前最流行的开源关系型数据库之一，具有高性能、高可靠性、易于使用等特点。

**MySQL的技术特点：**

1. **ACID特性**：支持事务的原子性、一致性、隔离性、持久性
2. **多存储引擎**：支持InnoDB、MyISAM等多种存储引擎
3. **索引优化**：支持B-Tree、Hash、Full-text等多种索引类型
4. **复制机制**：支持主从复制、主主复制等高可用方案
5. **分区表**：支持水平分区，提高大表查询性能

**数据库设计原则：**
- 遵循第三范式，减少数据冗余
- 合理设计索引，提高查询效率
- 使用外键约束保证数据完整性
- 采用UTF-8编码支持中文字符
- 设计合理的表结构支持业务扩展

## 3 系统分析

### 3.1 可行性分析

**技术可行性：**
本系统采用的Spring Boot、Vue.js、MySQL等技术均为成熟的主流技术，拥有完善的文档和社区支持。开发团队具备相关技术栈的开发经验，技术实现难度适中，技术可行性高。

**经济可行性：**
系统开发主要使用开源技术，无需购买昂贵的商业软件许可证。服务器部署成本较低，可根据业务规模灵活调整配置。预期投入产出比良好，经济可行性强。

**操作可行性：**
系统界面设计简洁直观，操作流程符合用户习惯。提供完善的帮助文档和用户指南，用户学习成本低。系统兼容主流浏览器，无需安装额外软件，操作可行性高。

**法律可行性：**
系统设计遵循相关法律法规，用户数据处理符合《个人信息保护法》要求。房源信息发布遵循《房地产广告发布规定》，合同签署符合《电子签名法》规范，法律可行性充分。

### 3.2 功能性需求分析

根据对租房市场的调研和用户需求分析，系统需要实现以下核心功能：

**用户管理模块：**
- 用户注册与登录：支持手机号注册，短信验证码登录
- 身份认证：房东和租客身份认证，实名制管理
- 个人信息管理：头像上传、基本信息维护、安全设置
- 权限管理：基于角色的权限控制，管理员后台管理

**房源管理模块：**
- 房源发布：房东发布房源信息，包括图片、价格、位置等
- 房源搜索：多条件筛选，地图定位，智能推荐
- 房源详情：详细信息展示，虚拟看房，周边配套
- 房源管理：房东管理自己的房源，状态更新

**订单管理模块：**
- 租房申请：租客提交租房申请，填写个人信息
- 订单处理：房东审核申请，确认租赁关系
- 合同签署：在线合同生成与电子签名
- 订单跟踪：订单状态实时更新，流程透明化

**支付管理模块：**
- 押金支付：在线支付押金，多种支付方式
- 租金缴纳：定期租金缴纳，自动提醒功能
- 费用管理：水电费、物业费等其他费用管理
- 退款处理：押金退还，费用结算

**评价系统模块：**
- 双向评价：租客评价房源和房东，房东评价租客
- 多维度评分：位置、设施、服务、性价比等维度评分
- 评价展示：评价信息公开展示，提高透明度
- 信用体系：基于评价建立用户信用等级

**消息通知模块：**
- 站内消息：系统通知、订单状态变更通知
- 短信通知：重要事件短信提醒
- 邮件通知：合同、账单等文档邮件发送
- 实时聊天：租客与房东在线沟通

### 3.3 非功能性需求分析

**性能需求：**
- 响应时间：页面加载时间不超过3秒，API响应时间不超过1秒
- 并发处理：支持1000个并发用户同时在线
- 数据处理：支持10万级房源数据的快速检索
- 系统可用性：99.5%以上的系统可用率

**安全需求：**
- 数据加密：敏感数据传输采用HTTPS加密
- 身份认证：JWT令牌认证，防止非法访问
- 权限控制：基于角色的访问控制(RBAC)
- 数据备份：定期数据备份，防止数据丢失

**可靠性需求：**
- 故障恢复：系统故障后能够快速恢复
- 数据一致性：保证数据的完整性和一致性
- 异常处理：完善的异常处理机制
- 日志记录：详细的操作日志记录

**可扩展性需求：**
- 模块化设计：系统采用模块化设计，便于功能扩展
- 接口标准化：RESTful API设计，便于第三方集成
- 数据库扩展：支持数据库分库分表扩展
- 服务器扩展：支持负载均衡和集群部署

**易用性需求：**
- 界面友好：简洁直观的用户界面设计
- 操作简单：符合用户习惯的操作流程
- 响应式设计：支持PC端和移动端访问
- 多浏览器兼容：兼容主流浏览器

## 4 系统设计

### 4.1 系统功能设计

基于需求分析，系统整体功能架构如下：

**系统功能层次结构：**

1. **表现层(Presentation Layer)**
   - 用户界面：租客端、房东端、管理员端
   - 移动端适配：响应式设计，支持移动设备访问
   - 交互设计：友好的用户体验，直观的操作流程

2. **业务逻辑层(Business Logic Layer)**
   - 用户服务：注册登录、身份认证、权限管理
   - 房源服务：发布管理、搜索推荐、信息维护
   - 订单服务：申请处理、状态跟踪、流程管理
   - 支付服务：在线支付、费用管理、账单生成
   - 评价服务：双向评价、信用计算、数据统计
   - 消息服务：通知推送、在线聊天、消息管理

3. **数据访问层(Data Access Layer)**
   - 数据持久化：MyBatis ORM框架
   - 数据库连接池：Druid连接池管理
   - 事务管理：Spring事务管理
   - 缓存机制：Redis缓存热点数据

4. **基础设施层(Infrastructure Layer)**
   - 安全框架：Spring Security + JWT
   - 文件存储：本地文件存储/云存储
   - 日志管理：Logback日志框架
   - 监控告警：Spring Boot Actuator

**核心业务流程设计：**

1. **用户注册流程**
   - 手机号验证 → 短信验证码 → 设置密码 → 完善信息 → 注册成功

2. **房源发布流程**
   - 身份验证 → 填写房源信息 → 上传图片 → 设置价格 → 发布审核 → 上线展示

3. **租房申请流程**
   - 浏览房源 → 提交申请 → 房东审核 → 签署合同 → 支付押金 → 确认入住

4. **支付结算流程**
   - 生成账单 → 选择支付方式 → 在线支付 → 支付确认 → 更新状态 → 生成凭证

### 4.2 系统框架设计

系统采用前后端分离的微服务架构，具体设计如下：

**整体架构图：**

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用层     │    │   API网关层     │    │   后端服务层     │
│                │    │                │    │                │
│  Vue.js + UI   │◄──►│  Spring Boot   │◄──►│  业务服务集群   │
│  Element UI    │    │  Gateway       │    │  微服务架构     │
│  Vue Router    │    │  负载均衡       │    │  RESTful API   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                      │
                       ┌─────────────────┐           │
                       │   数据存储层     │◄──────────┘
                       │                │
                       │  MySQL 数据库  │
                       │  Redis 缓存    │
                       │  文件存储      │
                       └─────────────────┘
```

**前端架构设计：**

1. **组件化架构**
   - 页面组件：完整的页面级组件
   - 业务组件：可复用的业务逻辑组件
   - 基础组件：通用的UI组件

2. **状态管理**
   - Vuex Store：全局状态管理
   - 模块化Store：按业务模块划分状态
   - 持久化存储：关键状态本地存储

3. **路由设计**
   - 嵌套路由：支持多级页面嵌套
   - 路由守卫：权限控制和登录验证
   - 懒加载：按需加载页面组件

**后端架构设计：**

1. **分层架构**
   - Controller层：处理HTTP请求，参数验证
   - Service层：业务逻辑处理，事务管理
   - DAO层：数据访问，SQL操作
   - Entity层：数据实体，对象映射

2. **安全架构**
   - JWT认证：无状态的用户认证
   - RBAC权限：基于角色的访问控制
   - 数据加密：敏感数据加密存储
   - 接口防护：防止恶意请求和攻击

3. **服务架构**
   - RESTful API：标准化的接口设计
   - 异常处理：统一的异常处理机制
   - 日志记录：完整的操作日志
   - 监控告警：系统健康状态监控

### 4.3 概念模型设计

系统的核心实体及其关系如下：

**主要实体：**

1. **用户(User)**
   - 属性：用户ID、用户名、密码、手机号、邮箱、用户类型、创建时间
   - 关系：一个用户可以有多个用户详情信息

2. **用户详情(UserList)**
   - 属性：详情ID、姓名、身份证号、手机号、用户ID
   - 关系：属于一个用户

3. **房源(House)**
   - 属性：房源ID、地址、价格、状态、详情、图片URL、房东ID、区域、房间数、面积、装修情况
   - 关系：属于一个房东，可以有多个订单

4. **订单(Order)**
   - 属性：订单ID、订单号、房源ID、租客ID、房东ID、开始时间、结束时间、状态、总金额
   - 关系：关联一个房源和一个租客，可以有多个支付记录和一个评价

5. **支付记录(Payment)**
   - 属性：支付ID、订单ID、支付类型、金额、支付方式、支付状态、支付时间
   - 关系：属于一个订单

6. **评价(Review)**
   - 属性：评价ID、订单ID、用户ID、房源ID、评分、内容、标签、图片、回复
   - 关系：属于一个订单

7. **合同(Contract)**
   - 属性：合同ID、订单ID、合同内容、签署状态、创建时间
   - 关系：属于一个订单

8. **消息通知(Notification)**
   - 属性：通知ID、用户ID、标题、内容、类型、状态、创建时间
   - 关系：属于一个用户

**实体关系图(ERD)：**

```
User (1) ──── (1) UserList
  │
  │ (1)
  │
  ▼ (n)
House ──── (1:n) ──── Order ──── (1:1) ──── Contract
  │                     │
  │                     │ (1:n)
  │                     ▼
  │                  Payment
  │                     │
  │                     │ (1:1)
  │                     ▼
  └─────────────────► Review

User (1) ──── (n) Notification
```

### 4.4 逻辑结构表设计

基于概念模型，设计具体的数据库表结构：

**1. 用户表(user)**
```sql
CREATE TABLE `user` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(255) NOT NULL COMMENT '用户名',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `type` int(11) DEFAULT 2 COMMENT '用户类型：1-管理员，2-普通用户',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户表';
```

**2. 用户详情表(userlist)**
```sql
CREATE TABLE `userlist` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '详情ID',
  `name` varchar(255) NOT NULL COMMENT '真实姓名',
  `idcard` varchar(18) NOT NULL COMMENT '身份证号',
  `phone` varchar(11) NOT NULL COMMENT '手机号',
  `userid` int(11) NOT NULL COMMENT '用户ID',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_idcard` (`idcard`),
  UNIQUE KEY `uk_phone` (`phone`),
  KEY `fk_userlist_userid` (`userid`),
  CONSTRAINT `fk_userlist_userid` FOREIGN KEY (`userid`) REFERENCES `user` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户详情表';
```

**3. 房源表(house)**
```sql
CREATE TABLE `house` (
  `house_id` int(11) NOT NULL AUTO_INCREMENT COMMENT '房源ID',
  `address` varchar(255) NOT NULL COMMENT '房源地址',
  `price` decimal(10,2) NOT NULL COMMENT '租金价格',
  `status` varchar(50) DEFAULT '可租' COMMENT '房源状态：可租、已租、下架',
  `detail` text COMMENT '房源详情描述',
  `image_url` text COMMENT '房源图片URL，多个用逗号分隔',
  `owner_id` int(11) NOT NULL COMMENT '房东ID',
  `owner_name` varchar(64) NOT NULL COMMENT '房东姓名',
  `area` varchar(100) NOT NULL COMMENT '所在区域',
  `room_num` int(11) NOT NULL COMMENT '房间数量',
  `house_area` decimal(10,2) NOT NULL COMMENT '房屋面积',
  `decoration` varchar(50) NOT NULL COMMENT '装修情况',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '发布时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`house_id`),
  KEY `idx_owner_id` (`owner_id`),
  KEY `idx_status` (`status`),
  KEY `idx_area` (`area`),
  KEY `idx_price` (`price`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='房源信息表';
```

**4. 订单表(tb_order)**
```sql
CREATE TABLE `tb_order` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单编号',
  `house_id` int(11) NOT NULL COMMENT '房源ID',
  `tenant_id` int(11) NOT NULL COMMENT '租客ID',
  `owner_id` int(11) NOT NULL COMMENT '房东ID',
  `start_date` date NOT NULL COMMENT '租赁开始日期',
  `end_date` date NOT NULL COMMENT '租赁结束日期',
  `monthly_rent` decimal(10,2) NOT NULL COMMENT '月租金',
  `deposit` decimal(10,2) NOT NULL COMMENT '押金',
  `total_amount` decimal(10,2) NOT NULL COMMENT '总金额',
  `status` varchar(20) DEFAULT 'PENDING' COMMENT '订单状态',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_house_id` (`house_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_owner_id` (`owner_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='订单表';
```

**5. 支付记录表(tb_payment)**
```sql
CREATE TABLE `tb_payment` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '支付ID',
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `payment_type` varchar(20) NOT NULL COMMENT '支付类型：DEPOSIT-押金，RENT-租金',
  `amount` decimal(10,2) NOT NULL COMMENT '支付金额',
  `payment_method` varchar(20) NOT NULL COMMENT '支付方式：ALIPAY-支付宝，WECHAT-微信',
  `payment_status` varchar(20) DEFAULT 'PENDING' COMMENT '支付状态',
  `transaction_id` varchar(64) DEFAULT NULL COMMENT '第三方交易号',
  `payment_time` datetime DEFAULT NULL COMMENT '支付时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_payment_status` (`payment_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='支付记录表';
```

**6. 评价表(tb_review)**
```sql
CREATE TABLE `tb_review` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '评价ID',
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单编号',
  `house_id` int(11) NOT NULL COMMENT '房源ID',
  `owner_id` int(11) NOT NULL COMMENT '房东ID',
  `user_id` int(11) NOT NULL COMMENT '评价用户ID',
  `user_name` varchar(64) NOT NULL COMMENT '评价用户名',
  `user_avatar` varchar(255) DEFAULT NULL COMMENT '用户头像',
  `rating` decimal(3,2) NOT NULL COMMENT '总体评分(1-5分)',
  `location_rating` decimal(3,2) DEFAULT NULL COMMENT '位置便利性评分',
  `facility_rating` decimal(3,2) DEFAULT NULL COMMENT '房屋设施评分',
  `service_rating` decimal(3,2) DEFAULT NULL COMMENT '房东服务评分',
  `value_rating` decimal(3,2) DEFAULT NULL COMMENT '性价比评分',
  `environment_rating` decimal(3,2) DEFAULT NULL COMMENT '周边环境评分',
  `cleanliness_rating` decimal(3,2) DEFAULT NULL COMMENT '卫生情况评分',
  `average_rating` decimal(3,2) DEFAULT NULL COMMENT '平均评分',
  `content` text COMMENT '评价内容',
  `tags` varchar(500) DEFAULT NULL COMMENT '评价标签，逗号分隔',
  `images` text DEFAULT NULL COMMENT '评价图片，逗号分隔的URL',
  `anonymous` tinyint(1) DEFAULT 0 COMMENT '是否匿名评价',
  `reply` text DEFAULT NULL COMMENT '房东回复',
  `reply_time` datetime DEFAULT NULL COMMENT '回复时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_id` (`order_id`),
  KEY `idx_house_id` (`house_id`),
  KEY `idx_owner_id` (`owner_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='评价表';
```

**7. 合同表(tb_contract)**
```sql
CREATE TABLE `tb_contract` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '合同ID',
  `order_id` int(11) NOT NULL COMMENT '订单ID',
  `contract_no` varchar(32) NOT NULL COMMENT '合同编号',
  `content` longtext NOT NULL COMMENT '合同内容',
  `status` varchar(20) DEFAULT 'DRAFT' COMMENT '合同状态：DRAFT-草稿，SIGNED-已签署',
  `tenant_signed` tinyint(1) DEFAULT 0 COMMENT '租客是否签署',
  `owner_signed` tinyint(1) DEFAULT 0 COMMENT '房东是否签署',
  `tenant_sign_time` datetime DEFAULT NULL COMMENT '租客签署时间',
  `owner_sign_time` datetime DEFAULT NULL COMMENT '房东签署时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_id` (`order_id`),
  UNIQUE KEY `uk_contract_no` (`contract_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='合同表';
```

**8. 通知表(tb_notification)**
```sql
CREATE TABLE `tb_notification` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `title` varchar(255) NOT NULL COMMENT '通知标题',
  `content` text NOT NULL COMMENT '通知内容',
  `type` varchar(20) NOT NULL COMMENT '通知类型：SYSTEM-系统，ORDER-订单，PAYMENT-支付',
  `status` varchar(20) DEFAULT 'UNREAD' COMMENT '状态：UNREAD-未读，READ-已读',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='通知表';
```

**索引设计说明：**
1. 主键索引：每个表都有自增主键，提供唯一性约束
2. 唯一索引：用户名、身份证号、订单号等需要唯一性的字段
3. 普通索引：经常用于查询条件的字段，如状态、时间、外键等
4. 复合索引：多字段组合查询的场景，如(user_id, status)

**外键约束设计：**
- 保证数据完整性和一致性
- 防止无效的关联数据插入
- 支持级联更新和删除操作

## 5 系统实现

### 5.1 登录功能实现

登录功能是系统的基础功能，采用JWT(JSON Web Token)实现无状态的用户认证。

**后端实现：**

1. **用户实体类设计**

```java
@Data
@AllArgsConstructor
@NoArgsConstructor
public class User {
    private Integer id;
    private String username;
    private String password;
    private Integer type; // 1-管理员，2-普通用户
    private Date createTime;
    private Date updateTime;
}

@Data
@AllArgsConstructor
@NoArgsConstructor
public class LoginUser {
    private String username;
    private String password;
    private String token;
    private Integer type;
    private Integer userId;
}
```

2. **JWT工具类实现**

```java
@Component
public class JwtUtil {
    private static final String SECRET = "house_rental_system_secret_key";
    private static final long EXPIRATION = 86400000; // 24小时

    public String generateToken(String username, Integer userId, Integer type) {
        return Jwts.builder()
                .setSubject(username)
                .claim("userId", userId)
                .claim("type", type)
                .setIssuedAt(new Date())
                .setExpiration(new Date(System.currentTimeMillis() + EXPIRATION))
                .signWith(SignatureAlgorithm.HS512, SECRET)
                .compact();
    }

    public Claims getClaimsFromToken(String token) {
        return Jwts.parser()
                .setSigningKey(SECRET)
                .parseClaimsJws(token)
                .getBody();
    }

    public boolean isTokenExpired(String token) {
        Date expiration = getClaimsFromToken(token).getExpiration();
        return expiration.before(new Date());
    }
}
```

3. **用户服务层实现**

```java
@Service
@Transactional
public class UserServiceImpl implements UserService {

    @Autowired
    private UserDao userDao;

    @Autowired
    private JwtUtil jwtUtil;

    @Override
    public Result<LoginUser> login(String username, String password) {
        try {
            // 查询用户
            User user = userDao.selectByUsername(username);
            if (user == null) {
                return new Result<>(false, StatusCode.ERROR, "用户不存在");
            }

            // 验证密码
            if (!password.equals(user.getPassword())) {
                return new Result<>(false, StatusCode.ERROR, "密码错误");
            }

            // 生成JWT令牌
            String token = jwtUtil.generateToken(username, user.getId(), user.getType());

            LoginUser loginUser = new LoginUser();
            loginUser.setUsername(username);
            loginUser.setToken(token);
            loginUser.setType(user.getType());
            loginUser.setUserId(user.getId());

            return new Result<>(true, StatusCode.OK, "登录成功", loginUser);
        } catch (Exception e) {
            return new Result<>(false, StatusCode.ERROR, "登录失败：" + e.getMessage());
        }
    }
}
```

4. **登录控制器实现**

```java
@RestController
@RequestMapping("/api/user")
@CrossOrigin
public class UserController {

    @Autowired
    private UserService userService;

    @PostMapping("/login")
    public Result<LoginUser> login(@RequestBody User user) {
        return userService.login(user.getUsername(), user.getPassword());
    }

    @PostMapping("/logout")
    public Result<String> logout(HttpServletRequest request) {
        // JWT是无状态的，前端删除token即可
        return new Result<>(true, StatusCode.OK, "退出成功");
    }
}
```

**前端实现：**

1. **登录页面组件**

```vue
<template>
  <div class="login-container">
    <el-card class="login-card">
      <h2 class="login-title">智能租房管理系统</h2>
      <el-form :model="loginForm" :rules="loginRules" ref="loginForm">
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            prefix-icon="el-icon-user">
          </el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            prefix-icon="el-icon-lock">
          </el-input>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            @click="handleLogin"
            :loading="loading"
            class="login-btn">
            登录
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { login } from '@/api/user'
import { setToken } from '@/utils/auth'

export default {
  name: 'Login',
  data() {
    return {
      loginForm: {
        username: '',
        password: ''
      },
      loginRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' }
        ]
      },
      loading: false
    }
  },
  methods: {
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          this.loading = true
          login(this.loginForm).then(response => {
            if (response.flag) {
              // 保存token和用户信息
              setToken(response.data.token)
              this.$store.dispatch('user/setUserInfo', response.data)

              // 根据用户类型跳转不同页面
              if (response.data.type === 1) {
                this.$router.push('/admin')
              } else {
                this.$router.push('/home')
              }

              this.$message.success('登录成功')
            } else {
              this.$message.error(response.message)
            }
          }).catch(error => {
            this.$message.error('登录失败')
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>
```

2. **认证工具类**

```javascript
// utils/auth.js
import Cookies from 'js-cookie'

const TokenKey = 'house_token'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(TokenKey, token)
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}
```

3. **HTTP请求拦截器**

```javascript
// utils/request.js
import axios from 'axios'
import { getToken, removeToken } from '@/utils/auth'
import { Message } from 'element-ui'
import router from '@/router'

const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API,
  timeout: 5000
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    const token = getToken()
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    if (error.response && error.response.status === 401) {
      Message.error('登录已过期，请重新登录')
      removeToken()
      router.push('/login')
    }
    return Promise.reject(error)
  }
)

export default service
```

### 5.2 注册功能实现

注册功能包括用户基本信息注册和详细信息完善两个步骤。

**后端实现：**

1. **注册服务实现**

```java
@Service
@Transactional
public class UserServiceImpl implements UserService {

    @Override
    public Result<String> register(User user, UserList userList) {
        try {
            // 检查用户名是否已存在
            User existUser = userDao.selectByUsername(user.getUsername());
            if (existUser != null) {
                return new Result<>(false, StatusCode.ERROR, "用户名已存在");
            }

            // 检查手机号是否已注册
            UserList existUserList = userListDao.selectByPhone(userList.getPhone());
            if (existUserList != null) {
                return new Result<>(false, StatusCode.ERROR, "手机号已注册");
            }

            // 检查身份证号是否已注册
            UserList existIdCard = userListDao.selectByIdCard(userList.getIdcard());
            if (existIdCard != null) {
                return new Result<>(false, StatusCode.ERROR, "身份证号已注册");
            }

            // 插入用户基本信息
            user.setType(2); // 默认为普通用户
            user.setCreateTime(new Date());
            userDao.insertUser(user);

            // 插入用户详细信息
            userList.setUserid(user.getId());
            userList.setCreateTime(new Date());
            userListDao.insertUserList(userList);

            return new Result<>(true, StatusCode.OK, "注册成功");
        } catch (Exception e) {
            throw new UserOperationException("注册失败：" + e.getMessage());
        }
    }
}
```

2. **注册控制器**

```java
@RestController
@RequestMapping("/api/user")
public class UserController {

    @PostMapping("/register")
    public Result<String> register(@RequestBody Map<String, Object> registerData) {
        try {
            // 解析注册数据
            User user = new User();
            user.setUsername((String) registerData.get("username"));
            user.setPassword((String) registerData.get("password"));

            UserList userList = new UserList();
            userList.setName((String) registerData.get("name"));
            userList.setIdcard((String) registerData.get("idcard"));
            userList.setPhone((String) registerData.get("phone"));

            return userService.register(user, userList);
        } catch (Exception e) {
            return new Result<>(false, StatusCode.ERROR, "注册失败：" + e.getMessage());
        }
    }
}
```

**前端实现：**

```vue
<template>
  <div class="register-container">
    <el-card class="register-card">
      <h2 class="register-title">用户注册</h2>
      <el-form :model="registerForm" :rules="registerRules" ref="registerForm">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="registerForm.username" placeholder="请输入用户名"></el-input>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="registerForm.password" type="password" placeholder="请输入密码"></el-input>
        </el-form-item>
        <el-form-item label="确认密码" prop="confirmPassword">
          <el-input v-model="registerForm.confirmPassword" type="password" placeholder="请确认密码"></el-input>
        </el-form-item>
        <el-form-item label="真实姓名" prop="name">
          <el-input v-model="registerForm.name" placeholder="请输入真实姓名"></el-input>
        </el-form-item>
        <el-form-item label="身份证号" prop="idcard">
          <el-input v-model="registerForm.idcard" placeholder="请输入身份证号"></el-input>
        </el-form-item>
        <el-form-item label="手机号" prop="phone">
          <el-input v-model="registerForm.phone" placeholder="请输入手机号"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleRegister" :loading="loading">注册</el-button>
          <el-button @click="$router.push('/login')">返回登录</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import { register } from '@/api/user'

export default {
  name: 'Register',
  data() {
    const validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.registerForm.password) {
        callback(new Error('两次输入密码不一致!'))
      } else {
        callback()
      }
    }

    return {
      registerForm: {
        username: '',
        password: '',
        confirmPassword: '',
        name: '',
        idcard: '',
        phone: ''
      },
      registerRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, validator: validatePass, trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入真实姓名', trigger: 'blur' }
        ],
        idcard: [
          { required: true, message: '请输入身份证号', trigger: 'blur' },
          { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, message: '身份证号格式不正确', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
        ]
      },
      loading: false
    }
  },
  methods: {
    handleRegister() {
      this.$refs.registerForm.validate(valid => {
        if (valid) {
          this.loading = true
          register(this.registerForm).then(response => {
            if (response.flag) {
              this.$message.success('注册成功，请登录')
              this.$router.push('/login')
            } else {
              this.$message.error(response.message)
            }
          }).catch(error => {
            this.$message.error('注册失败')
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>
```
