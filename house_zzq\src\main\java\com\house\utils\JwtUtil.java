package com.house.utils;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.JwtBuilder;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Base64;

/**
 * JWT工具类 - 升级到JJWT 0.11.x
 */
@Component
@ConfigurationProperties("jwt")
public class JwtUtil {

    // 使用固定的密钥字符串，确保所有实例使用相同的密钥
    private static final String FIXED_KEY = "house_rental_platform_jwt_secret_key_for_secure_authentication_2025";
    
    // 使用Base64编码的安全密钥字符串，这是一个至少256位的密钥
    private String encodedKey;
    
    // 密钥对象，懒加载方式创建
    private SecretKey secretKey;

    // 过期时间的时长，默认2小时，从配置文件中读取
    private long ttl = 7200000; // 2小时 = 2 * 60分钟 * 60秒 * 1000毫秒 = 7200000毫秒
    
    // 记住我功能的过期时间，默认7天，从配置文件中读取
    private long rememberMeTtl = 604800000;

    public JwtUtil() {
        // 使用固定密钥，而不是每次生成新密钥
        this.secretKey = Keys.hmacShaKeyFor(FIXED_KEY.getBytes(StandardCharsets.UTF_8));
        this.encodedKey = Base64.getEncoder().encodeToString(this.secretKey.getEncoded());
    }

    public String getEncodedKey() {
        return encodedKey;
    }

    public void setTtl(long ttl) {
        this.ttl = ttl;
    }

    public long getTtl() {
        return ttl;
    }
    
    public void setRememberMeTtl(long rememberMeTtl) {
        this.rememberMeTtl = rememberMeTtl;
    }
    
    public long getRememberMeTtl() {
        return rememberMeTtl;
    }

    /**
     * 获取用于签名的密钥
     * @return SecretKey
     */
    private SecretKey getSigningKey() {
        return this.secretKey;
    }

    /**
     * 生成JWT
     *
     * @param id 用户ID
     * @param subject 用户名
     * @param roles 角色
     * @return JWT令牌
     */
    public String createJWT(String id, String subject, String roles) {
        return createJWT(id, subject, roles, false, false);
    }
    
    /**
     * 生成JWT，支持记住我功能
     *
     * @param id 用户ID
     * @param subject 用户名
     * @param roles 角色
     * @param rememberMe 是否记住我
     * @return JWT令牌
     */
    public String createJWT(String id, String subject, String roles, boolean rememberMe) {
        return createJWT(id, subject, roles, rememberMe, false);
    }
    
    /**
     * 生成JWT，支持记住我功能和前后台区分
     *
     * @param id 用户ID
     * @param subject 用户名
     * @param roles 角色
     * @param rememberMe 是否记住我
     * @param isBackstage 是否为后台token
     * @return JWT令牌
     */
    public String createJWT(String id, String subject, String roles, boolean rememberMe, boolean isBackstage) {
        //当前时间
        long nowMillis = System.currentTimeMillis();
        Date now = new Date(nowMillis);
        //构建JwtBuilder
        JwtBuilder builder = Jwts.builder()
                .setId(id)
                .setSubject(subject)
                .setIssuedAt(now)
                .signWith(getSigningKey(), SignatureAlgorithm.HS256)
                .claim("roles", roles)
                .claim("isBackstage", isBackstage); // 添加标识是否为后台token的声明
        //有过期时间
        long expiration = rememberMe ? rememberMeTtl : ttl;
        if (expiration > 0) {
            builder.setExpiration(new Date(nowMillis + expiration));
        }
        return builder.compact();
    }

    /**
     * 解析JWT
     * @param jwtStr JWT字符串
     * @return Claims
     */
    public Claims parseJWT(String jwtStr) {
        return Jwts.parserBuilder()
                .setSigningKey(getSigningKey())
                .build()
                .parseClaimsJws(jwtStr)
                .getBody();
    }
    
    /**
     * 检查token是否为后台token
     * @param jwtStr JWT字符串
     * @return 是否为后台token
     */
    public boolean isBackstageToken(String jwtStr) {
        try {
            Claims claims = parseJWT(jwtStr);
            return claims.get("isBackstage") != null && Boolean.TRUE.equals(claims.get("isBackstage"));
        } catch (Exception e) {
            return false;
        }
    }
}
