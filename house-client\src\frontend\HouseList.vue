<template>
  <div class="house-list-container page-container has-fixed-header">
    <!-- 使用Header组件 -->
    <frontend-header :active-index="activeIndex"></frontend-header>

    <!-- 搜索和筛选区域 -->
    <div class="filter-container">
      <div class="search-bar">
        <el-input placeholder="请输入小区名、地址等关键词" v-model="filters.keyword" class="input-with-select">
          <template #append>
            <el-button type="primary" @click="searchHouses" class="search-button right-side-btn">搜索</el-button>
          </template>
        </el-input>
      </div>
      
      <div class="filter-options">
        <div class="filter-group">
          <div class="filter-label">价格区间：</div>
          <div class="filter-values">
            <el-radio-group v-model="filters.priceRange" @change="filterHouses">
              <el-radio-button label="">全部</el-radio-button>
              <el-radio-button label="0-1000">1000元以下</el-radio-button>
              <el-radio-button label="1000-2000">1000-2000元</el-radio-button>
              <el-radio-button label="2000-3000">2000-3000元</el-radio-button>
              <el-radio-button label="3000-5000">3000-5000元</el-radio-button>
              <el-radio-button label="5000-999999">5000元以上</el-radio-button>
            </el-radio-group>
          </div>
        </div>
        
        <div class="filter-group">
          <div class="filter-label">房源状态：</div>
          <div class="filter-values">
            <el-radio-group v-model="filters.status" @change="filterHouses">
              <el-radio-button label="">全部</el-radio-button>
              <el-radio-button label="可租">可租</el-radio-button>
              <el-radio-button label="已租">已租</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </div>
    </div>

    <!-- 房源列表 -->
    <div class="houses-container">
      <div class="houses-wrapper">
        <el-row :gutter="20">
          <el-col :xs="24" :sm="12" :md="8" :lg="6" v-for="house in displayHouses" :key="house.houseId">
            <div @click="viewHouseDetail(house.houseId)" style="cursor: pointer;">
              <el-card class="house-card" shadow="hover">
                <div class="house-img" :style="{ backgroundImage: `url(${house.imageUrl || require('../assets/showcase.jpg')})` }"></div>
                <div class="house-info">
                  <h3 class="house-title">{{ house.address }}</h3>
                  <div class="house-tags">
                    <el-tag size="small" effect="plain" :type="house.status === '可租' ? 'success' : 'info'">
                      {{ house.status }}
                    </el-tag>
                  </div>
                  <div class="house-price">{{ house.price }} 元/月</div>
                  <div class="house-desc">{{ house.detail }}</div>
                </div>
              </el-card>
            </div>
          </el-col>
        </el-row>
      </div>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="prev, pager, next"
          :total="totalHouses"
          :page-size="pageSize"
          :current-page="currentPage"
          @current-change="handlePageChange">
        </el-pagination>
      </div>
    </div>

    <!-- 使用Footer组件 -->
    <frontend-footer></frontend-footer>
  </div>
</template>

<script>
// 导入组件
import FrontendHeader from './components/Header.vue';
import FrontendFooter from './components/Footer.vue';

export default {
  name: "HouseList",
  // 注册组件
  components: {
    FrontendHeader,
    FrontendFooter
  },
  data() {
    return {
      activeIndex: '/frontend/houses',
      filters: {
        keyword: '',
        priceRange: '',
        status: ''
      },
      // 模拟房源数据
      allHouses: [
        {
          houseId: 1,
          address: '海淀区西二旗大街20号',
          price: 3500,
          status: '可租',
          detail: '2室1厅，70平米，精装修，家具家电齐全',
          imageUrl: ''
        },
        {
          houseId: 2,
          address: '朝阳区望京soho',
          price: 5000,
          status: '可租',
          detail: '1室1厅，50平米，高档公寓，地铁口',
          imageUrl: ''
        },
        {
          houseId: 3,
          address: '西城区德胜门外大街',
          price: 2800,
          status: '已租',
          detail: '2室1厅，60平米，普通装修，交通便利',
          imageUrl: ''
        },
        {
          houseId: 4,
          address: '丰台区丰台科技园',
          price: 3000,
          status: '可租',
          detail: '2室2厅，80平米，花园小区，环境优美',
          imageUrl: ''
        },
        {
          houseId: 5,
          address: '东城区东直门外大街',
          price: 4500,
          status: '可租',
          detail: '3室1厅，90平米，南北通透，拎包入住',
          imageUrl: ''
        },
        {
          houseId: 6,
          address: '昌平区回龙观东大街',
          price: 2500,
          status: '可租',
          detail: '2室1厅，65平米，中等装修，交通方便',
          imageUrl: ''
        },
        {
          houseId: 7,
          address: '海淀区中关村大街',
          price: 6000,
          status: '已租',
          detail: '2室2厅，85平米，豪华装修，科技园区',
          imageUrl: ''
        },
        {
          houseId: 8,
          address: '朝阳区建国路',
          price: 7000,
          status: '可租',
          detail: '3室2厅，120平米，高端小区，品质生活',
          imageUrl: ''
        },
        {
          houseId: 9,
          address: '昌平区北七家',
          price: 2200,
          status: '可租',
          detail: '1室1厅，45平米，普通装修，经济实惠',
          imageUrl: ''
        },
        {
          houseId: 10,
          address: '大兴区亦庄开发区',
          price: 3800,
          status: '可租',
          detail: '2室1厅，75平米，精装修，配套齐全',
          imageUrl: ''
        }
      ],
      currentPage: 1,
      pageSize: 8,
      filteredHouses: [] // 过滤后的房源
    };
  },
  computed: {
    // 分页显示的房源
    displayHouses() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.filteredHouses.slice(start, end);
    },
    // 总房源数
    totalHouses() {
      return this.filteredHouses.length;
    }
  },
  created() {
    // 初始化过滤后的房源
    this.filteredHouses = [...this.allHouses];
    
    // 如果有URL参数，应用过滤条件
    const query = this.$route.query;
    if (query.keyword) {
      this.filters.keyword = query.keyword;
    }
    if (query.price) {
      this.filters.priceRange = query.price;
    }
    if (Object.keys(query).length > 0) {
      this.filterHouses();
    }
  },
  methods: {
    // 搜索房源
    searchHouses() {
      this.filterHouses();
    },
    
    // 过滤房源
    filterHouses() {
      let filtered = [...this.allHouses];
      
      // 关键词过滤
      if (this.filters.keyword) {
        const keyword = this.filters.keyword.toLowerCase();
        filtered = filtered.filter(house => 
          house.address.toLowerCase().includes(keyword) || 
          house.detail.toLowerCase().includes(keyword)
        );
      }
      
      // 价格范围过滤
      if (this.filters.priceRange) {
        const [min, max] = this.filters.priceRange.split('-').map(Number);
        filtered = filtered.filter(house => 
          house.price >= min && (max ? house.price <= max : true)
        );
      }
      
      // 状态过滤
      if (this.filters.status) {
        filtered = filtered.filter(house => house.status === this.filters.status);
      }
      
      this.filteredHouses = filtered;
      this.currentPage = 1; // 重置为第一页
    },
    
    // 页码变化
    handlePageChange(page) {
      this.currentPage = page;
    },
    
    // 查看详情
    viewHouseDetail(houseId) {
      console.log('HouseList.vue - 查看房源详情，房源ID：', houseId);
      // 使用命名路由跳转到详情页
      try {
        this.$router.push({
          name: 'FrontendHouseDetail',
          params: { id: houseId }
        });
      } catch (err) {
        console.error('路由跳转失败:', err);
      }
    },
    
    // 跳转登录
    goToLogin() {
      this.$router.push('/login');
    }
  }
};
</script>

<style scoped>
.house-list-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 搜索和筛选区域样式 */
.filter-container {
  background-color: #f0f8ff;
  padding: 30px 20px;
  margin-bottom: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-bottom: 1px solid #e6f0fa;
}

.search-bar {
  margin-bottom: 25px;
  padding-right: 10px; /* 为滚动条留出空间 */
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.search-bar .el-input__inner {
  height: 50px;
  border-radius: 4px;
  font-size: 16px;
  border: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.filter-options {
  max-width: 1000px;
  margin: 0 auto;
}

.filter-group {
  margin-bottom: 20px;
}

.filter-label {
  color: #333333;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 10px;
}

.filter-values {
  display: flex;
  flex-wrap: wrap;
}

/* 自定义 Element UI 样式 */
.el-radio-button__orig-radio:checked + .el-radio-button__inner {
  background-color: #ff6b6b;
  border-color: #ff6b6b;
  box-shadow: -1px 0 0 0 #ff6b6b;
}

.el-radio-button__inner {
  border-color: #e0e0e0;
  background-color: #ffffff;
  color: #333;
  padding: 10px 20px;
}

.el-radio-button__inner:hover {
  color: #ff6b6b;
}

/* 房源列表样式 */
.houses-container {
  padding: 0 20px 30px;
}

.houses-wrapper {
  margin-bottom: 30px;
}

.house-card {
  margin-bottom: 25px;
  cursor: pointer;
  transition: all 0.3s;
  border-radius: 8px;
  overflow: hidden;
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.house-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.house-img {
  height: 180px;
  background-size: cover;
  background-position: center;
  border-radius: 8px 8px 0 0;
}

.house-info {
  padding: 15px;
}

.house-title {
  font-size: 18px;
  margin-bottom: 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  color: #333;
}

.house-price {
  color: #ff6b6b;
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 12px;
}

.house-desc {
  color: #666;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
}

/* 分页样式 */
.pagination-container {
  text-align: center;
  margin-top: 30px;
}
</style> 