package com.house.service.impl;

import com.house.mapper.NotificationMapper;
import com.house.pojo.Notification;
import com.house.service.NotificationService;
import com.house.websocket.MessageDTO;
import com.house.websocket.WebSocketServer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通知服务实现类
 */
@Service
public class NotificationServiceImpl implements NotificationService {

    @Autowired
    private NotificationMapper notificationMapper;

    @Autowired
    private WebSocketServer webSocketServer;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long sendNotification(String userId, String title, String content, String type) {
        return sendNotification(userId, title, content, type, null, null);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long sendNotification(String userId, String title, String content, String type, String linkId, String linkType) {
        // 创建通知
        Notification notification = new Notification();
        notification.setUserId(Integer.parseInt(userId));
        notification.setTitle(title);
        notification.setContent(content);
        notification.setType(type);
        notification.setLinkId(linkId);
        notification.setLinkType(linkType);
        notification.setRead(false);
        Date now = new Date();
        notification.setCreateTime(now);
        notification.setUpdateTime(now);

        // 保存通知到数据库
        notificationMapper.insert(notification);

        // 发送WebSocket消息
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setType("notification");
        messageDTO.setTitle(title);
        messageDTO.setContent(content);
        messageDTO.setLinkType(linkType);
        messageDTO.setLinkId(linkId);
        messageDTO.setTime(now.getTime());

        // 发送消息给指定用户（默认发送到前台系统）
        webSocketServer.sendMessage("frontend", userId, messageDTO);

        return notification.getId().longValue();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean sendNotificationToAll(String title, String content, String type) {
        try {
            // 发送WebSocket消息给所有用户
            MessageDTO messageDTO = new MessageDTO();
            messageDTO.setType(type);
            messageDTO.setTitle(title);
            messageDTO.setContent(content);
            messageDTO.setTime(System.currentTimeMillis());
            
            WebSocketServer.sendMessageToAll(messageDTO);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long sendOrderNotification(String userId, String title, String content, String orderId) {
        return sendNotification(userId, title, content, "order", orderId, "order");
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long sendContractNotification(String userId, String title, String content, String contractId) {
        return sendNotification(userId, title, content, "contract", contractId, "contract");
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long sendPaymentNotification(String userId, String title, String content, String paymentId) {
        return sendNotification(userId, title, content, "payment", paymentId, "payment");
    }

    public List<Notification> getUserNotifications(Integer userId) {
        return notificationMapper.selectByUserId(userId);
    }

    public List<Notification> getUserUnreadNotifications(Integer userId) {
        // 检查是否存在selectUnreadByUserId方法，如果不存在，可以使用条件查询
        try {
            return notificationMapper.selectUnreadByUserId(userId);
        } catch (Exception e) {
            // 备选方案：使用条件查询
            Map<String, Object> params = new HashMap<>();
            params.put("userId", userId);
            params.put("read", false);
            return notificationMapper.selectByCondition(params);
        }
    }

    public int getUserUnreadCount(Integer userId) {
        return notificationMapper.countUnreadByUserId(userId);
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean markAsRead(Integer notificationId) {
        // 使用标记消息为已读的方法
        Date now = new Date();
        return notificationMapper.markAsRead(notificationId, now) > 0;
    }
    
    @Transactional(rollbackFor = Exception.class)
    public boolean markAsUnread(Integer notificationId) {
        // 创建一个消息对象
        Notification notification = notificationMapper.selectById(notificationId);
        if (notification == null) {
            return false;
        }
        
        // 修改消息状态
        notification.setRead(false);
        notification.setReadTime(null);
        notification.setUpdateTime(new Date());
        
        // 更新消息
        return notificationMapper.update(notification) > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean markAllAsRead(Integer userId) {
        // 使用标记用户所有消息为已读的方法
        Date now = new Date();
        return notificationMapper.markAllAsRead(userId, now) > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean deleteNotification(Integer notificationId) {
        return notificationMapper.deleteById(notificationId) > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean deleteAllNotifications(Integer userId) {
        return notificationMapper.deleteByUserId(userId) > 0;
    }

    public Map<String, Object> getNotificationStatistics(Integer userId) {
        // 创建统计信息
        Map<String, Object> stats = new HashMap<>();
        
        // 获取未读消息数量
        int unreadCount = notificationMapper.countUnreadByUserId(userId);
        stats.put("unreadCount", unreadCount);
        
        // 获取总消息数量
        List<Notification> allNotifications = notificationMapper.selectByUserId(userId);
        stats.put("totalCount", allNotifications.size());
        
        return stats;
    }
} 