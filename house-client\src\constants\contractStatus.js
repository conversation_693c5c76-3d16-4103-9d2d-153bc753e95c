/**
 * 合同状态常量定义
 * 与后端保持完全一致
 */

// 合同状态枚举
export const CONTRACT_STATUS = {
  PENDING: 'pending',           // 待签署
  ACTIVE: 'active',            // 生效中
  EXPIRED: 'expired',          // 已过期
  CANCELLED: 'cancelled'       // 已取消
}

// 状态显示文本映射
export const CONTRACT_STATUS_TEXT = {
  [CONTRACT_STATUS.PENDING]: '待签署',
  [CONTRACT_STATUS.ACTIVE]: '已生效',
  [CONTRACT_STATUS.EXPIRED]: '已过期',
  [CONTRACT_STATUS.CANCELLED]: '已取消'
}

// 状态类型映射（用于Element UI的tag类型）
export const CONTRACT_STATUS_TYPE = {
  [CONTRACT_STATUS.PENDING]: 'warning',
  [CONTRACT_STATUS.ACTIVE]: 'success',
  [CONTRACT_STATUS.EXPIRED]: 'info',
  [CONTRACT_STATUS.CANCELLED]: 'danger'
}

// 状态描述映射
export const CONTRACT_STATUS_DESC = {
  [CONTRACT_STATUS.PENDING]: '合同待双方签署',
  [CONTRACT_STATUS.ACTIVE]: '合同已生效，正在执行中',
  [CONTRACT_STATUS.EXPIRED]: '合同已到期',
  [CONTRACT_STATUS.CANCELLED]: '合同已取消'
}

// 状态图标映射
export const CONTRACT_STATUS_ICON = {
  [CONTRACT_STATUS.PENDING]: 'el-icon-document',
  [CONTRACT_STATUS.ACTIVE]: 'el-icon-circle-check',
  [CONTRACT_STATUS.EXPIRED]: 'el-icon-time',
  [CONTRACT_STATUS.CANCELLED]: 'el-icon-circle-close'
}

// 可操作的状态转换
export const CONTRACT_STATUS_TRANSITIONS = {
  [CONTRACT_STATUS.PENDING]: [CONTRACT_STATUS.ACTIVE, CONTRACT_STATUS.CANCELLED],
  [CONTRACT_STATUS.ACTIVE]: [CONTRACT_STATUS.EXPIRED, CONTRACT_STATUS.CANCELLED],
  [CONTRACT_STATUS.EXPIRED]: [],
  [CONTRACT_STATUS.CANCELLED]: []
}

// 工具函数
export const ContractStatusUtils = {
  // 获取状态文本
  getStatusText(status) {
    return CONTRACT_STATUS_TEXT[status] || status
  },
  
  // 获取状态类型
  getStatusType(status) {
    return CONTRACT_STATUS_TYPE[status] || 'info'
  },
  
  // 获取状态描述
  getStatusDesc(status) {
    return CONTRACT_STATUS_DESC[status] || ''
  },
  
  // 获取状态图标
  getStatusIcon(status) {
    return CONTRACT_STATUS_ICON[status] || 'el-icon-info'
  },
  
  // 检查状态是否可以转换
  canTransitionTo(fromStatus, toStatus) {
    const allowedTransitions = CONTRACT_STATUS_TRANSITIONS[fromStatus] || []
    return allowedTransitions.includes(toStatus)
  },
  
  // 获取下一个可能的状态
  getNextStatuses(currentStatus) {
    return CONTRACT_STATUS_TRANSITIONS[currentStatus] || []
  },
  
  // 检查是否为终态
  isFinalStatus(status) {
    return status === CONTRACT_STATUS.EXPIRED || status === CONTRACT_STATUS.CANCELLED
  },
  
  // 检查是否可以签署
  canSign(status) {
    return status === CONTRACT_STATUS.PENDING
  },
  
  // 检查是否可以取消
  canCancel(status) {
    return status === CONTRACT_STATUS.PENDING || status === CONTRACT_STATUS.ACTIVE
  }
}
